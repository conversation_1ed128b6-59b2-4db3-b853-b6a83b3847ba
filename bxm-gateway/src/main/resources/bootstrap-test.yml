spring:
  cloud:
    nacos:
      discovery:
        # 服务注册地址
        server-addr: 120.27.156.42:18848
        namespace: test
        username: zbb
        password: cTcjZfNwAPRaPtMJFk2NPjB0cTkn3znS
      config:
        # 配置中心地址
        server-addr: 120.27.156.42:18848
        # 配置文件格式
        file-extension: yaml
        refresh-enabled: true
        namespace: test
        group: bxm-gateway
        name: bxm-gateway
        username: zbb
        password: cTcjZfNwAPRaPtMJFk2NPjB0cTkn3znS
        extension-configs: # 此处加载分离的扩展配置，例如抽取数据库、mybats配置等
          - data-id: redis.yaml
            group: bxm-common
            refresh: true

          - data-id: datasource.yaml
            group: bxm-common
            refresh: true
    sentinel:
      # 取消控制台懒加载
      eager: true
      transport:
        # 控制台地址
        dashboard: 121.41.41.248:8718
      # nacos配置持久化
      datasource:
        ds1:
          nacos:
            server-addr: 120.27.156.42:18848
            dataId: sentinel-bxm-gateway
            groupId: DEFAULT_GROUP
            data-type: json
            rule-type: gw-flow
            username: zbb
            password: cTcjZfNwAPRaPtMJFk2NPjB0cTkn3znS