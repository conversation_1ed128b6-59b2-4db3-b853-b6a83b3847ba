DROP TABLE IF EXISTS c_customer_service_bank_account;
create table c_customer_service_bank_account
(
    id                  bigint auto_increment comment '主键ID'
        primary key,
    customer_service_id bigint                                not null comment '客户服务ID',
    bank_name           varchar(255)                          not null comment '银行名称',
    bank_account_number varchar(100)                          not null comment '银行账号',
    password            varchar(100)                          null comment '密码',
    phone_number        varchar(15)                           null comment '手机号',
    account_open_date   date                                  null comment '开户时间',
    account_close_date  date                                  null comment '销户时间',
    receipt_status      tinyint                               null comment '回单卡状态, 1-已托管, 2-未托管',
    bank_direct         tinyint                               null comment '银企直连, 1-已开通, 2-未开通',
    deposit_name        varchar(50)                           null comment '开户行名称',
    remarks             varchar(255)                          null comment '备注说明',
    create_by           varchar(64) default ''                null comment '创建者',
    create_time         datetime    default CURRENT_TIMESTAMP null comment '创建时间',
    update_by           varchar(64) default ''                null comment '更新者',
    update_time         datetime    default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='服务银行账号表';

DROP TABLE IF EXISTS c_new_customer_info;
CREATE TABLE c_new_customer_info (
     id BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
     customer_name VARCHAR(255) NOT NULL COMMENT '客户企业名',
     credit_code VARCHAR(100) NOT NULL COMMENT '信用代码',
     tax_number VARCHAR(100) NOT NULL COMMENT '税号',
     registration_date VARCHAR(20) NOT NULL COMMENT '注册时间',
     registration_region VARCHAR(255) NOT NULL COMMENT '注册区域',
     industry VARCHAR(255) NOT NULL COMMENT '所属行业',
     tax_type TINYINT(2) NOT NULL COMMENT '纳税人性质, 1-小规模, 2-一般纳税人',
     business_dept_id BIGINT NOT NULL COMMENT '业务公司ID',
     business_top_dept_id BIGINT NOT NULL COMMENT '业务顶级部门ID',
     advisor_dept_id BIGINT NOT NULL COMMENT '顾问组别ID',
     advisor_top_dept_id BIGINT NOT NULL COMMENT '顾问顶级部门ID',
     accounting_dept_id BIGINT NOT NULL COMMENT '会计组别ID',
     accounting_top_dept_id BIGINT NOT NULL COMMENT '会计顶级部门ID',
     first_account_period INT NOT NULL COMMENT '首个账期',
     status TINYINT(2) NOT NULL COMMENT '状态, 0-待完善, 1-待提交, 2-待重提，3-待流转, 4-已流转',
     is_new_customer TINYINT(1) NOT NULL COMMENT '新客户, 0-否, 1-是',
     is_del TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否删除, 0-否, 1-是',
     create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
     create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
     update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
     update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
     PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='新户流转客户信息表';

DROP TABLE IF EXISTS c_new_customer_bank_account;
CREATE TABLE c_new_customer_bank_account (
     id                  bigint auto_increment comment '主键ID'
         primary key,
     customer_id         bigint                                not null comment '客户服务ID',
     bank_name           varchar(255)                          not null comment '银行名称',
     bank_account_number varchar(100)                          not null comment '银行账号',
     password            varchar(100)                          null comment '密码',
     phone_number        varchar(15)                           null comment '手机号',
     account_open_date   date                                  null comment '开户时间',
     account_close_date  date                                  null comment '销户时间',
     receipt_status      tinyint                               null comment '回单卡状态, 1-已托管, 2-未托管',
     bank_direct         tinyint                               null comment '银企直连, 1-已开通, 2-未开通',
     deposit_name        varchar(50)                           null comment '开户行名称',
     remarks             varchar(255)                          null comment '备注说明',
     create_by           varchar(64) default ''                null comment '创建者',
     create_time         datetime    default CURRENT_TIMESTAMP null comment '创建时间',
     update_by           varchar(64) default ''                null comment '更新者',
     update_time         datetime    default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='新户流转银行账号表';

DROP TABLE IF EXISTS c_new_customer_sys_account;
CREATE TABLE c_new_customer_sys_account (
    id BIGINT NOT NULL AUTO_INCREMENT COMMENT 'id',
    customer_id BIGINT NOT NULL COMMENT '客户服务id',
    sys_type TINYINT NOT NULL COMMENT '系统账号类型，1-医保，2-社保，3-公积金，4-个税，5-国税',
    sys_type_name VARCHAR(100) DEFAULT NULL COMMENT '系统账号类型名称',
    account VARCHAR(100) NOT NULL COMMENT '账号',
    password VARCHAR(100) NOT NULL COMMENT '密码',
    login_type VARCHAR(100) DEFAULT NULL COMMENT '登陆方式',
    contact VARCHAR(100) DEFAULT NULL COMMENT '实名人员',
    contact_mobile VARCHAR(100) DEFAULT NULL COMMENT '实名手机号',
    is_same_with_credit_code TINYINT(1) NOT NULL DEFAULT '1' COMMENT '账号是否同信用代码',
    id_number VARCHAR(100) DEFAULT NULL COMMENT '身份证号',
    remark VARCHAR(100) DEFAULT NULL COMMENT '备注',
    is_del tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除，0-否，1-是',
    create_by varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '创建者',
    create_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '更新者',
    update_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id),
    KEY customer_id (customer_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='新户流转系统账号表';

DROP TABLE IF EXISTS c_new_customer_finance_tax_info;
CREATE TABLE c_new_customer_finance_tax_info (
     id BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
     customer_id BIGINT NOT NULL COMMENT '客户ID',
     accounting_system TINYINT(2) NULL DEFAULT NULL COMMENT '做账系统',
     finance_record TINYINT(1) NULL DEFAULT NULL COMMENT '财务制度是否备案, 0-否, 1-是',
     report_sent TINYINT(1) NULL DEFAULT NULL COMMENT '会计报表是否送报, 0-否, 1-是',
     last_account_month DATE NULL DEFAULT NULL COMMENT '最后入账月份',
     not_account_reason VARCHAR(255) COMMENT '未入账原因',
     other_remarks VARCHAR(255) COMMENT '其他备注',
     income_main DECIMAL(10, 2) DEFAULT NULL COMMENT '主营收入',
     income_other DECIMAL(10, 2) DEFAULT NULL COMMENT '营业外收入',
     cost DECIMAL(10, 2) DEFAULT NULL COMMENT '成本',
     expense DECIMAL(10, 2) DEFAULT NULL COMMENT '费用',
     profit DECIMAL(10, 2) DEFAULT NULL COMMENT '利润',
     offset_loss DECIMAL(10, 2) DEFAULT NULL COMMENT '可弥补损益',
     total_salary DECIMAL(10, 2) DEFAULT NULL COMMENT '工资总额',
     welfare_fee DECIMAL(10, 2) DEFAULT NULL COMMENT '福利费',
     entertainment_fee DECIMAL(10, 2) DEFAULT NULL COMMENT '招待费',
     other_adjustment DECIMAL(10, 2) DEFAULT NULL COMMENT '其他调增项',
     tax_method TINYINT(2) NULL DEFAULT NULL COMMENT '个税申报方式, 1-易捷账, 2-扣缴端',
     ez_tax_account TINYINT(1) NULL DEFAULT NULL COMMENT '易捷账个税账号, 0-未维护, 1-已维护',
     tax_disk TINYINT(2) NULL DEFAULT NULL COMMENT '税盘, 1-黑盘, 2-白盘, 3-U-key, 4-无',
     tax_password VARCHAR(100) COMMENT '税盘密码',
     create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
     create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
     update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
     update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
     PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='新户流转财税信息表';

DROP TABLE IF EXISTS c_customer_service_finance_tax_info;
CREATE TABLE c_customer_service_finance_tax_info (
     id BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
     customer_service_id BIGINT NOT NULL COMMENT '客户服务ID',
     accounting_system TINYINT(2) NULL DEFAULT NULL COMMENT '做账系统',
     finance_record TINYINT(1) NULL DEFAULT NULL COMMENT '财务制度是否备案, 0-否, 1-是',
     report_sent TINYINT(1) NULL DEFAULT NULL COMMENT '会计报表是否送报, 0-否, 1-是',
     last_account_month DATE NULL DEFAULT NULL COMMENT '最后入账月份',
     not_account_reason VARCHAR(255) COMMENT '未入账原因',
     other_remarks VARCHAR(255) COMMENT '其他备注',
     income_main DECIMAL(10, 2) DEFAULT NULL COMMENT '主营收入',
     income_other DECIMAL(10, 2) DEFAULT NULL COMMENT '营业外收入',
     cost DECIMAL(10, 2) DEFAULT NULL COMMENT '成本',
     expense DECIMAL(10, 2) DEFAULT NULL COMMENT '费用',
     profit DECIMAL(10, 2) DEFAULT NULL COMMENT '利润',
     offset_loss DECIMAL(10, 2) DEFAULT NULL COMMENT '可弥补损益',
     total_salary DECIMAL(10, 2) DEFAULT NULL COMMENT '工资总额',
     welfare_fee DECIMAL(10, 2) DEFAULT NULL COMMENT '福利费',
     entertainment_fee DECIMAL(10, 2) DEFAULT NULL COMMENT '招待费',
     other_adjustment DECIMAL(10, 2) DEFAULT NULL COMMENT '其他调增项',
     tax_method TINYINT(2) NULL DEFAULT NULL COMMENT '个税申报方式, 1-易捷账, 2-扣缴端',
     ez_tax_account TINYINT(1) NULL DEFAULT NULL COMMENT '易捷账个税账号, 0-未维护, 1-已维护',
     tax_disk TINYINT(2) NULL DEFAULT NULL COMMENT '税盘, 1-黑盘, 2-白盘, 3-U-key, 4-无',
     tax_password VARCHAR(100) COMMENT '税盘密码',
     create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
     create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
     update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
     update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
     PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='客户服务财税信息表';

DROP TABLE IF EXISTS c_new_customer_tax_type_check;
CREATE TABLE c_new_customer_tax_type_check (
   id BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
   customer_id BIGINT NOT NULL COMMENT '客户ID',
   report_type TINYINT(2) NOT NULL COMMENT '上报类型, 1-月报, 2-季报, 3-年报, 4-次报',
   tax_type VARCHAR(100) NOT NULL COMMENT '税种类型',
   create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
   create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
   update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
   update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
   PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='新户流转税种核定表';

DROP TABLE IF EXISTS c_new_customer_insurance_fund_info;
CREATE TABLE c_new_customer_insurance_fund_info (
        id BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
        customer_id BIGINT NOT NULL COMMENT '客户ID',
        social_security_people INT NOT NULL COMMENT '社保人数',
        social_security_base DECIMAL(10, 2) NOT NULL COMMENT '社保基数',
        total_contribution DECIMAL(10, 2) DEFAULT NULL COMMENT '社保缴纳金额',
        injury_fee DECIMAL(10, 2) DEFAULT NULL COMMENT '工伤金额',
        injury_rate DECIMAL(5, 4) NOT NULL COMMENT '工伤比例',
        medical_people INT NOT NULL COMMENT '医保人数',
        medical_base DECIMAL(10, 2) NOT NULL COMMENT '医保基数',
        medical_fee DECIMAL(10, 2) DEFAULT NULL COMMENT '医保缴纳金额',
        medical_rate DECIMAL(5, 4) NOT NULL COMMENT '医保比例',
        fund_people INT NOT NULL COMMENT '公积金人数',
        fund_base DECIMAL(10, 2) NOT NULL COMMENT '公积金基数',
        fund_fee DECIMAL(10, 2) DEFAULT NULL COMMENT '公积金缴纳金额',
        fund_rate DECIMAL(5, 4) NOT NULL COMMENT '公积金比例',
        social_security_contact VARCHAR(100) DEFAULT NULL COMMENT '社保人员',
        medical_contact VARCHAR(100) DEFAULT NULL COMMENT '医保人员',
        fund_contact VARCHAR(100) DEFAULT NULL COMMENT '公积金人员',
        create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
        create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
        update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
        PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='新户流转五险一金信息表';

DROP TABLE IF EXISTS c_customer_service_insurance_fund_info;
CREATE TABLE c_customer_service_insurance_fund_info (
        id BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
        customer_id BIGINT NOT NULL COMMENT '客户ID',
        social_security_people INT NOT NULL COMMENT '社保人数',
        social_security_base DECIMAL(10, 2) NOT NULL COMMENT '社保基数',
        total_contribution DECIMAL(10, 2) DEFAULT NULL COMMENT '社保缴纳金额',
        injury_fee DECIMAL(10, 2) DEFAULT NULL COMMENT '工伤金额',
        injury_rate DECIMAL(5, 4) NOT NULL COMMENT '工伤比例',
        medical_people INT NOT NULL COMMENT '医保人数',
        medical_base DECIMAL(10, 2) NOT NULL COMMENT '医保基数',
        medical_fee DECIMAL(10, 2) DEFAULT NULL COMMENT '医保缴纳金额',
        medical_rate DECIMAL(5, 4) NOT NULL COMMENT '医保比例',
        fund_people INT NOT NULL COMMENT '公积金人数',
        fund_base DECIMAL(10, 2) NOT NULL COMMENT '公积金基数',
        fund_fee DECIMAL(10, 2) DEFAULT NULL COMMENT '公积金缴纳金额',
        fund_rate DECIMAL(5, 4) NOT NULL COMMENT '公积金比例',
        social_security_contact VARCHAR(100) DEFAULT NULL COMMENT '社保人员',
        medical_contact VARCHAR(100) DEFAULT NULL COMMENT '医保人员',
        fund_contact VARCHAR(100) DEFAULT NULL COMMENT '公积金人员',
        create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
        create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
        update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
        PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='客户服务五险一金信息表';

DROP TABLE IF EXISTS c_new_customer_insurance_fund_status;
CREATE TABLE c_new_customer_insurance_fund_status (
      id BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
      insurance_fund_id BIGINT NOT NULL COMMENT '五险一金信息ID',
      month TINYINT(2) NOT NULL COMMENT '月份',
      status TINYINT(2) NOT NULL COMMENT '状态, 1-已扣款, 2-待申报',
      create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
      create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
      update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
      update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
      PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='新户流转五险一金月状态表';

DROP TABLE IF EXISTS c_customer_service_insurance_fund_status;
CREATE TABLE c_customer_service_insurance_fund_status (
      id BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
      insurance_fund_id BIGINT NOT NULL COMMENT '五险一金信息ID',
      month TINYINT(2) NOT NULL COMMENT '月份',
      status TINYINT(2) NOT NULL COMMENT '状态, 1-已扣款, 2-待申报',
      create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
      create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
      update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
      update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
      PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='客户服务五险一金月状态表';

DROP TABLE IF EXISTS c_new_customer_other_info;
CREATE TABLE c_new_customer_other_info (
       id BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
       customer_id BIGINT NOT NULL COMMENT '客户ID',
       tax_submission_status TINYINT(2) NOT NULL COMMENT '汇算清缴状态, 1-已申报已扣款, 2-已申报未扣款',
       pre_tax_profit DECIMAL(10, 2) DEFAULT NULL COMMENT '所得税交税金额',
       next_year_supplement DECIMAL(10, 2) DEFAULT NULL COMMENT '下一年可弥补金额',
       notes VARCHAR(255) COMMENT '备注',
       create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
       create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
       update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
       update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
       PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='新户流转其他信息表';

DROP TABLE IF EXISTS c_customer_service_other_info;
CREATE TABLE c_customer_service_other_info (
       id BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
       customer_service_id BIGINT NOT NULL COMMENT '客户服务ID',
       tax_submission_status TINYINT(2) NOT NULL COMMENT '汇算清缴状态, 1-已申报已扣款, 2-已申报未扣款',
       pre_tax_profit DECIMAL(10, 2) DEFAULT NULL COMMENT '所得税交税金额',
       next_year_supplement DECIMAL(10, 2) DEFAULT NULL COMMENT '下一年可弥补金额',
       notes VARCHAR(255) COMMENT '备注',
       create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
       create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
       update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
       update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
       PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='客户服务其他信息表';

DROP TABLE IF EXISTS c_new_customer_income_info;
CREATE TABLE c_new_customer_income_info (
        id BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
        customer_id BIGINT NOT NULL COMMENT '客户ID',
        month TINYINT(2) NOT NULL COMMENT '月份',
        amount DECIMAL(10, 2) DEFAULT NULL COMMENT '收入金额',
        create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
        create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
        update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
        PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='新户流转收入信息表';

DROP TABLE IF EXISTS c_customer_service_income_info;
CREATE TABLE c_customer_service_income_info (
        id BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
        customer_service_id BIGINT NOT NULL COMMENT '客户服务ID',
        month TINYINT(2) NOT NULL COMMENT '月份',
        amount DECIMAL(10, 2) DEFAULT NULL COMMENT '收入金额',
        create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
        create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
        update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
        PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='客户服务收入信息表';

DROP TABLE IF EXISTS `c_new_customer_fixed_assets_info`;
CREATE TABLE c_new_customer_fixed_assets_info (
      id BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
      customer_id BIGINT NOT NULL COMMENT '客户ID',
      asset_name VARCHAR(255) NOT NULL COMMENT '一次性税前扣除固定资产',
      occurrence_year INT NOT NULL COMMENT '发生年份',
      create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
      create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
      update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
      update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
      PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='新户流转固定资产信息表';

DROP TABLE IF EXISTS `c_new_customer_annual_report_info`;
CREATE TABLE c_new_customer_annual_report_info (
       id BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
       customer_id BIGINT NOT NULL COMMENT '客户ID',
       status TINYINT(2) NOT NULL COMMENT '状态, 1-已申报, 2-未申报',
       e_business_license TINYINT(1) NOT NULL COMMENT '电子营业执照, 0-无, 1-有',
       contact_name VARCHAR(100) COMMENT '联系人姓名',
       contact_id_number VARCHAR(100) COMMENT '联系人身份证',
       notes VARCHAR(255) COMMENT '备注',
       create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
       create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
       update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
       update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
       PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='新户流转工商年报信息表';

DROP TABLE IF EXISTS `c_customer_service_fixed_assets_info`;
CREATE TABLE c_customer_service_fixed_assets_info (
      id BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
      customer_service_id BIGINT NOT NULL COMMENT '客户服务ID',
      asset_name VARCHAR(255) NOT NULL COMMENT '一次性税前扣除固定资产',
      occurrence_year INT NOT NULL COMMENT '发生年份',
      create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
      create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
      update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
      update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
      PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='客户服务固定资产信息表';

DROP TABLE IF EXISTS `c_customer_service_annual_report_info`;
CREATE TABLE c_customer_service_annual_report_info (
       id BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
       customer_service_id BIGINT NOT NULL COMMENT '客户服务ID',
       status TINYINT(2) NOT NULL COMMENT '状态, 1-已申报, 2-未申报',
       e_business_license TINYINT(1) NOT NULL COMMENT '电子营业执照, 0-无, 1-有',
       contact_name VARCHAR(100) COMMENT '联系人姓名',
       contact_id_number VARCHAR(100) COMMENT '联系人身份证',
       notes VARCHAR(255) COMMENT '备注',
       create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
       create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
       update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
       update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
       PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='客户服务工商年报信息表';

