####### 已有表。表结构变更
ALTER TABLE c_customer_service_in_account
    CHANGE COLUMN major_income_total major_income_total DECIMAL(40, 20) NULL COMMENT '本年累计主营收入';
ALTER TABLE c_customer_service_in_account
    CHANGE COLUMN major_cost_total major_cost_total DECIMAL(40, 20) NULL COMMENT '本年累计主营成本';
ALTER TABLE c_customer_service_in_account
    CHANGE COLUMN profit_total profit_total DECIMAL(40, 20) NULL COMMENT '本年累计会计利润';

ALTER TABLE c_customer_service_in_account
    ADD COLUMN deliver_result TINYINT NULL COMMENT '交付结果：1-正常、2-无需交付' AFTER `status`;

ALTER TABLE c_customer_service_in_account
    ADD COLUMN tax_report_count INT NULL COMMENT '个税申报人数' AFTER `profit_total`;
ALTER TABLE c_customer_service_in_account
    ADD COLUMN tax_report_salary_total DECIMAL(40, 20) NULL COMMENT '本年个税申报工资总额' AFTER `tax_report_count`;


ALTER TABLE c_customer_service_in_account
    ADD COLUMN rpa_exe_result TINYINT NULL COMMENT 'RPA执行结果：1-成功、0-失败' AFTER `end_employee_name`;
ALTER TABLE c_customer_service_in_account
    ADD COLUMN table_status_balance VARCHAR(100) NULL COMMENT '报表状态是否平衡' AFTER `rpa_exe_result`;
ALTER TABLE c_customer_service_in_account
    ADD COLUMN rpa_search_time DATETIME NULL COMMENT 'RPA查询时间' AFTER `table_status_balance`;
ALTER TABLE c_customer_service_in_account
    ADD COLUMN rpa_remark VARCHAR(1000) COMMENT 'RPA备注' AFTER `rpa_search_time`;


