alter table c_customer_service_period_month_income
    add column `no_ticket_income_amount` decimal(10,2) not null default 0 comment '无票收入' after `ticket_search_tax_amount`,
    add column `no_ticket_income_time` datetime null default null comment '无票收入更新时间' after `no_ticket_income_amount`;

CREATE TABLE `c_customer_service_period_month_tax_type_check` (
     `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
     `customer_service_period_month_id` bigint NOT NULL DEFAULT '0' COMMENT '月账期id',
     `report_type` tinyint NOT NULL COMMENT '上报类型，1-月报，2-季报，3-年报，4-次报，5-半年报，6-无需申报',
     `tax_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '税种类型',
     `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '创建者',
     `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
     `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '更新者',
     `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
     PRIMARY KEY (`id`),
     KEY `customer_service_id` (`customer_service_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='服务月账期税种核定表';