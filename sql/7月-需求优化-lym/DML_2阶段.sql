UPDATE `bxm-test`.c_customer_deliver_template t SET t.file_url = 'template/taxReportTemplate_1.0.1.xlsx' WHERE t.id = 12;

UPDATE `bxm-test`.c_customer_deliver_template t SET t.file_url = 'template/taxReportTemplate_1.0.1.xlsx' WHERE t.id = 2;

UPDATE `bxm-test`.c_customer_deliver_template t SET t.file_url = 'template/taxReportTemplate_1.0.1.xlsx' WHERE t.id = 7;

UPDATE `bxm-test`.c_customer_deliver_template t SET t.file_url = 'template/countryTaxReportTemplate_1.0.1.xlsx' WHERE t.id = 17;

update c_customer_service ccs join (select customer_service_id,
    sum(if(period = 202407, all_ticket_amount + no_ticket_income_amount, 0)) as thisMonthIncome,
    sum(if(period >= 202401 and period <= 202412, all_ticket_amount + no_ticket_income_amount, 0)) as thisYearIncome,
    sum(if(period >= 202407 and period <= 202409, all_ticket_amount + no_ticket_income_amount, 0)) as thisSeasonIncome,
    sum(if(period >= 202308 and period <= 202407, all_ticket_amount + no_ticket_income_amount, 0)) as this12MonthIncome,
    max(ticket_time) as ticketTime
    from c_customer_service_period_month_income where period >= 202308 and period <= 202412 group by customer_service_id) a
on ccs.id = a.customer_service_id
    set
        ccs.this_month_income = a.thisMonthIncome,
        ccs.this_year_income = a.thisYearIncome,
        ccs.this_12_month_income = a.this12MonthIncome,
        ccs.this_season_income = a.thisSeasonIncome,
        ccs.ticket_time = a.ticketTime
where ccs.is_del = 0;

update c_customer_deliver set current_period_amount = report_amount where report_amount is not null;