CREATE TABLE `c_open_api_sync_statement_detail` (
                                        `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                        `syc_record_id` BIGINT NOT NULL COMMENT '同步记录id',
                                        `sync_customer_id` BIGINT NOT NULL COMMENT '同步客户id',
                                        `uid` VARCHAR(100) NULL DEFAULT NULL COMMENT '唯一标识符',
                                        `tax_number` VARCHAR(50) NULL DEFAULT NULL COMMENT '税号',
                                        `customer_id` VARCHAR(50) NULL DEFAULT NULL COMMENT '客户ID',
                                        `accounting_month` VARCHAR(50) NULL DEFAULT NULL COMMENT '建账月份',
                                        `payment_month` VARCHAR(50) NULL DEFAULT NULL COMMENT '缴费所属月份',
                                        `personal_id` VARCHAR(50) NULL DEFAULT NULL COMMENT '个人识别号',
                                        `name` VARCHAR(50) NULL DEFAULT NULL COMMENT '姓名',
                                        `insured_identity` VARCHAR(50) NULL DEFAULT NULL COMMENT '参保人员身份',
                                        `total_insured_months` VARCHAR(50) NULL DEFAULT NULL COMMENT '累计参保月数',
                                        `total_payment_amount` VARCHAR(50) NULL DEFAULT NULL COMMENT '缴费总金额',
                                        `company_bear_amount` VARCHAR(50) NULL DEFAULT NULL COMMENT '单位承担金额',
                                        `personal_bear_amount` VARCHAR(50) NULL DEFAULT NULL COMMENT '个人承担金额',
                                        `medical_personal_bear` VARCHAR(50) NULL DEFAULT NULL COMMENT '医疗_个人承担',
                                        `pension_personal_bear` VARCHAR(50) NULL DEFAULT NULL COMMENT '养老_个人承担',
                                        `unemployment_personal_bear` VARCHAR(50) NULL DEFAULT NULL COMMENT '失业_个人承担',
                                        `maternity_personal_bear` VARCHAR(50) NULL DEFAULT NULL COMMENT '生育_个人承担',
                                        `employee_medical_insurance_personal` VARCHAR(50) NULL DEFAULT NULL COMMENT '职工社会医疗保险_个人',
                                        `occupational_annuity_personal` VARCHAR(50) NULL DEFAULT NULL COMMENT '职业年金缴费金额_个人',
                                        `government_agency_pension_personal` VARCHAR(50) NULL DEFAULT NULL COMMENT '机关事业单位养老保险缴费金额_个人',
                                        `civil_servant_medical_subsidy_personal` VARCHAR(50) NULL DEFAULT NULL COMMENT '公务员医疗补助缴费金额_个人',
                                        `major_medical_expense_subsidy_personal` VARCHAR(50) NULL DEFAULT NULL COMMENT '大额医疗费用补助缴费金额_个人',
                                        `long_term_care_insurance_personal` VARCHAR(50) NULL DEFAULT NULL COMMENT '长期照护保险缴费金额_个人',
                                        `medical_company_bear` VARCHAR(50) NULL DEFAULT NULL COMMENT '医疗_单位承担',
                                        `pension_company_bear` VARCHAR(50) NULL DEFAULT NULL COMMENT '养老_单位承担',
                                        `injury_company_bear` VARCHAR(50) NULL DEFAULT NULL COMMENT '工伤_单位承担',
                                        `unemployment_company_bear` VARCHAR(50) NULL DEFAULT NULL COMMENT '失业_单位承担',
                                        `maternity_company_bear` VARCHAR(50) NULL DEFAULT NULL COMMENT '生育_单位承担',
                                        `employee_major_disease_medical_subsidy_company_bear` VARCHAR(50) NULL DEFAULT NULL COMMENT '职工重大疾病医疗补助_单位承担',
                                        `employee_medical_insurance_company` VARCHAR(50) NULL DEFAULT NULL COMMENT '职工社会医疗保险_单位',
                                        `employee_additional_medical_insurance_company` VARCHAR(50) NULL DEFAULT NULL COMMENT '职工补充医疗保险_单位',
                                        `occupational_annuity_company` VARCHAR(50) NULL DEFAULT NULL COMMENT '职业年金缴费金额_单位',
                                        `government_agency_pension_company` VARCHAR(50) NULL DEFAULT NULL COMMENT '机关事业单位养老保险缴费金额_单位',
                                        `civil_servant_medical_subsidy_company` VARCHAR(50) NULL DEFAULT NULL COMMENT '公务员医疗补助缴费金额_单位',
                                        `major_medical_expense_subsidy_company` VARCHAR(50) NULL DEFAULT NULL COMMENT '大额医疗费用补助缴费金额_单位',
                                        `employee_major_medical_mutual_insurance_company` VARCHAR(50) NULL DEFAULT NULL COMMENT '职工大额医疗互助保险缴费金额_单位',
                                        `long_term_care_insurance_company` VARCHAR(50) NULL DEFAULT NULL COMMENT '长期照护保险缴费金额_单位',
                                        `employee_major_disease_mutual_insurance_company` VARCHAR(50) NULL DEFAULT NULL COMMENT '职工大病医疗互助保险缴费金额_单位',
                                        `create_by` VARCHAR(64) NULL DEFAULT NULL COMMENT '创建者' COLLATE 'utf8mb4_general_ci',
                                        `create_time` DATETIME NOT NULL DEFAULT (CURRENT_TIMESTAMP) COMMENT '创建时间',
                                        `update_by` VARCHAR(64) NULL DEFAULT NULL COMMENT '更新者' COLLATE 'utf8mb4_general_ci',
                                        `update_time` DATETIME NOT NULL DEFAULT (CURRENT_TIMESTAMP) ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                        PRIMARY KEY (`id`) USING BTREE
)
COMMENT='医社保个人明细查询记录'
COLLATE='utf8mb4_general_ci'
ENGINE=InnoDB
;
