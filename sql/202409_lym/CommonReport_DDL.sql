CREATE TABLE `c_open_api_deduction_record` (
                                               `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                               `source_type` TINYINT NOT NULL COMMENT '来源类型，1-鑫启易',
                                               `batch_no` VARCHAR(50) NULL DEFAULT NULL COMMENT '批次号' COLLATE 'utf8mb4_general_ci',
                                               `tax_number` VARCHAR(50) NULL DEFAULT NULL COMMENT '税号' COLLATE 'utf8mb4_general_ci',
                                               `customer_name` VARCHAR(50) NULL DEFAULT NULL COMMENT '客户名称' COLLATE 'utf8mb4_general_ci',
                                               `report_period` VARCHAR(50) NULL DEFAULT NULL COMMENT '申报期' COLLATE 'utf8mb4_general_ci',
                                               `offical_filename` VARCHAR(50) NULL DEFAULT NULL COMMENT '税局下载的报表名称' COLLATE 'utf8mb4_general_ci',
                                               `tax_period_start` VARCHAR(50) NULL DEFAULT NULL COMMENT '税款所属期起' COLLATE 'utf8mb4_general_ci',
                                               `tax_period_end` VARCHAR(50) NULL DEFAULT NULL COMMENT '税款所属期止' COLLATE 'utf8mb4_general_ci',
                                               `file_id` VARCHAR(255) NULL DEFAULT NULL COMMENT '文件url' COLLATE 'utf8mb4_general_ci',
                                               `file_name` VARCHAR(255) NULL DEFAULT NULL COMMENT '文件名称' COLLATE 'utf8mb4_general_ci',
                                               `deliver_id` BIGINT NULL DEFAULT NULL COMMENT '交付单id',
                                               `result` VARCHAR(50) NULL DEFAULT NULL COMMENT '扣款结果，1-正常，2-异常',
                                               `create_by` VARCHAR(64) NULL DEFAULT NULL COMMENT '创建者' COLLATE 'utf8mb4_general_ci',
                                               `create_time` DATETIME NOT NULL DEFAULT (CURRENT_TIMESTAMP) COMMENT '创建时间',
                                               `update_by` VARCHAR(64) NULL DEFAULT NULL COMMENT '更新者' COLLATE 'utf8mb4_general_ci',
                                               `update_time` DATETIME NOT NULL DEFAULT (CURRENT_TIMESTAMP) ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                               PRIMARY KEY (`id`) USING BTREE
)
    COMMENT='第三方扣款同步表'
COLLATE='utf8mb4_general_ci'
ENGINE=InnoDB
;
