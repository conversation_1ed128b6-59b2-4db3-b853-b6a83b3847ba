CREATE TABLE `c_download_record` (
       `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT 'id',
       `title` VARCHAR(255) NULL DEFAULT NULL COMMENT '标题',
       `data_count` BIGINT NULL DEFAULT NULL COMMENT '数据量',
       `attachment_count` BIGINT NULL DEFAULT NULL COMMENT '附件数量',
       `status` tinyint(2) not null default 0 comment '状态,0-生成中，1-成功，2-失败',
       `error_reason` varchar(500) null default null comment '错误原因',
       `download_type` tinyint(2) null default null comment '下载类型，1-服务-客户列表，2-服务-操作记录，3-服务-账期列表，4-服务-年度汇总，5-服务-补账服务，6-交付-医社保，7-交付-个税（工资薪金），8-个税（经营所得），9-交付-国税，10-交付-医社保 含附件，11-交付-个税（工资薪金）含附件，12-个税（经营所得）含附件，13-交付-国税 含附件，14-交付-入账，15-收入，16-材料-交接，17-材料借阅',
       `download_url` VARCHAR(255) NULL DEFAULT NULL COMMENT '下载地址',
       `param` varchar(5000) null default null comment '查询参数',
       `user_id` BIGINT NULL DEFAULT NULL COMMENT '用户id',
       `is_del` tinyint(1) not null default 0 comment '是否删除,0-否，1-是',
       `is_file_del` tinyint(1) not null default 0 comment '是否删除文件，0-否，1-是',
       `create_by` VARCHAR(64) NULL DEFAULT '' COMMENT '创建者' COLLATE 'utf8mb4_general_ci',
       `create_time` DATETIME NOT NULL DEFAULT (CURRENT_TIMESTAMP) COMMENT '创建时间',
       `update_by` VARCHAR(64) NULL DEFAULT '' COMMENT '更新者' COLLATE 'utf8mb4_general_ci',
       `update_time` DATETIME NOT NULL DEFAULT (CURRENT_TIMESTAMP) ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
       PRIMARY KEY (`id`) USING BTREE
)
    COMMENT='导出记录表'
COLLATE='utf8mb4_general_ci'
ENGINE=InnoDB
;
