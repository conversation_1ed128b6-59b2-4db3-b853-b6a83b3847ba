CREATE TABLE `c_settlement_order` (
                                      `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '结算单id',
                                      `bill_title` VARCHAR(100) NULL DEFAULT NULL COMMENT '账单标题' COLLATE 'utf8mb4_general_ci',
                                      `bill_no` VARCHAR(100) NULL DEFAULT NULL COMMENT '账单编号' COLLATE 'utf8mb4_general_ci',
                                      `settlement_order_no` VARCHAR(50) NULL DEFAULT NULL COMMENT '结算单编号',
                                      `settlement_batch_no` VARCHAR(50) NULL DEFAULT NULL COMMENT '结算单批次号',
                                      `settlement_title` VARCHAR(100) NULL DEFAULT NULL COMMENT '结算单标题',
                                      `business_top_dept_id` BIGINT NOT NULL COMMENT '业务集团id',
                                      `business_dept_id` BIGINT NOT NULL COMMENT '业务公司id',
                                      `settlement_type` TINYINT(2) NOT NULL COMMENT '结算类型，1-入账结算，2-新户预收',
                                      `is_supplement` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否补差，0-否，1-是',
                                      `unit` VARCHAR(50) NULL DEFAULT NULL COMMENT '单位',
                                      `data_count` BIGINT NOT NULL DEFAULT 0 COMMENT '总数据量',
                                      `price` DECIMAL(10,2) NOT NULL COMMENT '单价',
                                      `total_price` DECIMAL(10,2) NOT NULL COMMENT '总金额',
                                      `discount_price` DECIMAL(10,2) NOT NULL DEFAULT 0 COMMENT '优惠金额',
                                      `settlement_price` DECIMAL(10,2) NOT NULL COMMENT '结算金额',
                                      `remark` VARCHAR(1000) NULL DEFAULT NULL COMMENT '备注',
                                      `status` TINYINT(2) NOT NULL DEFAULT 1 COMMENT '状态,1-已创建待推送，2-结算中，3-已驳回，4-已撤回，5-已确认',
                                      `is_wait_for_edit` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否待修改，0-否，1-是',
                                      `is_del` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0-否，1-是',
                                      `create_by` VARCHAR(64) NULL DEFAULT '' COMMENT '创建者' COLLATE 'utf8mb4_general_ci',
                                      `create_time` DATETIME NOT NULL DEFAULT (CURRENT_TIMESTAMP) COMMENT '创建时间',
                                      `update_by` VARCHAR(64) NULL DEFAULT '' COMMENT '更新者' COLLATE 'utf8mb4_general_ci',
                                      `update_time` DATETIME NOT NULL DEFAULT (CURRENT_TIMESTAMP) ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                      PRIMARY KEY (`id`) USING BTREE
)
    COMMENT='结算单表'
COLLATE='utf8mb4_general_ci'
ENGINE=InnoDB
;

CREATE TABLE `c_settlement_order_condition` (
                                                `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '账单id',
                                                `settlement_order_id` BIGINT NOT NULL COMMENT '结算单id',
                                                `condition_type` VARCHAR(50) NOT NULL COMMENT '条件类型key',
                                                `condition_type_name` VARCHAR(50) NOT NULL COMMENT '条件类型名称',
                                                `condition_value` TEXT NULL DEFAULT NULL COMMENT '条件值',
                                                `create_by` VARCHAR(64) NULL DEFAULT '' COMMENT '创建者' COLLATE 'utf8mb4_general_ci',
                                                `create_time` DATETIME NOT NULL DEFAULT (CURRENT_TIMESTAMP) COMMENT '创建时间',
                                                `update_by` VARCHAR(64) NULL DEFAULT '' COMMENT '更新者' COLLATE 'utf8mb4_general_ci',
                                                `update_time` DATETIME NOT NULL DEFAULT (CURRENT_TIMESTAMP) ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                                PRIMARY KEY (`id`) USING BTREE
)
    COMMENT='结算单条件表'
COLLATE='utf8mb4_general_ci'
ENGINE=InnoDB
;

CREATE TABLE `c_settlement_order_file` (
                                           `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '账单id',
                                           `settlement_order_id` BIGINT NOT NULL COMMENT '结算单id',
                                           `file_url` VARCHAR(255) NOT NULL COMMENT 'url',
                                           `file_name` VARCHAR(500) NOT NULL COMMENT '文件名',
                                           `file_type` TINYINT(2) NOT NULL COMMENT '文件类型，1-创建附件，2-导入范围附件',
                                           `is_del` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0-否，1-是',
                                           `create_by` VARCHAR(64) NULL DEFAULT '' COMMENT '创建者' COLLATE 'utf8mb4_general_ci',
                                           `create_time` DATETIME NOT NULL DEFAULT (CURRENT_TIMESTAMP) COMMENT '创建时间',
                                           `update_by` VARCHAR(64) NULL DEFAULT '' COMMENT '更新者' COLLATE 'utf8mb4_general_ci',
                                           `update_time` DATETIME NOT NULL DEFAULT (CURRENT_TIMESTAMP) ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                           PRIMARY KEY (`id`) USING BTREE
)
    COMMENT='结算单附件表'
COLLATE='utf8mb4_general_ci'
ENGINE=InnoDB
;

CREATE TABLE `c_settlement_order_data` (
                                           `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '账单id',
                                           `settlement_order_id` BIGINT NOT NULL COMMENT '结算单id',
                                           `business_id` BIGINT NOT NULL COMMENT '业务id，服务id或账期id',
                                           `business_type` TINYINT(2) NOT NULL COMMENT '业务类型，1-服务，2-账期',
                                           `customer_service_id` BIGINT NOT NULL COMMENT '客户服务id',
                                           `customer_service_tax_type` TINYINT(2) NULL DEFAULT NULL COMMENT '服务纳税人性质',
                                           `customer_service_advisor_dept_id` BIGINT NULL DEFAULT NULL COMMENT '服务顾问小组id',
                                           `customer_service_advisor_dept_name` VARCHAR(50) NULL DEFAULT NULL COMMENT '服务顾问小组名称',
                                           `customer_service_advisor_employee_name` VARCHAR(500) NULL DEFAULT NULL COMMENT '服务顾问人员名称',
                                           `customer_service_accounting_dept_id` BIGINT NULL DEFAULT NULL COMMENT '服务会计小组id',
                                           `customer_service_accounting_dept_name` VARCHAR(50) NULL DEFAULT NULL COMMENT '服务会计小组名称',
                                           `customer_service_accounting_employee_name` VARCHAR(500) NULL DEFAULT NULL COMMENT '服务会计人员名称',
                                           `customer_service_first_account_period` INT NULL DEFAULT NULL COMMENT '服务首个账期',
                                           `customer_service_accounting_remark` VARCHAR(1000) NULL DEFAULT NULL COMMENT '服务会计备注',
                                           `period` INT NULL DEFAULT NULL COMMENT '账期',
                                           `period_service_type` TINYINT(2) NULL DEFAULT NULL COMMENT '账期类型，1-代账，2-补账',
                                           `period_tax_type` TINYINT(2) NULL DEFAULT NULL COMMENT '账期纳税人性质',
                                           `period_advisor_dept_id` BIGINT NULL DEFAULT NULL COMMENT '账期顾问小组id',
                                           `period_advisor_dept_name` VARCHAR(50) NULL DEFAULT NULL COMMENT '账期顾问小组名称',
                                           `period_advisor_employee_name` VARCHAR(500) NULL DEFAULT NULL COMMENT '账期顾问人员名称',
                                           `period_accounting_dept_id` BIGINT NULL DEFAULT NULL COMMENT '账期会计小组id',
                                           `period_accounting_dept_name` VARCHAR(50) NULL DEFAULT NULL COMMENT '账期会计小组名称',
                                           `period_accounting_employee_name` VARCHAR(500) NULL DEFAULT NULL COMMENT '账期会计人员名称',
                                           `period_accounting_status` TINYINT(2) NULL DEFAULT NULL COMMENT '账务状态，1-正常，2-无需做账',
                                           `period_in_account_deliver_result` TINYINT(2) NULL DEFAULT NULL COMMENT '交付结果：1-正常、2-无需交付、3-异常',
                                           `period_in_account_bank_payment_input_time` DATE NULL DEFAULT NULL COMMENT '银行流水录入日期',
                                           `period_in_account_in_time` DATE NULL DEFAULT NULL COMMENT '入账时间',
                                           `period_in_account_end_time` DATE NULL DEFAULT NULL COMMENT '结账时间',
                                           `period_in_account_rpa_remark` VARCHAR(1000) NULL DEFAULT NULL COMMENT '入账rpa备注',
                                           `create_dept_id` BIGINT NULL DEFAULT NULL COMMENT '创建小组id',
                                           `create_dept_name` VARCHAR(50) NULL DEFAULT NULL COMMENT '创建小组名称',
                                           `create_by` VARCHAR(64) NULL DEFAULT '' COMMENT '创建者' COLLATE 'utf8mb4_general_ci',
                                           `create_time` DATETIME NOT NULL DEFAULT (CURRENT_TIMESTAMP) COMMENT '创建时间',
                                           `update_by` VARCHAR(64) NULL DEFAULT '' COMMENT '更新者' COLLATE 'utf8mb4_general_ci',
                                           `update_time` DATETIME NOT NULL DEFAULT (CURRENT_TIMESTAMP) ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                           PRIMARY KEY (`id`) USING BTREE
)
    COMMENT='结算单关联数据表'
COLLATE='utf8mb4_general_ci'
ENGINE=InnoDB
;

CREATE TABLE `c_settlement_order_data_temp` (
                                           `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '账单id',
                                           `settlement_order_batch_no` VARCHAR(50) NOT NULL COMMENT '结算单批次号',
                                           `business_id` BIGINT NOT NULL COMMENT '业务id，服务id或账期id',
                                           `business_type` TINYINT(2) NOT NULL COMMENT '业务类型，1-服务，2-账期',
                                           `business_dept_id` BIGINT NOT NULL COMMENT '业务公司id',
                                           `customer_service_id` BIGINT NOT NULL COMMENT '客户服务id',
                                           `customer_service_tax_type` TINYINT(2) NULL DEFAULT NULL COMMENT '服务纳税人性质',
                                           `customer_service_advisor_dept_id` BIGINT NULL DEFAULT NULL COMMENT '服务顾问小组id',
                                           `customer_service_advisor_dept_name` VARCHAR(50) NULL DEFAULT NULL COMMENT '服务顾问小组名称',
                                           `customer_service_advisor_employee_name` VARCHAR(500) NULL DEFAULT NULL COMMENT '服务顾问人员名称',
                                           `customer_service_accounting_dept_id` BIGINT NULL DEFAULT NULL COMMENT '服务会计小组id',
                                           `customer_service_accounting_dept_name` VARCHAR(50) NULL DEFAULT NULL COMMENT '服务会计小组名称',
                                           `customer_service_accounting_employee_name` VARCHAR(500) NULL DEFAULT NULL COMMENT '服务会计人员名称',
                                           `customer_service_first_account_period` INT NULL DEFAULT NULL COMMENT '服务首个账期',
                                           `customer_service_accounting_remark` VARCHAR(1000) NULL DEFAULT NULL COMMENT '服务会计备注',
                                           `period` INT NULL DEFAULT NULL COMMENT '账期',
                                           `period_service_type` TINYINT(2) NULL DEFAULT NULL COMMENT '账期类型，1-代账，2-补账',
                                           `period_tax_type` TINYINT(2) NULL DEFAULT NULL COMMENT '账期纳税人性质',
                                           `period_advisor_dept_id` BIGINT NULL DEFAULT NULL COMMENT '账期顾问小组id',
                                           `period_advisor_dept_name` VARCHAR(50) NULL DEFAULT NULL COMMENT '账期顾问小组名称',
                                           `period_advisor_employee_name` VARCHAR(500) NULL DEFAULT NULL COMMENT '账期顾问人员名称',
                                           `period_accounting_dept_id` BIGINT NULL DEFAULT NULL COMMENT '账期会计小组id',
                                           `period_accounting_dept_name` VARCHAR(50) NULL DEFAULT NULL COMMENT '账期会计小组名称',
                                           `period_accounting_employee_name` VARCHAR(500) NULL DEFAULT NULL COMMENT '账期会计人员名称',
                                           `period_accounting_status` TINYINT(2) NULL DEFAULT NULL COMMENT '账务状态，1-正常，2-无需做账',
                                           `period_in_account_deliver_result` TINYINT(2) NULL DEFAULT NULL COMMENT '交付结果：1-正常、2-无需交付、3-异常',
                                           `period_in_account_bank_payment_input_time` DATE NULL DEFAULT NULL COMMENT '银行流水录入日期',
                                           `period_in_account_in_time` DATE NULL DEFAULT NULL COMMENT '入账时间',
                                           `period_in_account_end_time` DATE NULL DEFAULT NULL COMMENT '结账时间',
                                           `period_in_account_rpa_remark` VARCHAR(1000) NULL DEFAULT NULL COMMENT '入账rpa备注',
                                           `create_dept_id` BIGINT NULL DEFAULT NULL COMMENT '创建小组id',
                                           `create_dept_name` VARCHAR(50) NULL DEFAULT NULL COMMENT '创建小组名称',
                                           `create_by` VARCHAR(64) NULL DEFAULT '' COMMENT '创建者' COLLATE 'utf8mb4_general_ci',
                                           `create_time` DATETIME NOT NULL DEFAULT (CURRENT_TIMESTAMP) COMMENT '创建时间',
                                           `update_by` VARCHAR(64) NULL DEFAULT '' COMMENT '更新者' COLLATE 'utf8mb4_general_ci',
                                           `update_time` DATETIME NOT NULL DEFAULT (CURRENT_TIMESTAMP) ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                           PRIMARY KEY (`id`) USING BTREE
)
    COMMENT='结算单关联数据临时表'
COLLATE='utf8mb4_general_ci'
ENGINE=InnoDB
;

CREATE TABLE `c_bill` (
                          `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '账单id',
                          `bill_no` VARCHAR(50) NOT NULL COMMENT '账单编号',
                          `bill_title` VARCHAR(500) NOT NULL COMMENT '账单标题',
                          `business_top_dept_id` BIGINT NOT NULL COMMENT '业务集团id',
                          `business_dept_id` BIGINT NOT NULL COMMENT '业务公司id',
                          `status` TINYINT(2) NOT NULL DEFAULT 0 COMMENT '状态，0-已推送待确认，1-已驳回，2-已撤回，3-已确认',
                          `total_price` DECIMAL(20,2) NOT NULL COMMENT '总金额',
                          `discount_price` DECIMAL(20,2) NOT NULL DEFAULT 0 COMMENT '优惠金额',
                          `deduction_price` DECIMAL(20,2) NOT NULL DEFAULT 0 COMMENT '抵扣金额',
                          `ought_price` DECIMAL(20,2) NOT NULL COMMENT '应收金额',
                          `remark` VARCHAR(1000) NULL DEFAULT NULL COMMENT '备注',
                          `is_del` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0-否，1-是',
                          `create_by` VARCHAR(64) NULL DEFAULT '' COMMENT '创建者' COLLATE 'utf8mb4_general_ci',
                          `create_time` DATETIME NOT NULL DEFAULT (CURRENT_TIMESTAMP) COMMENT '创建时间',
                          `update_by` VARCHAR(64) NULL DEFAULT '' COMMENT '更新者' COLLATE 'utf8mb4_general_ci',
                          `update_time` DATETIME NOT NULL DEFAULT (CURRENT_TIMESTAMP) ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                          PRIMARY KEY (`id`) USING BTREE
)
    COMMENT='账单表'
COLLATE='utf8mb4_general_ci'
ENGINE=InnoDB
;

CREATE TABLE `c_bill_settlement_order_relation` (
                                                    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT 'id',
                                                    `bill_id` BIGINT NOT NULL COMMENT '账单id',
                                                    `settlement_order_id` BIGINT NOT NULL COMMENT '结算单id',
                                                    `create_by` VARCHAR(64) NULL DEFAULT '' COMMENT '创建者' COLLATE 'utf8mb4_general_ci',
                                                    `create_time` DATETIME NOT NULL DEFAULT (CURRENT_TIMESTAMP) COMMENT '创建时间',
                                                    `update_by` VARCHAR(64) NULL DEFAULT '' COMMENT '更新者' COLLATE 'utf8mb4_general_ci',
                                                    `update_time` DATETIME NOT NULL DEFAULT (CURRENT_TIMESTAMP) ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                                    PRIMARY KEY (`id`) USING BTREE
)
    COMMENT='账单结算单关系表'
COLLATE='utf8mb4_general_ci'
ENGINE=InnoDB
;

CREATE TABLE `c_bill_file` (
                                           `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT 'id',
                                           `bill_id` BIGINT NOT NULL COMMENT '账单id',
                                           `file_url` VARCHAR(255) NOT NULL COMMENT 'url',
                                           `file_name` VARCHAR(500) NOT NULL COMMENT '文件名',
                                           `file_type` TINYINT(2) NOT NULL COMMENT '文件类型，1-创建附件',
                                           `is_del` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0-否，1-是',
                                           `create_by` VARCHAR(64) NULL DEFAULT '' COMMENT '创建者' COLLATE 'utf8mb4_general_ci',
                                           `create_time` DATETIME NOT NULL DEFAULT (CURRENT_TIMESTAMP) COMMENT '创建时间',
                                           `update_by` VARCHAR(64) NULL DEFAULT '' COMMENT '更新者' COLLATE 'utf8mb4_general_ci',
                                           `update_time` DATETIME NOT NULL DEFAULT (CURRENT_TIMESTAMP) ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                           PRIMARY KEY (`id`) USING BTREE
)
    COMMENT='账单附件表'
COLLATE='utf8mb4_general_ci'
ENGINE=InnoDB
;

ALTER TABLE c_customer_service ADD COLUMN `settlement_status` TINYINT NOT NULL DEFAULT '2' COMMENT '结算状态，1-不可结算，2-可结算，3-未结算，4-已结算';

ALTER TABLE sys_dept ADD COLUMN `account_balance` DECIMAL(20,2) NOT NULL DEFAULT 0 COMMENT '余额';
ALTER TABLE sys_dept ADD COLUMN `froze_balance` DECIMAL(20,2) NOT NULL DEFAULT 0 COMMENT '冻结金额' AFTER `account_balance`;

CREATE TABLE `sys_dept_account_balance_detail` (
                                                   `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT 'id',
                                                   `business_dept_id` BIGINT NOT NULL COMMENT '业务公司id',
                                                   `source_type` TINYINT(2) NOT NULL COMMENT '来源类型，1-结算单（新户预收），2-账单（预存抵扣），3-调入，4-拨出',
                                                   `business_no` VARCHAR(50) NULL DEFAULT NULL COMMENT '业务编号',
                                                   `business_type` TINYINT(2) NULL DEFAULT NULL COMMENT '业务类型，1-结算单，2-账单',
                                                   `business_id` BIGINT NULL DEFAULT NULL COMMENT '业务id',
                                                   `income_type` TINYINT(2) NOT NULL COMMENT '收支类型，1-收入，2-支出',
                                                   `change_amount` DECIMAL(20,2) NOT NULL COMMENT '变化金额',
                                                   `status` TINYINT(2) NOT NULL COMMENT '状态，1-待确认，2-已取消，3-已确认',
                                                   `remark` VARCHAR(1000) NULL DEFAULT NULL COMMENT '备注',
                                                   `create_by` VARCHAR(64) NULL DEFAULT '' COMMENT '创建者' COLLATE 'utf8mb4_general_ci',
                                                   `create_time` DATETIME NOT NULL DEFAULT (now()) COMMENT '创建时间',
                                                   `update_by` VARCHAR(64) NULL DEFAULT '' COMMENT '更新者' COLLATE 'utf8mb4_general_ci',
                                                   `update_time` DATETIME NOT NULL DEFAULT (now()) ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                                   PRIMARY KEY (`id`) USING BTREE
)
    COMMENT='业务公司余额变动明细表'
COLLATE='utf8mb4_general_ci'
ENGINE=InnoDB
;

CREATE TABLE `sys_dept_account_balance_detail_file` (
                                                        `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT 'id',
                                                        `balance_detail_id` BIGINT NOT NULL COMMENT '变动明细id',
                                                        `file_url` VARCHAR(255) NOT NULL COMMENT 'url' COLLATE 'utf8mb4_general_ci',
                                                        `file_name` VARCHAR(500) NOT NULL COMMENT '文件名' COLLATE 'utf8mb4_general_ci',
                                                        `file_type` TINYINT NOT NULL COMMENT '文件类型，1-调拨附件',
                                                        `create_by` VARCHAR(64) NULL DEFAULT '' COMMENT '创建者' COLLATE 'utf8mb4_general_ci',
                                                        `create_time` DATETIME NOT NULL DEFAULT (now()) COMMENT '创建时间',
                                                        `update_by` VARCHAR(64) NULL DEFAULT '' COMMENT '更新者' COLLATE 'utf8mb4_general_ci',
                                                        `update_time` DATETIME NOT NULL DEFAULT (now()) ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                                        PRIMARY KEY (`id`) USING BTREE
)
    COMMENT='业务公司余额变动明细附件表'
COLLATE='utf8mb4_general_ci'
ENGINE=InnoDB
;
