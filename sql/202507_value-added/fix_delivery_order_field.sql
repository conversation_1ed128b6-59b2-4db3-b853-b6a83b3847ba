-- 修正增值员工信息表的交付单字段
-- 将 delivery_order_id (bigint) 修改为 delivery_order_no (varchar)

-- 1. 添加新的 delivery_order_no 字段
ALTER TABLE `c_value_added_employee` 
ADD COLUMN `delivery_order_no` varchar(100) COLLATE utf8mb4_general_ci NOT NULL COMMENT '增值交付单编号' AFTER `id`;

-- 2. 如果有数据，可以通过关联表将 delivery_order_id 转换为 delivery_order_no
-- UPDATE c_value_added_employee e 
-- INNER JOIN delivery_order_table d ON e.delivery_order_id = d.id 
-- SET e.delivery_order_no = d.order_no;

-- 3. 删除原有的 delivery_order_id 字段
ALTER TABLE `c_value_added_employee` 
DROP COLUMN `delivery_order_id`;

-- 4. 重新创建索引
ALTER TABLE `c_value_added_employee` 
ADD KEY `idx_delivery_order_biz` (`delivery_order_no`, `biz_type`);

-- 5. 更新业务类型注释，支持个税账号
ALTER TABLE `c_value_added_employee` 
MODIFY COLUMN `biz_type` tinyint NOT NULL COMMENT '业务类型，1-社医保，2-个税明细，3-国税账号，4-个税账号';
