update c_customer_deliver set is_del = 1 where deliver_type = 5;

INSERT INTO `c_customer_deliver_template` (`deliver_type`, `oper_type`, `file_url`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES (5, 11, 'template/confirmTemplate_1.0.0.xlsx', '', '2024-07-31 21:08:16', '', '2024-07-31 21:08:16');
INSERT INTO `c_customer_deliver_template` (`deliver_type`, `oper_type`, `file_url`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES (5, 9, 'template/reportSupplementTemplate_1.0.0.xlsx', '', '2024-07-26 10:48:53', '', '2024-07-26 10:48:53');
INSERT INTO `c_customer_deliver_template` (`deliver_type`, `oper_type`, `file_url`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES (1, 16, 'template/rejectTemplate_1.0.0.xlsx', '', '2024-07-26 10:48:53', '', '2024-07-26 10:48:53');
INSERT INTO `c_customer_deliver_template` (`deliver_type`, `oper_type`, `file_url`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES (2, 16, 'template/rejectTemplate_1.0.0.xlsx', '', '2024-07-26 10:48:53', '', '2024-07-26 10:48:53');
INSERT INTO `c_customer_deliver_template` (`deliver_type`, `oper_type`, `file_url`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES (3, 16, 'template/rejectTemplate_1.0.0.xlsx', '', '2024-07-26 10:48:53', '', '2024-07-26 10:48:53');
INSERT INTO `c_customer_deliver_template` (`deliver_type`, `oper_type`, `file_url`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES (4, 16, 'template/rejectTemplate_1.0.0.xlsx', '', '2024-07-26 10:48:53', '', '2024-07-26 10:48:53');
INSERT INTO `c_customer_deliver_template` (`deliver_type`, `oper_type`, `file_url`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES (5, 16, 'template/rejectTemplate_1.0.0.xlsx', '', '2024-07-26 10:48:53', '', '2024-07-26 10:48:53');
INSERT INTO `c_customer_deliver_template` (`deliver_type`, `oper_type`, `file_url`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES (6, 16, 'template/rejectTemplate_1.0.0.xlsx', '', '2024-07-26 10:48:53', '', '2024-07-26 10:48:53');
INSERT INTO `c_customer_deliver_template` (`deliver_type`, `oper_type`, `file_url`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES (5, 17, 'template/overTemplate_1.0.0.xlsx', '', '2024-07-26 10:48:53', '', '2024-07-26 10:48:53');
INSERT INTO `c_customer_deliver_template` (`deliver_type`, `oper_type`, `file_url`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES (5, 18, 'template/overExceptionTemplate_1.0.0.xlsx', '', '2024-07-26 10:48:53', '', '2024-07-26 10:48:53');


INSERT INTO `c_customer_deliver_template` (`deliver_type`, `oper_type`, `file_url`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES (51, 1, 'template/medicalSocialSecurityCreate_1.0.0.xlsx', '', '2024-07-31 21:08:16', '', '2024-07-31 21:08:16');
INSERT INTO `c_customer_deliver_template` (`deliver_type`, `oper_type`, `file_url`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES (51, 2, 'template/medicalSocialSecurityReport_1.0.0.xlsx', '', '2024-07-26 10:48:53', '', '2024-07-26 10:48:53');
INSERT INTO `c_customer_deliver_template` (`deliver_type`, `oper_type`, `file_url`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES (51, 3, 'template/medicalSocialSecurityDealReportException_1.0.0.xlsx', '', '2024-07-26 10:48:53', '', '2024-07-26 10:48:53');
INSERT INTO `c_customer_deliver_template` (`deliver_type`, `oper_type`, `file_url`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES (51, 4, 'template/medicalSocialSecurityDeduction_1.0.0.xlsx', '', '2024-07-26 10:48:53', '', '2024-07-26 10:48:53');
INSERT INTO `c_customer_deliver_template` (`deliver_type`, `oper_type`, `file_url`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES (51, 5, 'template/medicalSocialSecurityDealDeductionException_1.0.0.xlsx', '', '2024-07-26 10:48:53', '', '2024-07-26 10:48:53');
INSERT INTO `c_customer_deliver_template` (`deliver_type`, `oper_type`, `file_url`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES (51, 6, 'template/medicalSocialSecurityConfirm_1.0.0.xlsx', '', '2024-07-26 10:48:53', '', '2024-07-26 10:48:53');
INSERT INTO `c_customer_deliver_template` (`deliver_type`, `oper_type`, `file_url`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES (51, 7, 'template/medicalSocialSecurityUpdateFiles_1.0.0.xlsx', '', '2024-07-26 10:48:53', '', '2024-07-26 10:48:53');
INSERT INTO `c_customer_deliver_template` (`deliver_type`, `oper_type`, `file_url`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES (51, 8, 'template/medicalSocialSecurityReject_1.0.0.xlsx', '', '2024-07-26 10:48:53', '', '2024-07-26 10:48:53');
