ALTER TABLE c_customer_service_in_account
    ADD COLUMN bank_payment_input_result TINYINT(2) NULL DEFAULT NULL COMMENT '银行流水录入结果,1-正常2-异常3-无需交付4-已开户无流水5-未开户6-银行部分缺' AFTER bank_payment_input_time,
    ADD COLUMN in_account_result TINYINT(2) NULL DEFAULT NULL COMMENT '入账结果，1-正常2-异常3-无需交付4-无账务' AFTER bank_payment_input_result;

UPDATE c_customer_deliver_template SET file_url = 'template/inAccountEditTemplate_1.0.1.xlsx' WHERE id = 42;
ALTER TABLE c_settlement_order_data_temp
    ADD COLUMN `period_bank_payment_input_result` TINYINT NULL DEFAULT NULL COMMENT '银行流水录入结果,1-正常2-异常3-无需交付4-已开户无流水5-未开户6-银行部分缺' AFTER `period_in_account_deliver_result`,
ADD COLUMN `period_in_account_result` TINYINT NULL DEFAULT NULL COMMENT '入账结果，1-正常2-异常3-无需交付4-无账务' AFTER `period_bank_payment_input_result`;
ALTER TABLE c_settlement_order_data
    ADD COLUMN `period_bank_payment_input_result` TINYINT NULL DEFAULT NULL COMMENT '银行流水录入结果,1-正常2-异常3-无需交付4-已开户无流水5-未开户6-银行部分缺' AFTER `period_in_account_deliver_result`,
ADD COLUMN `period_in_account_result` TINYINT NULL DEFAULT NULL COMMENT '入账结果，1-正常2-异常3-无需交付4-无账务' AFTER `period_bank_payment_input_result`;