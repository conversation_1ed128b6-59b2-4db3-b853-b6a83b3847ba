package com.bxm.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum MaterialDeliverAnalysisResult {

    // 解析结果，1-正常，2-异常
    NORMAL(1, "正常"),
    EXCEPTION(2, "异常"),
    UN_KNOW(99, "未知"),
    ;

    private final Integer code;

    private final String name;

    public static MaterialDeliverAnalysisResult getByCode(Integer code) {
        for (MaterialDeliverAnalysisResult item : MaterialDeliverAnalysisResult.values()) {
            if (item.getCode().equals(code)) {
                return item;
            }
        }
        return UN_KNOW;
    }
}
