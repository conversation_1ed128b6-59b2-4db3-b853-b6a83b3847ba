package com.bxm.common.core.utils.poi;

import java.lang.annotation.Annotation;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationHandler;
import java.lang.reflect.Proxy;
import java.util.Map;

public class CommonExcelUtils {
    public static void setAnnotationValue(Annotation annotation, String key, Object newValue) throws Exception {
        InvocationHandler handler = Proxy.getInvocationHandler(annotation);
        Field memberValuesField = handler.getClass().getDeclaredField("memberValues");
        memberValuesField.setAccessible(true);

        // 获取注解的属性键值对
        @SuppressWarnings("unchecked")
        Map<String, Object> memberValues = (Map<String, Object>) memberValuesField.get(handler);

        // 修改属性值
        if (memberValues.containsKey(key)) {
            memberValues.put(key, newValue);
        }
    }
}
