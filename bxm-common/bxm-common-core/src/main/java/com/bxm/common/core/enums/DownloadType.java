package com.bxm.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum DownloadType {

    //下载类型，1-服务-客户列表，2-服务-操作记录，3-服务-账期列表，4-服务-年度汇总，5-服务-补账服务，6-交付-医社保，7-交付-个税（工资薪金），8-个税（经营所得），9-交付-国税，10-交付-医社保 含附件，11-交付-个税（工资薪金）含附件，12-个税（经营所得）含附件，13-交付-国税 含附件，14-交付-入账，15-收入，16-材料-交接，17-材料借阅
    SERVICE_CUSTOMER_LIST(1, "服务-客户列表"),
    SERVICE_OPERATION_RECORD(2, "服务-操作记录"),
    SERVICE_BILL_PERIOD(3, "服务-账期列表"),
    SERVICE_ANNUAL_SUMMARY(4, "服务-年度汇总"),
    SERVICE_REPAIR_SERVICE(5, "服务-补账服务"),
    DELIVERY_MEDICAL_INSURANCE(6, "交付-医社保"),
    DELIVERY_INCOME_TAX(7, "交付-个税（工资薪金）"),
    INCOME_TAX_MANAGEMENT(8, "交付-个税（经营所得）"),
    DELIVERY_NATIONAL_TAX(9, "交付-国税"),
    DELIVERY_MEDICAL_INSURANCE_ATTACHMENT(10, "交付-医社保 含附件"),
    INCOME_TAX_ATTACHMENT(11, "交付-个税（工资薪金）含附件"),
    INCOME_TAX_MANAGEMENT_ATTACHMENT(12, "交付-个税（经营所得）含附件"),
    DELIVERY_NATIONAL_TAX_ATTACHMENT(13, "交付-国税 含附件"),
    DELIVERY_ACCOUNTING(14, "交付-入账"),
    INCOME(15, "收入"),
    MATERIAL_HANDOVER(16, "材料-交接"),
    MATERIAL_BORROWING(17, "材料借阅"),
    DELIVERY_ACCOUNTING_ATTACHMENT(18, "交付-入账 含附件"),
    DELIVERY_PRE_AUTH(19, "交付-预认证"),
    DELIVERY_PRE_AUTH_ATTACHMENT(20, "交付-预认证 含附件"),
    DELIVERY_SETTLE_ACCOUNTS(21, "交付-汇算"),
    DELIVERY_SETTLE_ACCOUNTS_ATTACHMENT(22, "交付-汇算"),
    DELIVERY_ANNUAL_REPORT(23, "交付-年报"),
    DELIVERY_ANNUAL_REPORT_ATTACHMENT(24, "交付-年报 含附件"),
    CUSTOMER_SERVICE_ACCOUNTING_CASHIER(25, "客户账务"),
    CUSTOMER_SERVICE_ACCOUNTING_CASHIER_ATTACHMENT(26, "客户账务 含附件"),
    MATERIAL_DELIVER(27, "材料交接单"),
    MATERIAL_FILES_ATTACHMENT(28, "账期材料"),
    PUSH_ERROR_ATTACHMENT(29, "推送异常数据"),
    DELIVERY_RESIDUAL_BENEFITS(30, "交付-残保金"),
    DELIVERY_RESIDUAL_BENEFITS_ATTACHMENT(31, "交付-残保金 含附件"),
    INCOME_WARNING(32, "收入-预警统计"),
    INCOME_YEAR_WARNING(33, "收入-年统计"),
    OPENAPI_RECORD(34, "第三方通知"),
    RPA_RECORD(35, "rpa记录"),
    BUSINESS_TASK_FOR_PERIOD(36, "账期任务"),
    BUSINESS_TASK_FOR_MANAGER(37, "任务管理"),
    BUSINESS_TASK_FOR_MY(38, "我的任务"),
    WORK_ORDER_ATTACHMENT(39, "工单"),
    DELIVERY_TIMES_REPORT(40, "交付-次报"),
    DELIVERY_TIMES_REPORT_ATTACHMENT(41, "交付-次报"),
    SYNC_ITEM(42, "待申报/待扣款（按税种）"),
    QUALITY_CHECKING_MINILIST(43, "工作台-质检minilist"),
    QUALITY_CHECKING_RESULT(44, "质检结果"),
    QUALITY_CHECKING_RECORD(45, "质检记录"),
    PERIOD_NO_ACCOUNTING_ADVISOR(46, "账期无会计/账期无顾问"),
    ;

    private final Integer code;

    private final String desc;
}
