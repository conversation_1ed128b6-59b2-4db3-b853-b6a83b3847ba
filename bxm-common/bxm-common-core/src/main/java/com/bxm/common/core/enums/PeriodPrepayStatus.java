package com.bxm.common.core.enums;

import com.bxm.common.core.utils.StringUtils;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.stream.Collectors;

@Getter
@AllArgsConstructor
public enum PeriodPrepayStatus {

    // 账期预收状态，1-未预收，2-预收中，3-已预收
    UNPREPAID(1, "未预收"),
    PREPAID(2, "预收中"),
    PREPAID_SUCCESS(3, "已预收"),
    UN_KNOW(99, "未知"),
    ;

    private final Integer code;

    private final String desc;

    public static PeriodPrepayStatus getByCode(Integer code) {
        for (PeriodPrepayStatus periodPrepayStatus : values()) {
            if (periodPrepayStatus.getCode().equals(code)) {
                return periodPrepayStatus;
            }
        }
        return UN_KNOW;
    }

    public static String getMultiNamesByCodes(String codes) {
        if (StringUtils.isEmpty(codes)) {
            return "";
        }
        String[] split = codes.split(",");
        return Arrays.stream(split).map(row -> getByCode(Integer.parseInt(row)).getDesc()).collect(Collectors.joining(","));
    }
}
