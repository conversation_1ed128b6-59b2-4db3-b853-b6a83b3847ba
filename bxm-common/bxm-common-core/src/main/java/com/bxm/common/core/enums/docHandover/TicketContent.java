package com.bxm.common.core.enums.docHandover;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/7/6 23:54
 * happy coding!
 */
@Getter
@AllArgsConstructor
public enum TicketContent {
    UN_KNOW(-1, "未知", Collections.emptyList()),

    BANK(1, "银企", Collections.singletonList(1)),
    PAPER(2, "纸质", Collections.singletonList(2)),
    ALL(99, "纸质+纸质", Arrays.asList(1, 2)),
    ;

    private final Integer code;

    private final String name;

    private final List<Integer> actualCodes;

    public static TicketContent getByCode(Integer source) {
        for (TicketContent item : TicketContent.values()) {
            if (item.getCode().equals(source)) {
                return item;
            }
        }
        return UN_KNOW;
    }
}
