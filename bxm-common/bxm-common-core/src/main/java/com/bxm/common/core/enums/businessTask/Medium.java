package com.bxm.common.core.enums.businessTask;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.util.ObjectUtils;

import java.util.List;
import java.util.stream.Collectors;

@Getter
@AllArgsConstructor
public enum Medium {

    // 介质 1-纸质，2-电子，3-银企
    PAPER(1, "纸质"),
    ELECTRONIC(2, "电子"),
    SILVER(3, "银企"),
    UN_KNOW(99, "未知"),
    ;

    private final Integer code;

    private final String desc;

    public static Medium getByCode(Integer code) {
        for (Medium medium : values()) {
            if (medium.getCode().equals(code)) {
                return medium;
            }
        }
        return UN_KNOW;
    }

    public static String getDescsByCodes(List<Integer> medium) {
        if (ObjectUtils.isEmpty(medium)) {
            return "";
        }
        return medium.stream().map(row -> getByCode(row).getDesc()).collect(Collectors.joining("、"));
    }
}
