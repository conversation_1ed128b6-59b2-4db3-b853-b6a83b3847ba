package com.bxm.common.core.enums.workOrder;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum WorkOrderDispatchType {

    // 分配类型，1-分配给指定组织，2-分配给服务会计，3-分配给服务顾问，4-按发起方分配，工厂发起顾问接收，业务发起工厂接收
    UNKNOWN(99, "未知"),
    ASSIGN_TO_SPECIFIC_ORGANIZATION(1, "分配给指定组织"),
    ASSIGN_TO_SERVICE_ACCOUNTANT(2, "分配给服务会计"),
    ASSIGN_TO_SERVICE_ADVISOR(3, "分配给服务顾问"),
    ASSIGN_TO_INITIATOR(4, "按发起方分配，工厂发起顾问接收，业务发起工厂接收"),
    ;
    private final Integer code;
    private final String desc;
}
