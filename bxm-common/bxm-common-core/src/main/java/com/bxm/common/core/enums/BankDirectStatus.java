package com.bxm.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum BankDirectStatus {

    // 银企直连状态，1-已开通，2-未开通
    OPEN(1, "已开通"),

    NOT_OPEN(2, "未开通"),

    UN_KNOW(99, "未知"),
    ;

    private final Integer code;

    private final String name;

    public static BankDirectStatus getBankDirectStatusByBankDirect(Integer bankDirect) {
        for (BankDirectStatus bankDirectStatus : BankDirectStatus.values()) {
            if (bankDirectStatus.getCode().equals(bankDirect)) {
                return bankDirectStatus;
            }
        }
        return UN_KNOW;
    }
}
