package com.bxm.common.core.enums.inAccount;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/8/17 10:58
 * happy coding!
 */
@Getter
@AllArgsConstructor
public enum InAccountRpaExeResult {
    UN_KNOW(-1, "未知"),

    S(1, "成功"),
    F(0, "失败"),
    ;

    private final Integer code;

    private final String name;

    public static InAccountRpaExeResult getByCode(Integer source) {
        for (InAccountRpaExeResult item : InAccountRpaExeResult.values()) {
            if (item.getCode().equals(source)) {
                return item;
            }
        }
        return UN_KNOW;
    }

    public static InAccountRpaExeResult getByName(String source) {
        for (InAccountRpaExeResult item : InAccountRpaExeResult.values()) {
            if (Objects.equals(source, item.getName())) {
                return item;
            }
        }
        return UN_KNOW;
    }
}
