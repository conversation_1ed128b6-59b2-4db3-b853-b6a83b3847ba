package com.bxm.common.core.enums.inAccount;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;

/**
 * 入账状态，1未入账、2已入账未结账、3已入账已结账
 *
 * <AUTHOR>
 * @date 2024/7/15 16:58
 * happy coding!
 */
@Getter
@AllArgsConstructor
public enum InAccountStatus {
    UN_KNOW(-1, "未知"),

    UN_IN(1, "未入账"),
    IN_UN_DO(2, "已入账未结账"),
    DONE(3, "已入账已结账"),
    ;

    private final Integer code;

    private final String name;

    private static List<Integer> CAN_UPDATE = Arrays.asList(UN_KNOW.getCode(), UN_IN.getCode(), IN_UN_DO.getCode(), DONE.getCode());

    public static InAccountStatus getByCode(Integer source) {
        for (InAccountStatus item : InAccountStatus.values()) {
            if (item.getCode().equals(source)) {
                return item;
            }
        }
        return UN_KNOW;
    }

    public static Boolean canUpdate(Integer code) {
        return CAN_UPDATE.contains(code);
    }
}
