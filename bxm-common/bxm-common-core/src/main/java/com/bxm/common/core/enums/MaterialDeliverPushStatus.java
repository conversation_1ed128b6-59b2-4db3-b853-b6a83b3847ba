package com.bxm.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum MaterialDeliverPushStatus {

    // 推送状态，1-待推送，2-已推送
    WAIT_PUSH(1, "待推送"),
    PUSHED(2, "已推送"),
    UN_KNOW(99, "未知"),
    ;

    private final Integer code;

    private final String name;

    public static MaterialDeliverPushStatus getByCode(Integer code) {
        for (MaterialDeliverPushStatus status : MaterialDeliverPushStatus.values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return UN_KNOW;
    }
}
