package com.bxm.common.core.enums.accountingCashier;

import com.bxm.common.core.utils.StringUtils;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Getter
@AllArgsConstructor
public enum AccountingCashierDeliverStatus {

    // 交付状态，1-待交付，2-交付完成，3-交付异常，4-待重提，5-关闭交付
    WAIT_CREATE(0, "待创建"),
    WAIT_DELIVER(1, "待交付"),
    DELIVER_COMPLETE(2, "已完成"),
    DELIVER_EXCEPTION(3, "异常"),
    WAIT_RESUBMIT(4, "待重提"),
    CLOSE_DELIVER(5, "关闭交付"),
    WAIT_SUBMIT(6, "交付待提交"),
    UN_KNOW(99, "未知"),
    ;

    private final Integer code;

    private final String name;

    public static AccountingCashierDeliverStatus getByCode(Integer code) {
        for (AccountingCashierDeliverStatus item : values()) {
            if (item.getCode().equals(code)) {
                return item;
            }
        }
        return UN_KNOW;
    }

    public static String getMultiNamesByCodes(String codes) {
        if (StringUtils.isEmpty(codes)) {
            return "";
        }
        String[] split = codes.split(",");
        return Arrays.stream(split).map(row -> getByCode(Integer.parseInt(row)).getName()).collect(Collectors.joining(","));
    }

    public static List<Integer> canModifyStatus() {
        return Arrays.asList(WAIT_RESUBMIT.getCode());
    }

    public static List<Integer> canDeleteStatus() {
        return Arrays.asList(WAIT_RESUBMIT.getCode());
    }

    public static List<Integer> canSubmitStatus() {
        return Arrays.asList(WAIT_RESUBMIT.getCode(), WAIT_SUBMIT.getCode());
    }

    public static List<Integer> canWaitSubmitStatus() {
        return Arrays.asList(WAIT_SUBMIT.getCode());
    }

    public static List<Integer> canDeliverStatus() {
        return Arrays.asList(WAIT_DELIVER.getCode(), DELIVER_COMPLETE.getCode(), WAIT_SUBMIT.getCode());
    }

    public static List<Integer> canBatchDeliverStatus() {
        return Arrays.asList(WAIT_DELIVER.getCode(), WAIT_SUBMIT.getCode());
    }

    public static List<Integer> canDealExceptionStatus() {
        return Arrays.asList(DELIVER_EXCEPTION.getCode());
    }

    public static List<Integer> canChangeDeliverStatus() {
        return Arrays.asList(WAIT_DELIVER.getCode(), DELIVER_COMPLETE.getCode(), DELIVER_EXCEPTION.getCode(), WAIT_SUBMIT.getCode());
    }

    public static List<Integer> canUpdateProfitStatus() {
        return Arrays.asList(WAIT_DELIVER.getCode(), DELIVER_COMPLETE.getCode(), DELIVER_EXCEPTION.getCode(), WAIT_SUBMIT.getCode());
    }

    public static List<Integer> canSupplementFileStatus() {
        return Arrays.asList(WAIT_DELIVER.getCode(), DELIVER_EXCEPTION.getCode(), WAIT_SUBMIT.getCode());
    }

    public static List<Integer> canReBackStatus() {
        return Arrays.asList(WAIT_DELIVER.getCode(), WAIT_SUBMIT.getCode());
    }

    public static List<Integer> canCreateTaskStatus() {
        return Arrays.asList(WAIT_DELIVER.getCode(), WAIT_SUBMIT.getCode());
    }

    public static List<Integer> canRpaUpdateStatus() {
        return Arrays.asList(WAIT_DELIVER.getCode(), DELIVER_COMPLETE.getCode(), DELIVER_EXCEPTION.getCode(), WAIT_SUBMIT.getCode());
    }

    public static Integer getDeliverStatusByStatisticType(Integer statisticType) {
        if (statisticType == 3) {
            return WAIT_RESUBMIT.getCode();
        } else if (statisticType == 4) {
            return WAIT_DELIVER.getCode();
        } else if (statisticType == 5) {
            return DELIVER_EXCEPTION.getCode();
        } else if (statisticType == 8) {
            return WAIT_SUBMIT.getCode();
        }
        return null;
    }

    public static Integer getAfterSubmitDeliverStatus(Integer deliverStatus, Integer deliverResult, Integer isClose) {
        if (!Objects.isNull(isClose) && isClose == 1) {
            return DELIVER_COMPLETE.getCode();
        }
        if (Objects.equals(deliverStatus, WAIT_RESUBMIT.getCode())) {
            return WAIT_DELIVER.getCode();
        }
        if (Objects.equals(deliverStatus, WAIT_SUBMIT.getCode())) {
            return Objects.equals(deliverResult, AccountingCashierDeliverResult.EXCEPTION.getCode()) ? DELIVER_EXCEPTION.getCode() : DELIVER_COMPLETE.getCode();
        }
        return null;
    }
}
