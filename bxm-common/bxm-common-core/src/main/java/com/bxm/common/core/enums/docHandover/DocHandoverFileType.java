package com.bxm.common.core.enums.docHandover;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/7/6 10:36
 * happy coding!
 */
@Getter
@AllArgsConstructor
public enum DocHandoverFileType {
    BASE(1, "基础附件"),
    BANK_CHECK_TICKET(2, "对账单附件"),
    BANK_BACK_TICKET(3, "回单附件"),
    TAX_TICKET(4, "税号发票附件"),
    OTHER_TICKET(5, "其他票据附件"),

    OPERATE_VERIFICATION(6, "核验操作的附件"),
    OPERATE_BACK(7, "退回操作的附件"),

    ;

    private final Integer code;

    private final String name;

    public static List<DocHandoverFileType> exceptBase() {
        return Arrays.stream(DocHandoverFileType.values()).filter(row -> !Objects.equals(row, BASE)).collect(Collectors.toList());
    }
}
