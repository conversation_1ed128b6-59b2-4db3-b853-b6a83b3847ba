package com.bxm.common.core.enums;

import com.bxm.common.core.utils.StringUtils;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Getter
@AllArgsConstructor
public enum BankPaymentInputResult {

    // 1-正常2-异常3-无需交付4-已开户无流水5-未开户6-银行部分缺
    NORMAL(1, "正常"),
    EXCEPTION(2, "异常"),
    NO_DELIVER(3, "无需交付"),
    NO_ACCOUNT(4, "已开户无流水"),
    NO_OPEN_ACCOUNT(5, "未开户"),
    PART_MISSING(6, "银行部分缺"),
    UN_KNOW(99, "未知"),
    ;

    private final Integer code;

    private final String name;

    public static BankPaymentInputResult getByCode(Integer code) {
        for (BankPaymentInputResult item : BankPaymentInputResult.values()) {
            if (item.getCode().equals(code)) {
                return item;
            }
        }
        return UN_KNOW;
    }

    public static List<Integer> setBankPaymentInputTimeResult() {
        return Arrays.asList(
                NORMAL.getCode(),
                NO_DELIVER.getCode(),
                NO_ACCOUNT.getCode(),
                NO_OPEN_ACCOUNT.getCode()
        );
    }

    public static BankPaymentInputResult getByName(String source) {
        for (BankPaymentInputResult item : BankPaymentInputResult.values()) {
            if (Objects.equals(source, item.getName())) {
                return item;
            }
        }
        return UN_KNOW;
    }

    public static Integer convertFromFinishResult(Integer finishResult) {
        switch (finishResult) {
            case 1:
                return NORMAL.getCode();
            case 2:
                return NO_ACCOUNT.getCode();
            case 3:
                return NO_OPEN_ACCOUNT.getCode();
            case 4:
                return PART_MISSING.getCode();
            case 5:
                return NO_DELIVER.getCode();
            default:
                return UN_KNOW.getCode();
        }
    }

    public static String getMultiNamesByCodes(String codes) {
        if (StringUtils.isEmpty(codes)) {
            return "";
        }
        String[] split = codes.split(",");
        return Arrays.stream(split).map(row -> getByCode(Integer.parseInt(row)).getName()).collect(Collectors.joining(","));
    }
}
