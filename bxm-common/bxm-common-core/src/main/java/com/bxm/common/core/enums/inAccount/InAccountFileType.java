package com.bxm.common.core.enums.inAccount;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/7/15 19:21
 * happy coding!
 */
@Getter
@AllArgsConstructor
public enum InAccountFileType {
    UN_KNOW(-1, "未知"),
    
    BASE(1, "基础附件"),
    RPA(2, "RPA附件"),
    ;

    private final Integer code;

    private final String name;

    public static InAccountFileType getByCode(Integer source) {
        for (InAccountFileType item : InAccountFileType.values()) {
            if (item.getCode().equals(source)) {
                return item;
            }
        }
        return UN_KNOW;
    }
}
