package com.bxm.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Getter
@AllArgsConstructor
public enum BorrowOrderStatus {

    // 材料借阅单状态，0-待出站，1-待归还，2-待确认，3-已确认，4-无需归还，5-待重提
    WAIT_OUT_STATION(0, "待出站", 5),
    WAIT_RETURN(1, "待归还", 0),
    WAIT_CONFIRM(2, "待确认", 1),
    CONFIRMED(3, "已确认", 2),
    NO_RETURN(4, "无需归还", 1),
    WAIT_RESUBMIT(5, "待重提", null),
    UN_KNOW(99, "未知", null),
    ;

    private final Integer code;

    private final String name;

    // 退回后的状态只
    private final Integer reBackStatus;

    public static BorrowOrderStatus getByCode(Integer code) {
        for (BorrowOrderStatus status : BorrowOrderStatus.values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return UN_KNOW;
    }

    public static List<Integer> canReBackStatus() {
        return Arrays.stream(values()).filter(s -> !Objects.isNull(s.getReBackStatus())).map(BorrowOrderStatus::getCode).collect(Collectors.toList());
    }

    public static List<Integer> canReturnStatus() {
        return Arrays.asList(WAIT_RETURN.getCode(), NO_RETURN.getCode());
    }
}
