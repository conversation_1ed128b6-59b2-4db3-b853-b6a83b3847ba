package com.bxm.common.core.enums.quality;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;

@Getter
@AllArgsConstructor
public enum QualityCheckingType {

    // 质检类型，1-账务问题，2-风险提示，99-未知
    ACCOUNT_PROBLEM(1, "账务问题"),
    RISK_WARNING(2, "风险提示"),
    UNKNOWN(99, "未知");

    private final Integer code;

    private final String name;

    public static QualityCheckingType getByCode(Integer code) {
        for (QualityCheckingType value : QualityCheckingType.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return UNKNOWN;
    }

    public static List<QualityCheckingType> allValidTypes() {
        return Arrays.asList(ACCOUNT_PROBLEM, RISK_WARNING);
    }

    public static List<QualityCheckingType> allValidTypes(Integer deptType) {
        if (deptType == 1) {
            // 业务只可见 风险提示
            return Arrays.asList(RISK_WARNING);
        }
        return allValidTypes();
    }
}
