package com.bxm.common.redis.service;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2022/2/21 13:50:30
 */
@Slf4j
public class PostponeDaemonTask implements Runnable {

    private String key;
    private String value;
    private long expireTime;
    private boolean isRunning;
    private RedisService redisService;

    public PostponeDaemonTask() {
    }

    public PostponeDaemonTask(String key, String value, long expireTime, boolean isRunning, RedisService redisService) {
        this.key = key;
        this.value = value;
        this.expireTime = expireTime;
        this.isRunning = isRunning;
        this.redisService = redisService;
    }

    @Override
    public void run() {
        // 线程等待多长时间后执行
        long waitTime = expireTime * 1000 * 2 / 3;
        while (isRunning) {
            try {
                Thread.sleep(waitTime);
                if (redisService.postpone(key, value, expireTime)) {
                    log.info("redis锁延时成功，{}，{} ", key, value);
                } else {
                    this.stop();
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    private void stop() {
        this.isRunning = false;
    }
}
