<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.bxm</groupId>
        <artifactId>bxm-common</artifactId>
        <version>1.0.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    
    <artifactId>bxm-common-security</artifactId>
    <version>${bxm.common.security.version}</version>
    <description>
        bxm-common-security安全模块
    </description>

    <dependencies>

        <!-- Spring Web -->
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-webmvc</artifactId>
        </dependency>

        <!-- RuoYi Api System -->
        <dependency>
            <groupId>com.bxm</groupId>
            <artifactId>bxm-api-system</artifactId>
            <version>${bxm.api.system.version}</version>
        </dependency>

        <!-- RuoYi Common Redis-->
        <dependency>
            <groupId>com.bxm</groupId>
            <artifactId>bxm-common-redis</artifactId>
            <version>${bxm.common.redis.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bxm</groupId>
            <artifactId>bxm-common-core</artifactId>
        </dependency>

    </dependencies>

    <distributionManagement>
        <repository>
            <id>maven-releases</id>
            <url>http://*************:18081/repository/maven-releases/</url>
        </repository>
        <snapshotRepository>
            <id>maven-snapshots</id>
            <url>http://*************:18081/repository/maven-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>
</project>
