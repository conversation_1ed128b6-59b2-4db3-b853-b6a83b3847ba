package com.bxm.job;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import com.bxm.common.security.annotation.EnableCustomConfig;
import com.bxm.common.security.annotation.EnableJsFeignClients;
import com.bxm.common.swagger.annotation.EnableCustomSwagger2;

/**
 * 定时任务
 * 
 * <AUTHOR>
 */
@EnableCustomConfig
@EnableCustomSwagger2
@EnableJsFeignClients
@SpringBootApplication
public class BxmJobApplication
{
    public static void main(String[] args)
    {
        SpringApplication.run(BxmJobApplication.class, args);
        System.out.println("(♥◠‿◠)ﾉﾞ  定时任务模块启动成功   ");
    }
}
