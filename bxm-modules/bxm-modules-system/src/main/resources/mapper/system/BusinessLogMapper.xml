<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bxm.system.mapper.BusinessLogMapper">
    
    <resultMap type="com.bxm.system.domain.BusinessLog" id="BusinessLogResult">
        <result property="id"    column="id"    />
        <result property="businessType"    column="business_type"    />
        <result property="businessId"    column="business_id"    />
        <result property="operType"    column="oper_type"    />
        <result property="operUserId"    column="oper_user_id"    />
        <result property="operName"    column="oper_name"    />
        <result property="deptName"    column="dept_name"    />
        <result property="operContent"    column="oper_content"    />
        <result property="operRemark"    column="oper_remark"    />
        <result property="operImages"    column="oper_images"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectBusinessLogVo">
        select id, business_type, business_id, oper_type, oper_user_id, oper_name, dept_name, oper_content, oper_remark, oper_images, create_by, create_time, update_by, update_time from sys_business_log
    </sql>

    <select id="selectBusinessLogList" parameterType="com.bxm.system.domain.BusinessLog" resultMap="BusinessLogResult">
        <include refid="selectBusinessLogVo"/>
        <where>  
            <if test="businessType != null "> and business_type = #{businessType}</if>
            <if test="businessId != null "> and business_id = #{businessId}</if>
            <if test="operType != null  and operType != ''"> and oper_type = #{operType}</if>
            <if test="operUserId != null "> and oper_user_id = #{operUserId}</if>
            <if test="operName != null  and operName != ''"> and oper_name like concat('%', #{operName}, '%')</if>
            <if test="deptName != null  and deptName != ''"> and dept_name like concat('%', #{deptName}, '%')</if>
            <if test="operContent != null  and operContent != ''"> and oper_content = #{operContent}</if>
            <if test="operRemark != null  and operRemark != ''"> and oper_remark = #{operRemark}</if>
            <if test="operImages != null  and operImages != ''"> and oper_images = #{operImages}</if>
        </where>
    </select>
    
    <select id="selectBusinessLogById" parameterType="Long" resultMap="BusinessLogResult">
        <include refid="selectBusinessLogVo"/>
        where id = #{id}
    </select>
    <select id="selectCustomerBusinessLog" resultType="com.bxm.system.domain.dto.CustomerBusinessLogDTO">
        select
        sbl.id,
        sbl.business_id as customerServiceId,
        ccs.customer_name as customerName,
        ccs.customer_company_name as customerCompanyName,
        sbl.oper_name as operName,
        sbl.create_time as operTime,
        sbl.oper_type as operType,
        sbl.oper_content as operContent
        from sys_business_log sbl left join c_customer_service ccs
        on sbl.business_id = ccs.id
        <where>
            ccs.is_del = 0 and sbl.business_type = 1 and oper_type != '新增'
            <if test="operName != null and operName != ''">
                and sbl.oper_name like concat('%', #{operName}, '%')
            </if>
            <if test="keyWord != null and keyWord != ''">
                and (ccs.customer_name like concat('%', #{keyWord}, '%') or ccs.customer_company_name like concat('%', #{keyWord}, '%') or ccs.credit_code like concat('%', #{keyWord}, '%'))
            </if>
            <if test="operTimeStart != null and operTimeStart != ''">
                and sbl.create_time &gt;= #{operTimeStart}
            </if>
            <if test="operTimeEnd != null and operTimeEnd != ''">
                and sbl.create_time &lt;= #{operTimeEnd}
            </if>
            <if test="operType != null and operType != ''">
                and sbl.oper_type like concat('%', #{operType}, '%')
            </if>
            <if test="operContent != null and operContent != ''">
                and sbl.oper_content like concat('%', #{operContent}, '%')
            </if>
            <if test="deptDTO.deptIds != null and deptDTO.deptIds.size() > 0">
                <if test="deptDTO.deptType == 1">
                    and (ccs.advisor_dept_id in
                        <foreach collection="deptDTO.deptIds" item="deptId" separator="," open="(" close=")">
                            #{deptId}
                        </foreach>
                    or ccs.advisor_top_dept_id in
                        <foreach collection="deptDTO.deptIds" item="deptId" separator="," open="(" close=")">
                            #{deptId}
                        </foreach>
                    )
                </if>
                <if test="deptDTO.deptType == 2">
                    and (ccs.accounting_dept_id in
                    <foreach collection="deptDTO.deptIds" item="deptId" separator="," open="(" close=")">
                        #{deptId}
                    </foreach>
                    or ccs.accounting_top_dept_id in
                    <foreach collection="deptDTO.deptIds" item="deptId" separator="," open="(" close=")">
                        #{deptId}
                    </foreach>
                    )
                </if>
            </if>
        </where>
        order by sbl.create_time desc,sbl.id desc
    </select>

    <insert id="insertBusinessLog" parameterType="com.bxm.system.domain.BusinessLog" useGeneratedKeys="true" keyProperty="id">
        insert into sys_business_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="businessType != null">business_type,</if>
            <if test="businessId != null">business_id,</if>
            <if test="operType != null and operType != ''">oper_type,</if>
            <if test="operUserId != null">oper_user_id,</if>
            <if test="operName != null">oper_name,</if>
            <if test="deptName != null">dept_name,</if>
            <if test="operContent != null">oper_content,</if>
            <if test="operRemark != null">oper_remark,</if>
            <if test="operImages != null">oper_images,</if>
            <if test="operTimeDifference != null">oper_time_difference,</if>
            <if test="operDeptType != null">oper_dept_type,</if>
            <if test="topDeptId != null">top_dept_id,</if>
            <if test="companyDeptId != null">company_dept_id,</if>
            <if test="lastDeptId != null">last_dept_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="businessType != null">#{businessType},</if>
            <if test="businessId != null">#{businessId},</if>
            <if test="operType != null and operType != ''">#{operType},</if>
            <if test="operUserId != null">#{operUserId},</if>
            <if test="operName != null">#{operName},</if>
            <if test="deptName != null">#{deptName},</if>
            <if test="operContent != null">#{operContent},</if>
            <if test="operRemark != null">#{operRemark},</if>
            <if test="operImages != null">#{operImages},</if>
            <if test="operTimeDifference != null">#{operTimeDifference},</if>
            <if test="operDeptType != null">#{operDeptType},</if>
            <if test="topDeptId != null">#{topDeptId},</if>
            <if test="companyDeptId != null">#{companyDeptId},</if>
            <if test="lastDeptId != null">#{lastDeptId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateBusinessLog" parameterType="com.bxm.system.domain.BusinessLog">
        update sys_business_log
        <trim prefix="SET" suffixOverrides=",">
            <if test="businessType != null">business_type = #{businessType},</if>
            <if test="businessId != null">business_id = #{businessId},</if>
            <if test="operType != null and operType != ''">oper_type = #{operType},</if>
            <if test="operUserId != null">oper_user_id = #{operUserId},</if>
            <if test="operName != null">oper_name = #{operName},</if>
            <if test="deptName != null">dept_name = #{deptName},</if>
            <if test="operContent != null">oper_content = #{operContent},</if>
            <if test="operRemark != null">oper_remark = #{operRemark},</if>
            <if test="operImages != null">oper_images = #{operImages},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBusinessLogById" parameterType="Long">
        delete from sys_business_log where id = #{id}
    </delete>

    <delete id="deleteBusinessLogByIds" parameterType="String">
        delete from sys_business_log where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>