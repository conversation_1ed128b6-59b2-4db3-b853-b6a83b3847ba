<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bxm.system.mapper.SysDeptAccountBalanceDetailMapper">
    
    <resultMap type="com.bxm.system.domain.SysDeptAccountBalanceDetail" id="SysDeptAccountBalanceDetailResult">
        <result property="id"    column="id"    />
        <result property="businessDeptId"    column="business_dept_id"    />
        <result property="sourceType"    column="source_type"    />
        <result property="businessNo"    column="business_no"    />
        <result property="businessType"    column="business_type"    />
        <result property="businessId"    column="business_id"    />
        <result property="incomeType"    column="income_type"    />
        <result property="changeAmount"    column="change_amount"    />
        <result property="status"    column="status"    />
        <result property="remark"    column="remark"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectSysDeptAccountBalanceDetailVo">
        select id, business_dept_id, source_type, business_no, business_type, business_id, income_type, change_amount, status, remark, create_by, create_time, update_by, update_time from sys_dept_account_balance_detail
    </sql>

    <select id="selectSysDeptAccountBalanceDetailList" parameterType="com.bxm.system.domain.SysDeptAccountBalanceDetail" resultMap="SysDeptAccountBalanceDetailResult">
        <include refid="selectSysDeptAccountBalanceDetailVo"/>
        <where>  
            <if test="businessDeptId != null "> and business_dept_id = #{businessDeptId}</if>
            <if test="sourceType != null "> and source_type = #{sourceType}</if>
            <if test="businessNo != null  and businessNo != ''"> and business_no = #{businessNo}</if>
            <if test="businessType != null "> and business_type = #{businessType}</if>
            <if test="businessId != null "> and business_id = #{businessId}</if>
            <if test="incomeType != null "> and income_type = #{incomeType}</if>
            <if test="changeAmount != null "> and change_amount = #{changeAmount}</if>
            <if test="status != null "> and status = #{status}</if>
        </where>
    </select>
    
    <select id="selectSysDeptAccountBalanceDetailById" parameterType="Long" resultMap="SysDeptAccountBalanceDetailResult">
        <include refid="selectSysDeptAccountBalanceDetailVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertSysDeptAccountBalanceDetail" parameterType="com.bxm.system.domain.SysDeptAccountBalanceDetail" useGeneratedKeys="true" keyProperty="id">
        insert into sys_dept_account_balance_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="businessDeptId != null">business_dept_id,</if>
            <if test="sourceType != null">source_type,</if>
            <if test="businessNo != null">business_no,</if>
            <if test="businessType != null">business_type,</if>
            <if test="businessId != null">business_id,</if>
            <if test="incomeType != null">income_type,</if>
            <if test="changeAmount != null">change_amount,</if>
            <if test="status != null">status,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="businessDeptId != null">#{businessDeptId},</if>
            <if test="sourceType != null">#{sourceType},</if>
            <if test="businessNo != null">#{businessNo},</if>
            <if test="businessType != null">#{businessType},</if>
            <if test="businessId != null">#{businessId},</if>
            <if test="incomeType != null">#{incomeType},</if>
            <if test="changeAmount != null">#{changeAmount},</if>
            <if test="status != null">#{status},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateSysDeptAccountBalanceDetail" parameterType="com.bxm.system.domain.SysDeptAccountBalanceDetail">
        update sys_dept_account_balance_detail
        <trim prefix="SET" suffixOverrides=",">
            <if test="businessDeptId != null">business_dept_id = #{businessDeptId},</if>
            <if test="sourceType != null">source_type = #{sourceType},</if>
            <if test="businessNo != null">business_no = #{businessNo},</if>
            <if test="businessType != null">business_type = #{businessType},</if>
            <if test="businessId != null">business_id = #{businessId},</if>
            <if test="incomeType != null">income_type = #{incomeType},</if>
            <if test="changeAmount != null">change_amount = #{changeAmount},</if>
            <if test="status != null">status = #{status},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSysDeptAccountBalanceDetailById" parameterType="Long">
        delete from sys_dept_account_balance_detail where id = #{id}
    </delete>

    <delete id="deleteSysDeptAccountBalanceDetailByIds" parameterType="String">
        delete from sys_dept_account_balance_detail where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>