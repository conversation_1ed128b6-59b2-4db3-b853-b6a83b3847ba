<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bxm.system.mapper.BundleMapper">
    
    <resultMap type="com.bxm.system.domain.Bundle" id="BundleResult">
        <result property="id"    column="id"    />
        <result property="bundleName"    column="bundle_name"    />
        <result property="remark"    column="remark"    />
        <result property="isDel"    column="is_del"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectBundleVo">
        select id, bundle_name, remark, is_del, create_by, create_time, update_by, update_time from sys_bundle
    </sql>

    <select id="selectBundleList" parameterType="com.bxm.system.domain.Bundle" resultMap="BundleResult">
        <include refid="selectBundleVo"/>
        <where>  
            <if test="bundleName != null  and bundleName != ''"> and bundle_name like concat('%', #{bundleName}, '%')</if>
            <if test="isDel != null "> and is_del = #{isDel}</if>
        </where>
    </select>
    
    <select id="selectBundleById" parameterType="Long" resultMap="BundleResult">
        <include refid="selectBundleVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertBundle" parameterType="com.bxm.system.domain.Bundle" useGeneratedKeys="true" keyProperty="id">
        insert into sys_bundle
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="bundleName != null">bundle_name,</if>
            <if test="remark != null">remark,</if>
            <if test="isDel != null">is_del,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="bundleName != null">#{bundleName},</if>
            <if test="remark != null">#{remark},</if>
            <if test="isDel != null">#{isDel},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateBundle" parameterType="com.bxm.system.domain.Bundle">
        update sys_bundle
        <trim prefix="SET" suffixOverrides=",">
            <if test="bundleName != null">bundle_name = #{bundleName},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBundleById" parameterType="Long">
        delete from sys_bundle where id = #{id}
    </delete>

    <delete id="deleteBundleByIds" parameterType="String">
        delete from sys_bundle where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>