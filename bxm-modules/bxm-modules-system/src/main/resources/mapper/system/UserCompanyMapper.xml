<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bxm.system.mapper.UserCompanyMapper">
    
    <resultMap type="com.bxm.system.domain.UserCompany" id="UserCompanyResult">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="companyId"    column="company_id"    />
    </resultMap>

    <sql id="selectUserCompanyVo">
        select id, user_id, company_id from sys_user_company
    </sql>

    <select id="selectUserCompanyList" parameterType="com.bxm.system.domain.UserCompany" resultMap="UserCompanyResult">
        <include refid="selectUserCompanyVo"/>
        <where>  
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="companyId != null "> and company_id = #{companyId}</if>
        </where>
    </select>
    
    <select id="selectUserCompanyById" parameterType="Long" resultMap="UserCompanyResult">
        <include refid="selectUserCompanyVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertUserCompany" parameterType="com.bxm.system.domain.UserCompany" useGeneratedKeys="true" keyProperty="id">
        insert into sys_user_company
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="companyId != null">company_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="companyId != null">#{companyId},</if>
         </trim>
    </insert>

    <update id="updateUserCompany" parameterType="com.bxm.system.domain.UserCompany">
        update sys_user_company
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="companyId != null">company_id = #{companyId},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteUserCompanyById" parameterType="Long">
        delete from sys_user_company where id = #{id}
    </delete>

    <delete id="deleteUserCompanyByIds" parameterType="String">
        delete from sys_user_company where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>