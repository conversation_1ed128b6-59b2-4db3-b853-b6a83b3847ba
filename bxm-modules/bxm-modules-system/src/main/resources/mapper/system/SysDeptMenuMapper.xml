<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bxm.system.mapper.SysDeptMenuMapper">
    
    <resultMap type="com.bxm.system.domain.SysDeptMenu" id="SysDeptMenuResult">
        <result property="deptId"    column="dept_id"    />
        <result property="menuId"    column="menu_id"    />
    </resultMap>

    <sql id="selectSysDeptMenuVo">
        select dept_id, menu_id from sys_dept_menu
    </sql>

    <select id="selectSysDeptMenuList" parameterType="com.bxm.system.domain.SysDeptMenu" resultMap="SysDeptMenuResult">
        <include refid="selectSysDeptMenuVo"/>
        <where>  
        </where>
    </select>
    
    <select id="selectSysDeptMenuByDeptId" parameterType="Long" resultMap="SysDeptMenuResult">
        <include refid="selectSysDeptMenuVo"/>
        where dept_id = #{deptId}
    </select>
        
    <insert id="insertSysDeptMenu" parameterType="com.bxm.system.domain.SysDeptMenu">
        insert into sys_dept_menu
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deptId != null">dept_id,</if>
            <if test="menuId != null">menu_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deptId != null">#{deptId},</if>
            <if test="menuId != null">#{menuId},</if>
         </trim>
    </insert>
    <insert id="saveDeptMenu">
        insert into sys_dept_menu
        select #{deptId}, menu_id
        from sys_bundle_menu where bundle_id = #{bundleId}
        and is_del = 0
    </insert>
    <insert id="saveDeptMenuByDeptIdsAndBundleId">
        insert into sys_dept_menu
        select sys_dept.dept_id, sys_bundle_menu.menu_id
        from sys_dept join sys_bundle_menu on 1 = 1 and sys_bundle_menu.is_del = 0
        where sys_dept.dept_id in
        <foreach item="deptId" collection="deptIds" open="(" separator="," close=")">
            #{deptId}
        </foreach>
        and sys_bundle_menu.bundle_id = #{bundleId}
    </insert>

    <update id="updateSysDeptMenu" parameterType="com.bxm.system.domain.SysDeptMenu">
        update sys_dept_menu
        <trim prefix="SET" suffixOverrides=",">
            <if test="menuId != null">menu_id = #{menuId},</if>
        </trim>
        where dept_id = #{deptId}
    </update>

    <delete id="deleteSysDeptMenuByDeptId" parameterType="Long">
        delete from sys_dept_menu where dept_id = #{deptId}
    </delete>

    <delete id="deleteSysDeptMenuByDeptIds" parameterType="String">
        delete from sys_dept_menu where dept_id in 
        <foreach item="deptId" collection="array" open="(" separator="," close=")">
            #{deptId}
        </foreach>
    </delete>
    <delete id="deleteByDeptIds">
        delete from sys_dept_menu
        where dept_id in
        <foreach item="deptId" collection="deptIds" open="(" separator="," close=")">
            #{deptId}
        </foreach>
    </delete>
</mapper>