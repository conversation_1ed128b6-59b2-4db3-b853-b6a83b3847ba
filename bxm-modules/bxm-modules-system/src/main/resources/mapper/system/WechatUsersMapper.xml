<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bxm.system.mapper.WechatUsersMapper">
    
    <resultMap type="com.bxm.system.domain.WechatUsers" id="WechatUsersResult">
        <result property="id"    column="id"    />
        <result property="corpId"    column="corp_id"    />
        <result property="userId"    column="user_id"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectWechatUsersVo">
        select id, corp_id, user_id, create_by, create_time, update_by, update_time from t_wechat_users
    </sql>

    <select id="selectWechatUsersList" parameterType="com.bxm.system.domain.WechatUsers" resultMap="WechatUsersResult">
        <include refid="selectWechatUsersVo"/>
        <where>  
            <if test="corpId != null  and corpId != ''"> and corp_id = #{corpId}</if>
            <if test="userId != null  and userId != ''"> and user_id = #{userId}</if>
        </where>
    </select>
    
    <select id="selectWechatUsersById" parameterType="Long" resultMap="WechatUsersResult">
        <include refid="selectWechatUsersVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertWechatUsers" parameterType="com.bxm.system.domain.WechatUsers" useGeneratedKeys="true" keyProperty="id">
        insert into t_wechat_users
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="corpId != null and corpId != ''">corp_id,</if>
            <if test="userId != null and userId != ''">user_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="corpId != null and corpId != ''">#{corpId},</if>
            <if test="userId != null and userId != ''">#{userId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateWechatUsers" parameterType="com.bxm.system.domain.WechatUsers">
        update t_wechat_users
        <trim prefix="SET" suffixOverrides=",">
            <if test="corpId != null and corpId != ''">corp_id = #{corpId},</if>
            <if test="userId != null and userId != ''">user_id = #{userId},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteWechatUsersById" parameterType="Long">
        delete from t_wechat_users where id = #{id}
    </delete>

    <delete id="deleteWechatUsersByIds" parameterType="String">
        delete from t_wechat_users where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>