package com.bxm.system.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bxm.system.api.domain.dept.UserDeptDTO;
import com.bxm.system.domain.dto.CustomerBusinessLogDTO;
import org.apache.ibatis.annotations.Mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bxm.system.domain.BusinessLog;
import org.apache.ibatis.annotations.Param;

/**
 * 业务统一操作记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-05-09
 */
@Mapper
public interface BusinessLogMapper extends BaseMapper<BusinessLog>
{
    /**
     * 查询业务统一操作记录
     * 
     * @param id 业务统一操作记录主键
     * @return 业务统一操作记录
     */
    public BusinessLog selectBusinessLogById(Long id);

    /**
     * 查询业务统一操作记录列表
     * 
     * @param businessLog 业务统一操作记录
     * @return 业务统一操作记录集合
     */
    public List<BusinessLog> selectBusinessLogList(BusinessLog businessLog);

    /**
     * 新增业务统一操作记录
     * 
     * @param businessLog 业务统一操作记录
     * @return 结果
     */
    public int insertBusinessLog(BusinessLog businessLog);

    /**
     * 修改业务统一操作记录
     * 
     * @param businessLog 业务统一操作记录
     * @return 结果
     */
    public int updateBusinessLog(BusinessLog businessLog);

    /**
     * 删除业务统一操作记录
     * 
     * @param id 业务统一操作记录主键
     * @return 结果
     */
    public int deleteBusinessLogById(Long id);

    /**
     * 批量删除业务统一操作记录
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteBusinessLogByIds(Long[] ids);

    List<CustomerBusinessLogDTO> selectCustomerBusinessLog(IPage<CustomerBusinessLogDTO> iPage,
                                                           @Param("operName") String operName, @Param("keyWord") String keyWord,
                                                           @Param("operTimeStart") String operTimeStart, @Param("operTimeEnd") String operTimeEnd,
                                                           @Param("operType") String operType, @Param("deptDTO") UserDeptDTO deptDTO, @Param("operContent") String operContent);
}
