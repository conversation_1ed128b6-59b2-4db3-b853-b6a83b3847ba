package com.bxm.system.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.bxm.common.core.constant.CacheConstants;
import com.bxm.common.core.enums.BusinessLogBusinessType;
import com.bxm.common.core.enums.DeliverStatus;
import com.bxm.common.core.exception.ServiceException;
import com.bxm.common.core.utils.ServletUtils;
import com.bxm.common.core.web.domain.CommonFileVO;
import com.bxm.common.redis.service.RedisService;
import com.bxm.common.security.utils.SecurityUtils;
import com.bxm.file.api.RemoteFileService;
import com.bxm.file.api.domain.RemoteAliFileDTO;
import com.bxm.system.api.domain.*;
import com.bxm.system.domain.BusinessLog;
import com.bxm.system.domain.WechatAppConfig;
import com.bxm.system.domain.WechatUserBinding;
import com.bxm.system.domain.WechatUsers;
import com.bxm.system.domain.dto.companyWechat.CompanyWechatBindDTO;
import com.bxm.system.domain.dto.companyWechat.CompanyWechatDeptInfo;
import com.bxm.system.domain.dto.companyWechat.WechatFileResultDTO;
import com.bxm.system.domain.vo.companyWechat.SendMessageVO;
import com.bxm.thirdpart.api.RemoteThirdpartService;
import com.bxm.thirdpart.api.domain.WechatSendMessageVO;
import com.bxm.thirdpart.api.domain.WechatUserInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class CompanyWechatService {

    @Autowired
    private ISysUserService sysUserService;

    @Autowired
    private IWechatUsersService wechatUsersService;

    @Autowired
    private IWechatUserBindingService wechatUserBindingService;

    @Autowired
    private IWechatAppConfigService wechatAppConfigService;

    @Autowired
    private RemoteThirdpartService remoteThirdpartService;

    @Autowired
    private RemoteFileService remoteFileService;

    @Autowired
    private ISysDeptService sysDeptService;

    @Autowired
    private IBusinessLogService businessLogService;

    @Autowired
    private IUserMessageService userMessageService;

    @Autowired
    private RedisService redisService;

    public CompanyWechatDeptInfo bindUser(CompanyWechatBindDTO dto) {
        // 验证账号密码
        SysUser user = sysUserService.selectUserByUserName(dto.getUsername());
        if (user == null) {
            throw new ServiceException("用户名或密码错误");
        }
        if (!SecurityUtils.matchesPassword(dto.getPassword(), user.getPassword())) {
            throw new ServiceException("用户名或密码错误");
        }
        WechatAppConfig config = wechatAppConfigService.getOne(new LambdaQueryWrapper<WechatAppConfig>().eq(WechatAppConfig::getCorpId, dto.getCorpId()));
        if (Objects.isNull(config)) {
            throw new ServiceException("配置不存在");
        }
        if (wechatUserBindingService.count(new LambdaQueryWrapper<WechatUserBinding>().eq(WechatUserBinding::getSystemUserId, user.getUserId())) > 0) {
            throw new ServiceException("该账号已绑定其他企微账号，得由其他企微账号解绑才可以再绑定");
        }
        // 获取企业微信用户信息
        WechatUserInfo userInfo = dto.getWechatUserInfo();
        if (Objects.isNull(userInfo)) {
            throw new ServiceException("企业微信用户不存在");
        }

        // 查找或创建企业微信用户
        WechatUsers weChatUser = wechatUsersService.getOne(new QueryWrapper<WechatUsers>().eq("corp_id", dto.getCorpId()).eq("user_id", userInfo.getUserid()));
        if (weChatUser == null) {
            weChatUser = new WechatUsers();
            weChatUser.setCorpId(dto.getCorpId());
            weChatUser.setUserId(userInfo.getUserid());
            wechatUsersService.save(weChatUser);
        }

        // 绑定用户信息
        WechatUserBinding binding = new WechatUserBinding();
        binding.setWechatUserId(weChatUser.getId());
        binding.setSystemUserId(user.getUserId());
        wechatUserBindingService.save(binding);

        return CompanyWechatDeptInfo.builder()
                .userName(user.getUserName())
                .userId(user.getUserId())
                .deptNameList(sysDeptService.getAllUserDeptNames(user))
                .build();
    }

    public void sendMessage(SendMessageVO vo) {
        if (ObjectUtils.isEmpty(vo.getUserIds())) {
            return;
        }
        List<WechatUserBinding> binds = wechatUserBindingService.list(new QueryWrapper<WechatUserBinding>().in("system_user_id", vo.getUserIds()));
        if (ObjectUtils.isEmpty(binds)) {
            return;
        }
        Long userId = SecurityUtils.getUserId();
        Long deptId = Long.parseLong(ServletUtils.getRequest().getHeader("deptId"));
        List<SysEmployee> employees = sysDeptService.getEmployeesBySecondDeptIdAndUserId(deptId, userId);
        String operName = ObjectUtils.isEmpty(employees) ? SecurityUtils.getLoginUser().getSysUser().getNickName() : employees.get(0).getEmployeeName();
        List<WechatUsers> wechatUsers = wechatUsersService.list(new LambdaQueryWrapper<WechatUsers>()
                .in(WechatUsers::getId, binds.stream().map(WechatUserBinding::getWechatUserId).collect(Collectors.toList())));
        Map<String, List<WechatUsers>> wechatUsersCoprIdMap = wechatUsers.stream().collect(Collectors.groupingBy(WechatUsers::getCorpId));
        Map<String, WechatAppConfig> configMap = wechatAppConfigService.selectAll()
                .stream().collect(Collectors.toMap(WechatAppConfig::getCorpId, Function.identity()));
        wechatUsersCoprIdMap.forEach((corpId, wechatUserList) -> {
            WechatAppConfig config = configMap.get(corpId);
            if (!Objects.isNull(config)) {
                String wechatUserIds = wechatUserList.stream().map(WechatUsers::getUserId).collect(Collectors.joining("|"));
                // 发送一条markdown消息
                WechatSendMessageVO sendMessageVO = WechatSendMessageVO.builder()
                        .content(buildMarkdownMessage(vo, wechatUserIds, config.getAgentId(), operName))
                        .corpId(corpId)
                        .corpSecret(config.getCorpSecret())
                        .build();
                remoteThirdpartService.sendCompanyWechatMessage(sendMessageVO);
                if (!ObjectUtils.isEmpty(vo.getFiles())) {
                    vo.getFiles().forEach(file -> {
                        WechatSendMessageVO sendFileMessageVO = WechatSendMessageVO.builder()
                                .content(buildFileMessage(wechatUserIds, config.getAgentId(), file.getWechatFileId()))
                                .corpId(corpId)
                                .corpSecret(config.getCorpSecret())
                                .build();
                        remoteThirdpartService.sendCompanyWechatMessage(sendFileMessageVO);
                    });
                }
            }
        });
        if (Objects.equals(vo.getBusinessType(), BusinessLogBusinessType.CUSTOMER_SERVICE_DELIVER.getCode())) {
            Map<String, String> operContent = new HashMap<>();
            operContent.put("发送内容", vo.getContent());
            businessLogService.save(new BusinessLog().setBusinessId(vo.getBusinessId())
                    .setBusinessType(vo.getBusinessType())
                    .setOperUserId(userId)
                    .setOperName(ObjectUtils.isEmpty(employees) ? SecurityUtils.getLoginUser().getSysUser().getNickName() : employees.get(0).getEmployeeName())
                    .setOperImages(ObjectUtils.isEmpty(vo.getFiles()) ? "" : JSONArray.toJSONString(vo.getFiles().stream().map(file -> CommonFileVO.builder().fileUrl(file.getFileUrl()).fileName(file.getFileName()).build()).collect(Collectors.toList())))
                    .setOperType("催办")
                    .setOperContent(JSONObject.toJSONString(operContent)));
        } else {
            businessLogService.save(new BusinessLog().setBusinessId(vo.getBusinessId())
                    .setBusinessType(vo.getBusinessType())
                    .setOperUserId(userId)
                    .setOperName(ObjectUtils.isEmpty(employees) ? SecurityUtils.getLoginUser().getSysUser().getNickName() : employees.get(0).getEmployeeName())
                    .setOperImages(ObjectUtils.isEmpty(vo.getFiles()) ? "" : JSONArray.toJSONString(vo.getFiles().stream().map(file -> CommonFileVO.builder().fileUrl(file.getFileUrl()).fileName(file.getFileName()).build()).collect(Collectors.toList())))
                    .setOperType("催办")
                    .setOperContent(vo.getContent()));
        }
    }

    private String buildMarkdownMessage(SendMessageVO vo, String wechatUserId, String agentId, String operName) {
        Map<String, Object> map = new HashMap<>();
        map.put("touser", wechatUserId);
        map.put("msgtype", "markdown");
        map.put("agentid", agentId);
        Map<String, String> contentMap = new HashMap<>();
        contentMap.put("content", String.format("消息发送人：%s</br>关联数据：%s</br>通知内容：%s", operName, vo.getRelationContent(), vo.getContent()));
        map.put("markdown", contentMap);
        return JSONObject.toJSONString(map);
    }

    private String buildMarkdownMessageV2(SendMessageVO vo, String wechatUserId, String agentId, String operName) {
        Map<String, Object> map = new HashMap<>();
        map.put("touser", wechatUserId);
        map.put("msgtype", "markdown");
        map.put("agentid", agentId);
        Map<String, String> contentMap = new HashMap<>();
        contentMap.put("content", String.format("消息发送人：%s</br>>通知内容：%s", operName, vo.getContent()));
        map.put("markdown", contentMap);
        return JSONObject.toJSONString(map);
    }

    private String buildTextMessage(SendMessageVO vo, String wechatUserId, String agentId) {
        Map<String, Object> map = new HashMap<>();
        map.put("touser", wechatUserId);
        map.put("msgtype", "text");
        map.put("agentid", agentId);
        Map<String, String> contentMap = new HashMap<>();
        contentMap.put("content", vo.getContent());
        map.put("text", contentMap);
        return JSONObject.toJSONString(map);
    }

    private String buildFileMessage(String wechatUserId, String agentId, String mediaId) {
        Map<String, Object> map = new HashMap<>();
        map.put("touser", wechatUserId);
        map.put("msgtype", "file");
        map.put("agentid", agentId);
        Map<String, String> contentMap = new HashMap<>();
        contentMap.put("media_id", mediaId);
        map.put("file", contentMap);
        return JSONObject.toJSONString(map);
    }

    public CompanyWechatDeptInfo getBindInfoByCode(CompanyWechatBindDTO dto) {
        WechatAppConfig config = wechatAppConfigService.getOne(new LambdaQueryWrapper<WechatAppConfig>().eq(WechatAppConfig::getCorpId, dto.getCorpId()));
        if (Objects.isNull(config)) {
            throw new ServiceException("配置不存在");
        }
        // 获取企业微信用户信息
        WechatUserInfo userInfo = dto.getWechatUserInfo();
        if (Objects.isNull(userInfo)) {
            throw new ServiceException("企业微信用户不存在");
        }
        // 查找或创建企业微信用户
        WechatUsers weChatUser = wechatUsersService.getOne(new QueryWrapper<WechatUsers>().eq("corp_id", dto.getCorpId()).eq("user_id", userInfo.getUserid()));
        if (weChatUser == null) {
            weChatUser = new WechatUsers();
            weChatUser.setCorpId(dto.getCorpId());
            weChatUser.setUserId(userInfo.getUserid());
            wechatUsersService.save(weChatUser);
        }
        WechatUserBinding bind = wechatUserBindingService.getOne(new LambdaQueryWrapper<WechatUserBinding>().eq(WechatUserBinding::getWechatUserId, weChatUser.getId()), false);
        if (Objects.isNull(bind)) {
            throw new ServiceException("未绑定系统用户");
        }
        SysUser user = sysUserService.getById(bind.getSystemUserId());
        return CompanyWechatDeptInfo.builder()
                .userName(user.getUserName())
                .userId(user.getUserId())
                .deptNameList(sysDeptService.getAllUserDeptNames(user))
                .build();
    }

    public CompanyWechatDeptInfo getBindInfoByUserId(Long userId) {
        SysUser user = sysUserService.getById(userId);
        return CompanyWechatDeptInfo.builder()
                .userName(user.getUserName())
                .userId(userId)
                .deptNameList(sysDeptService.getAllUserDeptNames(user))
                .build();
    }

    public void unBind(Long userId) {
        WechatUserBinding bind = wechatUserBindingService.getOne(new LambdaQueryWrapper<WechatUserBinding>().eq(WechatUserBinding::getSystemUserId, userId), false);
        if (!Objects.isNull(bind)) {
            wechatUserBindingService.removeById(bind.getId());
            wechatUsersService.removeById(bind.getWechatUserId());
        }
    }

    public WechatFileResultDTO uploadCompanyWechatFile(MultipartFile file, Long userId) {
        SysUser user = sysUserService.getById(userId);
        if (Objects.isNull(user) || Objects.equals("2", user.getDelFlag())) {
            throw new ServiceException("用户不存在");
        }
        WechatUserBinding bind = wechatUserBindingService.getOne(new QueryWrapper<WechatUserBinding>().eq("system_user_id", userId), false);
        if (Objects.isNull(bind)) {
            throw new ServiceException("未绑定企业微信");
        }
        WechatUsers wechatUsers = wechatUsersService.getOne(new LambdaQueryWrapper<WechatUsers>()
                .in(WechatUsers::getId, bind.getWechatUserId()), false);
        if (Objects.isNull(wechatUsers)) {
            throw new ServiceException("数据异常！");
        }
        WechatAppConfig config = wechatAppConfigService.getOne(new LambdaQueryWrapper<WechatAppConfig>()
                .eq(WechatAppConfig::getCorpId, wechatUsers.getCorpId()), false);
        if (Objects.isNull(config)) {
            throw new ServiceException("配置不存在");
        }
        RemoteAliFileDTO fileDTO = remoteFileService.uploadFile(file).getDataThrowException();
        String wechatFileResult = remoteThirdpartService.uploadFileToCompanyWechat(file, wechatUsers.getCorpId(), config.getCorpSecret()).getDataThrowException();
        return WechatFileResultDTO.builder()
                .fileName(fileDTO.getFileName())
                .fileUrl(fileDTO.getUrl())
                .fullFileUrl(fileDTO.getFullUrl())
                .wechatFileId(wechatFileResult)
                .build();
    }

    public void sendTextMessage(SendMessageVO vo) {
        if (ObjectUtils.isEmpty(vo.getUserIds())) {
            return;
        }
        List<WechatUserBinding> binds = wechatUserBindingService.list(new QueryWrapper<WechatUserBinding>().in("system_user_id", vo.getUserIds()));
        if (ObjectUtils.isEmpty(binds)) {
            return;
        }
        List<WechatUsers> wechatUsers = wechatUsersService.list(new LambdaQueryWrapper<WechatUsers>()
                .in(WechatUsers::getId, binds.stream().map(WechatUserBinding::getWechatUserId).collect(Collectors.toList())));
        Map<String, List<WechatUsers>> wechatUsersCoprIdMap = wechatUsers.stream().collect(Collectors.groupingBy(WechatUsers::getCorpId));
        Map<String, WechatAppConfig> configMap = wechatAppConfigService.selectAll()
                .stream().collect(Collectors.toMap(WechatAppConfig::getCorpId, Function.identity()));
        wechatUsersCoprIdMap.forEach((corpId, wechatUserList) -> {
            WechatAppConfig config = configMap.get(corpId);
            if (!Objects.isNull(config)) {
                String wechatUserIds = wechatUserList.stream().map(WechatUsers::getUserId).collect(Collectors.joining("|"));
                // 发送一条文本消息
                WechatSendMessageVO sendMessageVO = WechatSendMessageVO.builder()
                        .content(buildTextMessage(vo, wechatUserIds, config.getAgentId()))
                        .corpId(corpId)
                        .corpSecret(config.getCorpSecret())
                        .build();
                remoteThirdpartService.sendCompanyWechatMessage(sendMessageVO);
            }
        });
    }

    public WechatUserInfo getWechatUserInfo(String code, String corpId) {
        WechatAppConfig config = wechatAppConfigService.getOne(new LambdaQueryWrapper<WechatAppConfig>().eq(WechatAppConfig::getCorpId, corpId));
        if (Objects.isNull(config)) {
            throw new ServiceException("配置不存在");
        }
        WechatUserInfo userInfo = remoteThirdpartService.getCompanyWechatUserInfo(code, corpId, config.getCorpSecret()).getDataThrowException();
        // 查找或创建企业微信用户
        WechatUsers weChatUser = wechatUsersService.getOne(new QueryWrapper<WechatUsers>().eq("corp_id", corpId).eq("user_id", userInfo.getUserid()));
        if (weChatUser == null) {
            weChatUser = new WechatUsers();
            weChatUser.setCorpId(corpId);
            weChatUser.setUserId(userInfo.getUserid());
            wechatUsersService.save(weChatUser);
        }
        return userInfo;
    }

    @Transactional
    public void urge(SendMessageVO vo) {
        if (!redisService.lockNotWait(CacheConstants.URGE_LOCK_KEY + vo.getBusinessId(), SecurityUtils.getUserId().toString(), 60 * 5)) {
            throw new ServiceException("5分钟内只能发起一次催办");
        }
        if (ObjectUtils.isEmpty(vo.getUserIds())) {
            return;
        }
        List<WechatUserBinding> binds = wechatUserBindingService.list(new QueryWrapper<WechatUserBinding>().in("system_user_id", vo.getUserIds()));
        if (ObjectUtils.isEmpty(binds)) {
            return;
        }
        Long userId = SecurityUtils.getUserId();
        Long deptId = Long.parseLong(ServletUtils.getRequest().getHeader("deptId"));
        List<SysEmployee> employees = sysDeptService.getEmployeesBySecondDeptIdAndUserId(deptId, userId);
        String operName = ObjectUtils.isEmpty(employees) ? SecurityUtils.getLoginUser().getSysUser().getNickName() : employees.get(0).getEmployeeName();
        List<WechatUsers> wechatUsers = wechatUsersService.list(new LambdaQueryWrapper<WechatUsers>()
                .in(WechatUsers::getId, binds.stream().map(WechatUserBinding::getWechatUserId).collect(Collectors.toList())));
        Map<String, List<WechatUsers>> wechatUsersCoprIdMap = wechatUsers.stream().collect(Collectors.groupingBy(WechatUsers::getCorpId));
        Map<String, WechatAppConfig> configMap = wechatAppConfigService.selectAll()
                .stream().collect(Collectors.toMap(WechatAppConfig::getCorpId, Function.identity()));
        wechatUsersCoprIdMap.forEach((corpId, wechatUserList) -> {
            WechatAppConfig config = configMap.get(corpId);
            if (!Objects.isNull(config)) {
                String wechatUserIds = wechatUserList.stream().map(WechatUsers::getUserId).collect(Collectors.joining("|"));
                // 发送一条markdown消息
                WechatSendMessageVO sendMessageVO = WechatSendMessageVO.builder()
                        .content(buildMarkdownMessageV2(vo, wechatUserIds, config.getAgentId(), operName))
                        .corpId(corpId)
                        .corpSecret(config.getCorpSecret())
                        .build();
                remoteThirdpartService.sendCompanyWechatMessage(sendMessageVO);
            }
        });
        Map<String, String> operContent = new HashMap<>();
        operContent.put("发送内容", vo.getContent());
        businessLogService.save(new BusinessLog().setBusinessId(vo.getBusinessId())
                .setBusinessType(BusinessLogBusinessType.CUSTOMER_SERVICE_DELIVER.getCode())
                .setOperUserId(userId)
                .setOperName(ObjectUtils.isEmpty(employees) ? SecurityUtils.getLoginUser().getSysUser().getNickName() : employees.get(0).getEmployeeName())
                .setOperImages(ObjectUtils.isEmpty(vo.getFiles()) ? "" : JSONArray.toJSONString(vo.getFiles().stream().map(file -> CommonFileVO.builder().fileUrl(file.getFileUrl()).fileName(file.getFileName()).build()).collect(Collectors.toList())))
                .setOperType("催办")
                .setOperContent(JSONObject.toJSONString(operContent)));
    }

    @Transactional
    public void urgeV2(SendMessageVO vo) {
        if (!redisService.lockNotWait(CacheConstants.URGE_LOCK_KEY + vo.getBusinessId() + ":" + vo.getDeliverStatus(), SecurityUtils.getUserId().toString(), 60 * 5)) {
            throw new ServiceException("5分钟内只能发起一次催办");
        }
        if (ObjectUtils.isEmpty(vo.getUserIds())) {
            return;
        }
        Long userId = SecurityUtils.getUserId();
        List<Long> userIds = vo.getUserIds().stream().filter(uId -> !Objects.equals(uId, userId)).collect(Collectors.toList());
        if (ObjectUtils.isEmpty(userIds)) {
            return;
        }

        Long deptId = Long.parseLong(ServletUtils.getRequest().getHeader("deptId"));
        List<SysEmployee> employees = sysDeptService.getEmployeesBySecondDeptIdAndUserId(deptId, userId);
        SysEmployee currentEmployee = ObjectUtils.isEmpty(employees) ? null : employees.get(0);
        String currentDeptName = Objects.isNull(currentEmployee) ? "" : sysDeptService.getById(currentEmployee.getDeptId()).getDeptName();
        String operName = ObjectUtils.isEmpty(employees) ? SecurityUtils.getLoginUser().getSysUser().getNickName() : employees.get(0).getEmployeeName();
        Map<Long, String> userMap = sysUserService.getBaseMapper().selectBatchIds(userIds).stream().collect(Collectors.toMap(SysUser::getUserId, SysUser::getNickName));
        userMessageService.sendMessage(userIds.stream().map(receiveUserId -> RemoteUserMessageVO.builder()
                .receiveDeptId(vo.getDeptId()).receiveDeptName(vo.getDeptName()).receiveUserId(receiveUserId)
                .sendDeptId(Objects.isNull(currentEmployee) ? null : currentEmployee.getDeptId()).sendDeptName(currentDeptName).sendEmployeeId(Objects.isNull(currentEmployee) ? null : currentEmployee.getEmployeeId()).sendEmployeeName(operName)
                .content(vo.getContent())
                .messageType(1)
                .receiveEmployeeName(userMap.getOrDefault(receiveUserId, ""))
                .build()).collect(Collectors.toList()));
        Map<String, String> operContent = new HashMap<>();
        operContent.put("发送内容", vo.getContent());
        businessLogService.save(new BusinessLog().setBusinessId(vo.getBusinessId())
                .setBusinessType(BusinessLogBusinessType.CUSTOMER_SERVICE_DELIVER.getCode())
                .setOperUserId(userId)
                .setOperName(ObjectUtils.isEmpty(employees) ? SecurityUtils.getLoginUser().getSysUser().getNickName() : employees.get(0).getEmployeeName())
                .setOperImages(ObjectUtils.isEmpty(vo.getFiles()) ? "" : JSONArray.toJSONString(vo.getFiles().stream().map(file -> CommonFileVO.builder().fileUrl(file.getFileUrl()).fileName(file.getFileName()).build()).collect(Collectors.toList())))
                .setOperType("催办")
                .setOperContent(JSONObject.toJSONString(operContent)));
    }
}
