package com.bxm.system.domain.dto.role;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RoleDetailDTO {

    @ApiModelProperty("角色id")
    private Long id;

    @ApiModelProperty("角色名")
    private String roleName;

    @ApiModelProperty("角色类型，SYSTEM-系统，CUSTOMIZE-自定义")
    private String roleType;

    @ApiModelProperty("角色code")
    private String roleKey;

    @ApiModelProperty("可用组织类型，1-业务公司，2-会计工厂")
    private List<Integer> deptTypes;

    @ApiModelProperty("归属集团id")
    private Long belongDeptId;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("选择的菜单id列表")
    private List<Long> menuIds;
}
