package com.bxm.system.controller;

import com.bxm.common.core.utils.poi.ExcelUtil;
import com.bxm.common.core.web.controller.BaseController;
import com.bxm.common.core.web.domain.AjaxResult;
import com.bxm.common.core.web.page.TableDataInfo;
import com.bxm.common.log.annotation.Log;
import com.bxm.common.log.enums.BusinessType;
import com.bxm.common.security.annotation.RequiresPermissions;
import com.bxm.system.domain.SysDeptMenu;
import com.bxm.system.service.ISysDeptMenuService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 组织和菜单关联Controller
 * 
 * <AUTHOR>
 * @date 2024-05-29
 */
@RestController
@RequestMapping("/deptMenu")
@Api(tags = "组织和菜单关联")
public class SysDeptMenuController extends BaseController
{
    @Autowired
    private ISysDeptMenuService sysDeptMenuService;

    /**
     * 查询组织和菜单关联列表
     */
    @RequiresPermissions("customer:menu:list")
    @GetMapping("/list")
    @ApiOperation(value = "查询组织和菜单关联列表", notes = "查询组织和菜单关联列表")
    public TableDataInfo list(SysDeptMenu sysDeptMenu)
    {
        startPage();
        List<SysDeptMenu> list = sysDeptMenuService.selectSysDeptMenuList(sysDeptMenu);
        return getDataTable(list);
    }

    /**
     * 导出组织和菜单关联列表
     */
    @RequiresPermissions("customer:menu:export")
    @Log(title = "组织和菜单关联", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation(value = "导出组织和菜单关联列表", notes = "导出组织和菜单关联列表")
    public void export(HttpServletResponse response, SysDeptMenu sysDeptMenu)
    {
        List<SysDeptMenu> list = sysDeptMenuService.selectSysDeptMenuList(sysDeptMenu);
        ExcelUtil<SysDeptMenu> util = new ExcelUtil<SysDeptMenu>(SysDeptMenu.class);
        util.exportExcel(response, list, "组织和菜单关联数据");
    }

    /**
     * 获取组织和菜单关联详细信息
     */
    @RequiresPermissions("customer:menu:query")
    @GetMapping(value = "/{deptId}")
    @ApiOperation(value = "获取组织和菜单关联详细信息", notes = "获取组织和菜单关联详细信息")
    public AjaxResult getInfo(@PathVariable("deptId") Long deptId)
    {
        return success(sysDeptMenuService.selectSysDeptMenuByDeptId(deptId));
    }

    /**
     * 新增组织和菜单关联
     */
    @RequiresPermissions("customer:menu:add")
    @Log(title = "组织和菜单关联", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation(value = "新增组织和菜单关联", notes = "新增组织和菜单关联")
    public AjaxResult add(@RequestBody SysDeptMenu sysDeptMenu)
    {
        return toAjax(sysDeptMenuService.insertSysDeptMenu(sysDeptMenu));
    }

    /**
     * 修改组织和菜单关联
     */
    @RequiresPermissions("customer:menu:edit")
    @Log(title = "组织和菜单关联", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation(value = "修改组织和菜单关联", notes = "修改组织和菜单关联")
    public AjaxResult edit(@RequestBody SysDeptMenu sysDeptMenu)
    {
        return toAjax(sysDeptMenuService.updateSysDeptMenu(sysDeptMenu));
    }

    /**
     * 删除组织和菜单关联
     */
    @RequiresPermissions("customer:menu:remove")
    @Log(title = "组织和菜单关联", businessType = BusinessType.DELETE)
	@DeleteMapping("/{deptIds}")
    @ApiOperation(value = "删除组织和菜单关联", notes = "删除组织和菜单关联")
    public AjaxResult remove(@PathVariable Long[] deptIds)
    {
        return toAjax(sysDeptMenuService.deleteSysDeptMenuByDeptIds(deptIds));
    }
}
