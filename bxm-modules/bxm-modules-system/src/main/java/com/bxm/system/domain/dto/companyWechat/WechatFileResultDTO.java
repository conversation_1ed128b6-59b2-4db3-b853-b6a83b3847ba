package com.bxm.system.domain.dto.companyWechat;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class WechatFileResultDTO {

    @ApiModelProperty("文件短链接")
    private String fileUrl;

    @ApiModelProperty("文件名称")
    private String fileName;

    @ApiModelProperty("文件长链接")
    private String fullFileUrl;

    @ApiModelProperty("企微文件id")
    private String wechatFileId;
}
