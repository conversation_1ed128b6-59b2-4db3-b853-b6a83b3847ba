package com.bxm.system.domain.vo;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.bxm.system.api.domain.SysEmployee;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.bxm.system.api.domain.SysDept;
import com.bxm.system.domain.SysMenu;
import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.util.ObjectUtils;

/**
 * Treeselect树结构实体类
 * 
 * <AUTHOR>
 */
public class TreeSelect implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 节点ID */
    private Long id;

    /** 节点名称 */
    private String label;

    private Integer deptType;

    /** 子节点 */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<TreeSelect> children;

    @ApiModelProperty("员工列表")
    private List<SysEmployee> employees;

    public List<SysEmployee> getEmployees() {
        return employees;
    }

    public void setEmployees(List<SysEmployee> employees) {
        this.employees = employees;
    }

    public TreeSelect()
    {

    }

    public TreeSelect(SysDept dept, Map<Long, List<SysEmployee>> employesMap)
    {
        this.id = dept.getDeptId();
        List<SysEmployee> employees = employesMap.getOrDefault(dept.getDeptId(), Lists.newArrayList());
        this.label = dept.getDeptName() + (ObjectUtils.isEmpty(employees) ? "" : ("(" + employees.stream().map(SysEmployee::getEmployeeName).collect(Collectors.joining("、")) + ")"));
        this.deptType = dept.getDeptType();
        this.children = dept.getChildren().stream().map(t -> new TreeSelect(t, employesMap)).collect(Collectors.toList());
    }

    public TreeSelect(SysDept dept, Map<Long, List<SysEmployee>> employesMap, Map<Long, List<SysEmployee>> dataScopeEmployee)
    {
        this.id = dept.getDeptId();
        List<SysEmployee> employees = employesMap.getOrDefault(dept.getDeptId(), Lists.newArrayList());
        this.label = dept.getDeptName() + (ObjectUtils.isEmpty(employees) ? "" : ("(" + employees.stream().map(SysEmployee::getEmployeeName).collect(Collectors.joining("、")) + ")"));
        this.deptType = dept.getDeptType();
        this.employees = dataScopeEmployee.getOrDefault(dept.getDeptId(), Lists.newArrayList());
        this.children = dept.getChildren().stream().map(t -> new TreeSelect(t, employesMap, dataScopeEmployee)).collect(Collectors.toList());
    }

    public TreeSelect(SysDept dept, Map<Long, List<SysEmployee>> employesMap, Integer flag)
    {
        this.id = dept.getDeptId();
        List<SysEmployee> employees = employesMap.getOrDefault(dept.getDeptId(), Lists.newArrayList());
        this.label = dept.getDeptName() + "(" + employees.size() + ")";
        this.deptType = dept.getDeptType();
        this.children = dept.getChildren().stream().map(t -> new TreeSelect(t, employesMap, 1)).collect(Collectors.toList());
    }

    public TreeSelect(SysDept dept, Map<Long, List<SysEmployee>> employesMap, Map<Long, List<SysEmployee>> dataScopeEmployee, Integer flag)
    {
        this.id = dept.getDeptId();
        List<SysEmployee> employees = employesMap.getOrDefault(dept.getDeptId(), Lists.newArrayList());
        this.label = dept.getDeptName() + "(" + employees.size() + ")";
        this.deptType = dept.getDeptType();
        this.employees = dataScopeEmployee.getOrDefault(dept.getDeptId(), Lists.newArrayList());
        this.children = dept.getChildren().stream().map(t -> new TreeSelect(t, employesMap, dataScopeEmployee, 1)).collect(Collectors.toList());
    }

    public TreeSelect(SysDept dept)
    {
        this.id = dept.getDeptId();
        this.label = dept.getDeptName();
        this.deptType = dept.getDeptType();
        this.children = dept.getChildren().stream().map(TreeSelect::new).collect(Collectors.toList());
    }

    public TreeSelect(SysDept dept, Map<Long, List<SysEmployee>> dataScopeEmployee, Boolean flag)
    {
        this.id = dept.getDeptId();
        this.label = dept.getDeptName();
        this.deptType = dept.getDeptType();
        this.employees = dataScopeEmployee.getOrDefault(dept.getDeptId(), Lists.newArrayList());
        this.children = dept.getChildren().stream().map(t -> new TreeSelect(t, dataScopeEmployee, flag)).collect(Collectors.toList());
    }

    public TreeSelect(SysMenu menu)
    {
        this.id = menu.getMenuId();
        this.label = menu.getMenuName();
        this.children = menu.getChildren().stream().map(TreeSelect::new).collect(Collectors.toList());
    }

    public Long getId()
    {
        return id;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public String getLabel()
    {
        return label;
    }

    public void setLabel(String label)
    {
        this.label = label;
    }

    public List<TreeSelect> getChildren()
    {
        return children;
    }

    public void setChildren(List<TreeSelect> children)
    {
        this.children = children;
    }

    public Integer getDeptType() {
        return deptType;
    }

    public void setDeptType(Integer deptType) {
        this.deptType = deptType;
    }
}
