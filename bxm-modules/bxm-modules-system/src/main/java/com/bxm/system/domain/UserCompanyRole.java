package com.bxm.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bxm.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.bxm.common.core.web.domain.BaseEntity;
import lombok.experimental.Accessors;

/**
 * 公司用户关联对象 sys_user_company_role
 * 
 * <AUTHOR>
 * @date 2024-05-31
 */
@Data
@ApiModel("公司用户关联对象")
@Accessors(chain = true)
@TableName("sys_user_company_role")
public class UserCompanyRole extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 用户公司表ID */
    @Excel(name = "用户公司表ID")
    @ApiModelProperty(value = "用户公司表ID")
    private Long userCompanyId;

    /** 角色ID */
    @Excel(name = "角色ID")
    @ApiModelProperty(value = "角色ID")
    private Long roleId;

}
