package com.bxm.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.common.core.exception.ServiceException;
import com.bxm.common.core.utils.ServletUtils;
import com.bxm.system.api.domain.SysDept;
import com.bxm.system.domain.SysUsualLink;
import com.bxm.system.domain.dto.UsualLinkDTO;
import com.bxm.system.domain.dto.UsualLinkResultDTO;
import com.bxm.system.mapper.SysUsualLinkMapper;
import com.bxm.system.service.ISysDeptService;
import com.bxm.system.service.ISysUsualLinkService;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 业务统一操作记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-05-09
 */
@Service
public class SysUsualLinkServiceImpl extends ServiceImpl<SysUsualLinkMapper, SysUsualLink> implements ISysUsualLinkService
{

    @Autowired
    private ISysDeptService deptService;

    @Override
    public List<UsualLinkResultDTO> usualLinkList() {
        List<UsualLinkResultDTO> result = Lists.newArrayList();
        Long deptId = Long.parseLong(ServletUtils.getRequest().getHeader("deptId"));
        SysDept sysDept = deptService.getById(deptId);
        if (Objects.isNull(sysDept) || !Objects.equals("0", sysDept.getDelFlag())) {
            return result;
        }
        Long businessTopDeptId = Long.parseLong(sysDept.getAncestors().split(",")[1]);
        List<SysUsualLink> innerList = list(new LambdaQueryWrapper<SysUsualLink>().eq(SysUsualLink::getIsDel, false)
                .eq(SysUsualLink::getDeptId, businessTopDeptId)
                .eq(SysUsualLink::getLinkType, 2));
        List<SysUsualLink> list = list(new LambdaQueryWrapper<SysUsualLink>().eq(SysUsualLink::getIsDel, false)
                .eq(SysUsualLink::getLinkType, 1));
        result.add(UsualLinkResultDTO.builder().linkType(1).links(list.stream().map(item -> UsualLinkDTO.builder().linkUrl(item.getLinkUrl()).linkName(item.getLinkName()).build()).collect(Collectors.toList())).build());
        result.add(UsualLinkResultDTO.builder().linkType(2).links(innerList.stream().map(item -> UsualLinkDTO.builder().linkUrl(item.getLinkUrl()).linkName(item.getLinkName()).build()).collect(Collectors.toList())).build());
        return result;
    }
}
