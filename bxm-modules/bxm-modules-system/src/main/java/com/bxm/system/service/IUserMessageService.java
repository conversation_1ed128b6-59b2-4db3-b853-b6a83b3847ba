package com.bxm.system.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bxm.system.api.domain.RemoteUserMessagePageResult;
import com.bxm.system.api.domain.RemoteUserMessageVO;
import com.bxm.system.domain.UserMessage;
import com.bxm.system.domain.dto.UserMessageDTO;
import com.bxm.system.domain.dto.UserMessageNotReadInfoDTO;
import com.bxm.system.domain.dto.UserMessageTypeStatisticDTO;

import java.util.List;
import java.util.Map;

public interface IUserMessageService extends IService<UserMessage> {

    Integer getNotReadMessageCount();

    IPage<UserMessageDTO> getUserMessageList(Integer pageNum, Integer pageSize);

    void readMessage(List<Long> ids);

    void allRead(String readTime);

    void sendMessage(List<RemoteUserMessageVO> voList);

    Map<String, Integer> getNotReadMessageCountByUserId(Long userId);

    RemoteUserMessagePageResult getUserMessageListByUserId(Integer pageNum, Integer pageSize, Long userId);

    void readMessageInner(List<Long> ids, Long userId);

    void allReadInner(Map<String, Object> params);

    UserMessageNotReadInfoDTO getNotReadMessageInfo();

    List<UserMessageTypeStatisticDTO> userMessageTypeStatistic(Integer listType);

    IPage<UserMessageDTO> getUserMessageListV2(Integer pageNum, Integer pageSize, Integer messageType, Integer isRead, Integer listType);
}
