package com.bxm.system.controller;

import com.bxm.common.core.domain.Result;
import com.bxm.common.core.web.controller.BaseController;
import com.bxm.common.security.annotation.InnerAuth;
import com.bxm.system.service.IErrorCodeService;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 错误信息配置Controller
 * 
 * <AUTHOR>
 * @date 2025-05-14
 */
@RestController
@RequestMapping("/errorCode")
@Api(tags = "错误信息配置")
public class ErrorCodeController extends BaseController
{
    @Autowired
    private IErrorCodeService errorCodeService;

    @GetMapping("/getErrorMsgByErrorCode")
    @InnerAuth
    public Result<String> getErrorMsgByErrorCode(@RequestParam("errorCode") String errorCode) {
        return Result.ok(errorCodeService.getErrorMsgByErrorCode(errorCode));
    }
}
