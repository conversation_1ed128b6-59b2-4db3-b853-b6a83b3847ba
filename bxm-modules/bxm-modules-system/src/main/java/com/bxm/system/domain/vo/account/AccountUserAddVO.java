package com.bxm.system.domain.vo.account;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AccountUserAddVO {

    @ApiModelProperty("账号")
    @NotEmpty(message = "账号不能为空")
    @Length(max = 50, message = "账号不能超过50个字")
    @Length(min = 2, message = "账号不能少于2个字")
    private String userName;

    @ApiModelProperty("员工名")
    @NotEmpty(message = "员工名不能为空")
    @Length(max = 50, message = "员工名不能超过50个字")
    @Length(min = 2, message = "员工名不能少于2个字")
    private String nickName;

    @ApiModelProperty("手机号")
    @NotEmpty(message = "手机号不能为空")
    @Length(max = 50, message = "手机号不能超过50个字")
    @Length(min = 2, message = "手机号不能少于2个字")
    private String phonenumber;

    @ApiModelProperty("角色id列表")
    private List<Long> roleIds;

    @ApiModelProperty("部门/小组id列表")
    private List<Long> deptIds;

    @ApiModelProperty("是否主管，true-是，false-否")
    private Boolean isLeader;

    @ApiModelProperty(hidden = true)
    private Long deptId;

    @ApiModelProperty(hidden = true)
    private Long userId;
}
