package com.bxm.system.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DeptAccountBalanceDTO {

    @ApiModelProperty("业务公司id")
    private Long businessDeptId;

    @ApiModelProperty("业务公司名称")
    private String businessDeptName;

    @ApiModelProperty("业务集团名称")
    private String businessTopDeptName;

    @ApiModelProperty("余额")
    private BigDecimal accountBalance;
}
