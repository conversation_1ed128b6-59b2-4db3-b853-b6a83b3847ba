package com.bxm.system.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.bxm.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 组织和菜单关联对象 sys_dept_menu
 * 
 * <AUTHOR>
 * @date 2024-05-29
 */
@Data
@ApiModel("组织和菜单关联对象")
@Accessors(chain = true)
@TableName("sys_dept_menu")
public class SysDeptMenu implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 组织ID */
    @Excel(name = "组织ID")
    @ApiModelProperty(value = "组织ID")
    private Long deptId;

    /** 菜单ID */
    @Excel(name = "菜单ID")
    @ApiModelProperty(value = "菜单ID")
    private Long menuId;

}
