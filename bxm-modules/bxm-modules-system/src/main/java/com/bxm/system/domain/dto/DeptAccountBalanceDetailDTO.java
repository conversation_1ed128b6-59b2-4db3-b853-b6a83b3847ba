package com.bxm.system.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DeptAccountBalanceDetailDTO {

    @ApiModelProperty(value = "流水id")
    private Long id;

    @ApiModelProperty("创建时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @ApiModelProperty("来源类型，1-结算单（新户预收），2-账单（预存抵扣），3-调入，4-拨出")
    private Integer sourceType;

    @ApiModelProperty(value = "来源类型名称")
    private String sourceTypeName;

    @ApiModelProperty("业务编号")
    private String businessNo;

    @ApiModelProperty("收支类型")
    private String incomeType;

    @ApiModelProperty("变化金额，带正负号")
    private String changeAmount;

    @ApiModelProperty("状态，1-待确认，2-已取消，3-已确认")
    private Integer status;

    @ApiModelProperty(value = "状态名称")
    private String statusName;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("附件数量")
    private Integer fileCount;
}
