package com.bxm.system.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bxm.common.core.domain.Result;
import com.bxm.system.domain.dto.role.RoleDTO;
import com.bxm.system.domain.dto.role.RoleDetailDTO;
import com.bxm.system.domain.vo.role.RoleAddVO;
import com.bxm.system.domain.vo.role.RoleModifyVO;
import com.bxm.system.domain.vo.role.RoleUpdateMenuVO;
import com.bxm.system.service.RoleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/role")
@Api(tags = "角色管理")
public class RoleController {

    @Autowired
    private RoleService roleService;

    @GetMapping("/roleList")
    @ApiParam("角色列表")
    public Result<IPage<RoleDTO>> roleList(@RequestParam(value = "roleName", required = false) @ApiParam("角色名搜索") String roleName,
                                           @RequestParam(value = "pageNum", defaultValue = "1") @ApiParam("当前页") Integer pageNum,
                                           @RequestParam(value = "pageSize", defaultValue = "10") @ApiParam("每页显示数量") Integer pageSize) {
        return Result.ok(roleService.roleList(roleName, pageNum, pageSize));
    }

    @GetMapping("/detail")
    @ApiOperation("角色详情（回显用）")
    public Result<RoleDetailDTO> detail(@RequestParam("id") @ApiParam("角色id") Long id) {
        return Result.ok(roleService.detail(id));
    }

    @PostMapping("/addRole")
    @ApiOperation("新增角色")
    public Result addRole(@RequestBody RoleAddVO vo, @RequestHeader("deptId") Long deptId) {
        roleService.addRole(vo, deptId);
        return Result.ok();
    }

    @PostMapping("/modifyRole")
    @ApiOperation("编辑角色")
    public Result modifyRole(@RequestBody RoleModifyVO vo, @RequestHeader("deptId") Long deptId) {
        roleService.modifyRole(vo, deptId);
        return Result.ok();
    }

    @PostMapping("/deleteRole")
    @ApiOperation("删除角色（直接传角色id的值，不需要key）")
    public Result deleteRole(@RequestBody Long id, @RequestHeader("deptId") Long deptId) {
        roleService.deleteRole(id, deptId);
        return Result.ok();
    }

    @PostMapping("/addRoleMenu")
    @ApiOperation("角色批量增加权限")
    public Result addRoleMenu(@RequestBody RoleUpdateMenuVO vo, @RequestHeader("deptId") Long deptId) {
        roleService.addRoleMenu(vo, deptId);
        return Result.ok();
    }

    @PostMapping("/deleteRoleMenu")
    @ApiOperation("角色批量删除权限")
    public Result deleteRoleMenu(@RequestBody RoleUpdateMenuVO vo, @RequestHeader("deptId") Long deptId) {
        roleService.deleteRoleMenu(vo, deptId);
        return Result.ok();
    }

    @GetMapping("/roleSelectList")
    @ApiOperation("角色下拉列表")
    public Result<List<RoleDTO>> roleSelectList(@RequestHeader("deptId") Long deptId) {
        return Result.ok(roleService.roleSelectList(deptId));
    }
}
