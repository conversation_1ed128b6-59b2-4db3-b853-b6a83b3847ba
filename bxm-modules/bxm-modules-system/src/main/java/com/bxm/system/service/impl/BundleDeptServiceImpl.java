package com.bxm.system.service.impl;

import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.common.core.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.bxm.system.mapper.BundleDeptMapper;
import com.bxm.system.domain.BundleDept;
import com.bxm.system.service.IBundleDeptService;

/**
 * 套餐组织关系Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-04-17
 */
@Service
public class BundleDeptServiceImpl extends ServiceImpl<BundleDeptMapper, BundleDept> implements IBundleDeptService
{
    @Autowired
    private BundleDeptMapper bundleDeptMapper;

    /**
     * 查询套餐组织关系
     * 
     * @param id 套餐组织关系主键
     * @return 套餐组织关系
     */
    @Override
    public BundleDept selectBundleDeptById(Long id)
    {
        return bundleDeptMapper.selectBundleDeptById(id);
    }

    /**
     * 查询套餐组织关系列表
     * 
     * @param bundleDept 套餐组织关系
     * @return 套餐组织关系
     */
    @Override
    public List<BundleDept> selectBundleDeptList(BundleDept bundleDept)
    {
        return bundleDeptMapper.selectBundleDeptList(bundleDept);
    }

    /**
     * 新增套餐组织关系
     * 
     * @param bundleDept 套餐组织关系
     * @return 结果
     */
    @Override
    public int insertBundleDept(BundleDept bundleDept)
    {
        bundleDept.setCreateTime(DateUtils.getNowDate());
        return bundleDeptMapper.insertBundleDept(bundleDept);
    }

    /**
     * 修改套餐组织关系
     * 
     * @param bundleDept 套餐组织关系
     * @return 结果
     */
    @Override
    public int updateBundleDept(BundleDept bundleDept)
    {
        bundleDept.setUpdateTime(DateUtils.getNowDate());
        return bundleDeptMapper.updateBundleDept(bundleDept);
    }

    /**
     * 批量删除套餐组织关系
     * 
     * @param ids 需要删除的套餐组织关系主键
     * @return 结果
     */
    @Override
    public int deleteBundleDeptByIds(Long[] ids)
    {
        return bundleDeptMapper.deleteBundleDeptByIds(ids);
    }

    /**
     * 删除套餐组织关系信息
     * 
     * @param id 套餐组织关系主键
     * @return 结果
     */
    @Override
    public int deleteBundleDeptById(Long id)
    {
        return bundleDeptMapper.deleteBundleDeptById(id);
    }
}
