package com.bxm.system.domain.dto.role;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RoleDTO {

    @ApiModelProperty("角色id")
    private Long id;

    @ApiModelProperty("角色名称")
    private String roleName;

    @ApiModelProperty("角色code")
    private String roleKey;

    @ApiModelProperty("归属集团名称")
    private String belongDeptName;

    @ApiModelProperty("可用组织类型，1-业务公司，2-会计工厂")
    private List<Integer> deptTypes;

    @ApiModelProperty("可用组织类型名称")
    private String deptTypeStr;

    @ApiModelProperty("账号数")
    private Integer accountCount;

    @ApiModelProperty("备注")
    private String remark;
}
