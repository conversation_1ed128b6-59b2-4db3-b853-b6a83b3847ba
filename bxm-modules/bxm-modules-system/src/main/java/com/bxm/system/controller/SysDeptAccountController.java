package com.bxm.system.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bxm.common.core.domain.Result;
import com.bxm.common.core.web.domain.CommonFileVO;
import com.bxm.system.api.domain.CancelBalanceDetailVO;
import com.bxm.system.api.domain.ConfirmBalanceDetailVO;
import com.bxm.system.api.domain.RemoteDeptAccountBalanceDetail;
import com.bxm.system.domain.dto.DeptAccountBalanceDTO;
import com.bxm.system.domain.dto.DeptAccountBalanceDetailDTO;
import com.bxm.system.domain.vo.DeptAccountBalanceAllotVO;
import com.bxm.system.service.ISysDeptAccountBalanceDetailService;
import com.bxm.system.service.ISysDeptService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;

@RequestMapping("/deptAccount")
@Api(tags = "公司余额相关（预存账户）")
@RestController
public class SysDeptAccountController {

    @Autowired
    private ISysDeptService sysDeptService;

    @Autowired
    private ISysDeptAccountBalanceDetailService sysDeptAccountBalanceDetailService;

    @GetMapping("/deptAccountBalance")
    @ApiOperation("预存账户列表")
    public Result<IPage<DeptAccountBalanceDTO>> deptAccountBalance(@RequestHeader("deptId") Long deptId,
                                                                   @RequestParam(value = "queryDeptId", required = false) @ApiParam("查询组织id，业务集团id或业务公司id") String queryDeptId,
                                                                   @RequestParam("pageNum") @ApiParam("页码") Integer pageNum,
                                                                   @RequestParam("pageSize") @ApiParam("每页条数") Integer pageSize) {
        return Result.ok(sysDeptService.deptAccountBalance(deptId, queryDeptId, pageNum, pageSize));
    }

    @PostMapping("/allot")
    @ApiOperation("预存账户调拨")
    public Result allot(@RequestBody DeptAccountBalanceAllotVO vo) {
        sysDeptService.allot(vo);
        return Result.ok();
    }

    @GetMapping("/accountBalanceDetailList")
    @ApiOperation("预存账户流水列表")
    public Result<IPage<DeptAccountBalanceDetailDTO>> accountBalanceDetailList(@RequestParam("businessDeptId") @ApiParam("业务公司id") Long businessDeptId,
                                                                               @RequestParam("pageNum") @ApiParam("页码") Integer pageNum,
                                                                               @RequestParam("pageSize") @ApiParam("每页条数") Integer pageSize) {
        return Result.ok(sysDeptAccountBalanceDetailService.accountBalanceDetailList(businessDeptId, pageNum, pageSize));
    }

    @GetMapping("/getBalanceDetailFiles")
    @ApiOperation("查看流水附件")
    public Result<List<CommonFileVO>> getBalanceDetailFiles(@RequestParam("id") @ApiParam("流水id") Long id) {
        return Result.ok(sysDeptAccountBalanceDetailService.getBalanceDetailFiles(id));
    }

    @PostMapping("/remoteCreateBalanceDetail")
    @ApiIgnore
    public Result remoteCreateBalanceDetail(@RequestBody RemoteDeptAccountBalanceDetail detail) {
        sysDeptAccountBalanceDetailService.createBalanceDetail(detail);
        return Result.ok();
    }

    @PostMapping("/remoteBatchCreateBalanceDetail")
    @ApiIgnore
    public Result remoteBatchCreateBalanceDetail(@RequestBody List<RemoteDeptAccountBalanceDetail> details) {
        sysDeptAccountBalanceDetailService.batchCreateBalanceDetail(details);
        return Result.ok();
    }

    @PostMapping("/remoteBatchCancelBalanceDetail")
    @ApiIgnore
    public Result remoteBatchCancelBalanceDetail(@RequestBody CancelBalanceDetailVO vo) {
        sysDeptAccountBalanceDetailService.batchCancelBalanceDetail(vo);
        return Result.ok();
    }

    @PostMapping("/remoteBatchConfirmBalanceDetail")
    @ApiIgnore
    public Result remoteBatchConfirmBalanceDetail(@RequestBody ConfirmBalanceDetailVO vo) {
        sysDeptAccountBalanceDetailService.remoteBatchConfirmBalanceDetail(vo);
        return Result.ok();
    }
}
