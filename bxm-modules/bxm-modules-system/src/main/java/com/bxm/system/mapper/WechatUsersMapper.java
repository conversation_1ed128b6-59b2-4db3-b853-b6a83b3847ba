package com.bxm.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bxm.system.domain.WechatUsers;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 企业微信用户Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-06-23
 */
@Mapper
public interface WechatUsersMapper extends BaseMapper<WechatUsers>
{
    /**
     * 查询企业微信用户
     * 
     * @param id 企业微信用户主键
     * @return 企业微信用户
     */
    public WechatUsers selectWechatUsersById(Long id);

    /**
     * 查询企业微信用户列表
     * 
     * @param wechatUsers 企业微信用户
     * @return 企业微信用户集合
     */
    public List<WechatUsers> selectWechatUsersList(WechatUsers wechatUsers);

    /**
     * 新增企业微信用户
     * 
     * @param wechatUsers 企业微信用户
     * @return 结果
     */
    public int insertWechatUsers(WechatUsers wechatUsers);

    /**
     * 修改企业微信用户
     * 
     * @param wechatUsers 企业微信用户
     * @return 结果
     */
    public int updateWechatUsers(WechatUsers wechatUsers);

    /**
     * 删除企业微信用户
     * 
     * @param id 企业微信用户主键
     * @return 结果
     */
    public int deleteWechatUsersById(Long id);

    /**
     * 批量删除企业微信用户
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteWechatUsersByIds(Long[] ids);
}
