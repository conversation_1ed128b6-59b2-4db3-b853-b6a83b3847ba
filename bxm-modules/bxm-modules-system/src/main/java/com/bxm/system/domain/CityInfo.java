package com.bxm.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 城市信息表(包含省市区县)
 * </p>
 *
 * <AUTHOR> @since 2024-03-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sys_city_info")
public class CityInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 区划信息id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 父级挂接id
     */
    private Integer pid;

    /**
     * 区划编码
     */
    private String code;

    /**
     * 区划名称
     */
    private String name;

    /**
     * 地区类型：0:省/自治区/直辖市，1:市级地区，2:县级地区

     */
    @ApiModelProperty("地区类型：0:省/自治区/直辖市，1:市级地区，2:县级地区")
    private Integer areaType;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;


}
