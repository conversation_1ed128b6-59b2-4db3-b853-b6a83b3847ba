package com.bxm.system.service;

import java.util.List;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bxm.system.domain.BundleMenu;
import com.bxm.system.domain.SysMenu;

import javax.validation.constraints.NotNull;

/**
 * 套餐菜单关系Service接口
 * 
 * <AUTHOR>
 * @date 2025-04-17
 */
public interface IBundleMenuService extends IService<BundleMenu>
{
    /**
     * 查询套餐菜单关系
     * 
     * @param id 套餐菜单关系主键
     * @return 套餐菜单关系
     */
    public BundleMenu selectBundleMenuById(Long id);

    /**
     * 查询套餐菜单关系列表
     * 
     * @param bundleMenu 套餐菜单关系
     * @return 套餐菜单关系集合
     */
    public List<BundleMenu> selectBundleMenuList(BundleMenu bundleMenu);

    /**
     * 新增套餐菜单关系
     * 
     * @param bundleMenu 套餐菜单关系
     * @return 结果
     */
    public int insertBundleMenu(BundleMenu bundleMenu);

    /**
     * 修改套餐菜单关系
     * 
     * @param bundleMenu 套餐菜单关系
     * @return 结果
     */
    public int updateBundleMenu(BundleMenu bundleMenu);

    /**
     * 批量删除套餐菜单关系
     * 
     * @param ids 需要删除的套餐菜单关系主键集合
     * @return 结果
     */
    public int deleteBundleMenuByIds(Long[] ids);

    /**
     * 删除套餐菜单关系信息
     * 
     * @param id 套餐菜单关系主键
     * @return 结果
     */
    public int deleteBundleMenuById(Long id);

    void logicDeleteByBundleId(Long bundleId);

    void logicDeleteByBundleIdAndMenuIds(Long bundleId, List<Long> menuIds);

    List<SysMenu> selectMenusByBundleId(Long bundleId);

    List<Long> selectMenuIdsByBundleId(Long bundleId);

    List<BundleMenu> selectByBundleIds(List<Long> collect);
}
