package com.bxm.system.service.impl;

import java.util.List;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.bxm.system.mapper.UserCompanyMapper;
import com.bxm.system.domain.UserCompany;
import com.bxm.system.service.IUserCompanyService;

/**
 * 用户和公司关联Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-05-31
 */
@Service
public class UserCompanyServiceImpl extends ServiceImpl<UserCompanyMapper, UserCompany> implements IUserCompanyService
{
    @Autowired
    private UserCompanyMapper userCompanyMapper;

    /**
     * 查询用户和公司关联
     * 
     * @param id 用户和公司关联主键
     * @return 用户和公司关联
     */
    @Override
    public UserCompany selectUserCompanyById(Long id)
    {
        return userCompanyMapper.selectUserCompanyById(id);
    }

    /**
     * 查询用户和公司关联列表
     * 
     * @param userCompany 用户和公司关联
     * @return 用户和公司关联
     */
    @Override
    public List<UserCompany> selectUserCompanyList(UserCompany userCompany)
    {
        return userCompanyMapper.selectUserCompanyList(userCompany);
    }

    /**
     * 新增用户和公司关联
     * 
     * @param userCompany 用户和公司关联
     * @return 结果
     */
    @Override
    public int insertUserCompany(UserCompany userCompany)
    {
        return userCompanyMapper.insertUserCompany(userCompany);
    }

    /**
     * 修改用户和公司关联
     * 
     * @param userCompany 用户和公司关联
     * @return 结果
     */
    @Override
    public int updateUserCompany(UserCompany userCompany)
    {
        return userCompanyMapper.updateUserCompany(userCompany);
    }

    /**
     * 批量删除用户和公司关联
     * 
     * @param ids 需要删除的用户和公司关联主键
     * @return 结果
     */
    @Override
    public int deleteUserCompanyByIds(Long[] ids)
    {
        return userCompanyMapper.deleteUserCompanyByIds(ids);
    }

    /**
     * 删除用户和公司关联信息
     * 
     * @param id 用户和公司关联主键
     * @return 结果
     */
    @Override
    public int deleteUserCompanyById(Long id)
    {
        return userCompanyMapper.deleteUserCompanyById(id);
    }

    @Override
    public UserCompany getByUserIdAndCompanyId(Long userId, Long companyId) {
        return getOne(new LambdaQueryWrapper<UserCompany>()
                .eq(UserCompany::getUserId, userId)
                .eq(UserCompany::getCompanyId, companyId));
    }

}
