package com.bxm.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bxm.system.domain.WechatAppConfig;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 企微信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-06-23
 */
@Mapper
public interface WechatAppConfigMapper extends BaseMapper<WechatAppConfig>
{
    /**
     * 查询企微信息
     * 
     * @param id 企微信息主键
     * @return 企微信息
     */
    public WechatAppConfig selectWechatAppConfigById(Long id);

    /**
     * 查询企微信息列表
     * 
     * @param wechatAppConfig 企微信息
     * @return 企微信息集合
     */
    public List<WechatAppConfig> selectWechatAppConfigList(WechatAppConfig wechatAppConfig);

    /**
     * 新增企微信息
     * 
     * @param wechatAppConfig 企微信息
     * @return 结果
     */
    public int insertWechatAppConfig(WechatAppConfig wechatAppConfig);

    /**
     * 修改企微信息
     * 
     * @param wechatAppConfig 企微信息
     * @return 结果
     */
    public int updateWechatAppConfig(WechatAppConfig wechatAppConfig);

    /**
     * 删除企微信息
     * 
     * @param id 企微信息主键
     * @return 结果
     */
    public int deleteWechatAppConfigById(Long id);

    /**
     * 批量删除企微信息
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteWechatAppConfigByIds(Long[] ids);
}
