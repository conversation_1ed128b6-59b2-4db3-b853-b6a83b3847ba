package com.bxm.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bxm.common.core.annotation.Excel;
import com.bxm.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 *
 * <AUTHOR>
 * @date 2024-06-23
 */
@Data
@ApiModel("消息表对象")
@Accessors(chain = true)
@TableName("sys_user_message")
public class UserMessage extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键id */
    @TableId(value = "id", type = IdType.AUTO)
    @Excel(name = "主键id")
    @ApiModelProperty(value = "主键id")
    private Long id;

    @TableField(value = "send_employee_id")
    @Excel(name = "发送者员工id")
    @ApiModelProperty(value = "发送者员工id")
    private Long sendEmployeeId;

    @TableField(value = "send_employee_name")
    @Excel(name = "发送者员工姓名")
    @ApiModelProperty(value = "发送者员工姓名")
    private String sendEmployeeName;

    @TableField(value = "send_dept_id")
    @Excel(name = "发送者部门id")
    @ApiModelProperty(value = "发送者部门id")
    private Long sendDeptId;

    @TableField(value = "send_dept_name")
    @Excel(name = "发送者部门名称")
    @ApiModelProperty(value = "发送者部门名称")
    private String sendDeptName;

    @TableField(value = "receive_user_id")
    @Excel(name = "接收者用户id")
    @ApiModelProperty(value = "接收者用户id")
    private Long receiveUserId;

    @TableField(value = "receive_employee_id")
    @Excel(name = "接收者员工id")
    @ApiModelProperty(value = "接收者员工id")
    private Long receiveEmployeeId;

    @TableField(value = "receive_employee_name")
    @Excel(name = "接收者员工姓名")
    @ApiModelProperty(value = "接收者员工姓名")
    private String receiveEmployeeName;

    @TableField(value = "receive_dept_id")
    @Excel(name = "接收者部门id")
    @ApiModelProperty(value = "接收者部门id")
    private Long receiveDeptId;

    @TableField(value = "receive_dept_name")
    @Excel(name = "接收者部门名称")
    @ApiModelProperty(value = "接收者部门名称")
    private String receiveDeptName;

    @TableField(value = "content")
    @Excel(name = "消息内容")
    @ApiModelProperty(value = "消息内容")
    private String content;

    @TableField(value = "message_type")
    @Excel(name = "消息类型,1-主动催办，2-系统触发")
    @ApiModelProperty(value = "消息类型,1-主动催办，2-系统触发")
    private Integer messageType;

    @TableField(value = "is_read")
    @Excel(name = "是否已读")
    @ApiModelProperty(value = "是否已读")
    private Boolean isRead;

    @TableField(value = "is_del")
    @Excel(name = "是否删除")
    @ApiModelProperty(value = "是否删除")
    private Boolean isDel;
}
