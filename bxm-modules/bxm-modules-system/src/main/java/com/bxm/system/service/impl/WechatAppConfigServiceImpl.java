package com.bxm.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.common.core.constant.CacheConstants;
import com.bxm.common.core.utils.DateUtils;
import com.bxm.common.redis.service.RedisService;
import com.bxm.system.service.IWechatAppConfigService;
import com.bxm.system.domain.WechatAppConfig;
import com.bxm.system.mapper.WechatAppConfigMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.Collections;
import java.util.List;

/**
 * 企微信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-06-23
 */
@Service
public class WechatAppConfigServiceImpl extends ServiceImpl<WechatAppConfigMapper, WechatAppConfig> implements IWechatAppConfigService
{
    @Autowired
    private WechatAppConfigMapper wechatAppConfigMapper;

    @Autowired
    private RedisService redisService;

    /**
     * 查询企微信息
     * 
     * @param id 企微信息主键
     * @return 企微信息
     */
    @Override
    public WechatAppConfig selectWechatAppConfigById(Long id)
    {
        return wechatAppConfigMapper.selectWechatAppConfigById(id);
    }

    /**
     * 查询企微信息列表
     * 
     * @param wechatAppConfig 企微信息
     * @return 企微信息
     */
    @Override
    public List<WechatAppConfig> selectWechatAppConfigList(WechatAppConfig wechatAppConfig)
    {
        return wechatAppConfigMapper.selectWechatAppConfigList(wechatAppConfig);
    }

    /**
     * 新增企微信息
     * 
     * @param wechatAppConfig 企微信息
     * @return 结果
     */
    @Override
    public int insertWechatAppConfig(WechatAppConfig wechatAppConfig)
    {
        wechatAppConfig.setCreateTime(DateUtils.getNowDate());
        return wechatAppConfigMapper.insertWechatAppConfig(wechatAppConfig);
    }

    /**
     * 修改企微信息
     * 
     * @param wechatAppConfig 企微信息
     * @return 结果
     */
    @Override
    public int updateWechatAppConfig(WechatAppConfig wechatAppConfig)
    {
        wechatAppConfig.setUpdateTime(DateUtils.getNowDate());
        return wechatAppConfigMapper.updateWechatAppConfig(wechatAppConfig);
    }

    /**
     * 批量删除企微信息
     * 
     * @param ids 需要删除的企微信息主键
     * @return 结果
     */
    @Override
    public int deleteWechatAppConfigByIds(Long[] ids)
    {
        return wechatAppConfigMapper.deleteWechatAppConfigByIds(ids);
    }

    /**
     * 删除企微信息信息
     * 
     * @param id 企微信息主键
     * @return 结果
     */
    @Override
    public int deleteWechatAppConfigById(Long id)
    {
        return wechatAppConfigMapper.deleteWechatAppConfigById(id);
    }

    @Override
    public List<WechatAppConfig> selectAll() {
        if (redisService.hasKey(CacheConstants.WECHAT_APP_CONFIG_CACHE_KEY)) {
            return redisService.getCacheList(CacheConstants.WECHAT_APP_CONFIG_CACHE_KEY);
        }
        List<WechatAppConfig> configs = list();
        if (!ObjectUtils.isEmpty(configs)) {
            redisService.setCacheList(CacheConstants.WECHAT_APP_CONFIG_CACHE_KEY, configs);
        }
        return configs;
    }
}
