package com.bxm.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bxm.system.domain.WechatUserBinding;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 企微用户与系统用户关联Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-06-23
 */
@Mapper
public interface WechatUserBindingMapper extends BaseMapper<WechatUserBinding>
{
    /**
     * 查询企微用户与系统用户关联
     * 
     * @param id 企微用户与系统用户关联主键
     * @return 企微用户与系统用户关联
     */
    public WechatUserBinding selectWechatUserBindingById(Long id);

    /**
     * 查询企微用户与系统用户关联列表
     * 
     * @param wechatUserBinding 企微用户与系统用户关联
     * @return 企微用户与系统用户关联集合
     */
    public List<WechatUserBinding> selectWechatUserBindingList(WechatUserBinding wechatUserBinding);

    /**
     * 新增企微用户与系统用户关联
     * 
     * @param wechatUserBinding 企微用户与系统用户关联
     * @return 结果
     */
    public int insertWechatUserBinding(WechatUserBinding wechatUserBinding);

    /**
     * 修改企微用户与系统用户关联
     * 
     * @param wechatUserBinding 企微用户与系统用户关联
     * @return 结果
     */
    public int updateWechatUserBinding(WechatUserBinding wechatUserBinding);

    /**
     * 删除企微用户与系统用户关联
     * 
     * @param id 企微用户与系统用户关联主键
     * @return 结果
     */
    public int deleteWechatUserBindingById(Long id);

    /**
     * 批量删除企微用户与系统用户关联
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteWechatUserBindingByIds(Long[] ids);
}
