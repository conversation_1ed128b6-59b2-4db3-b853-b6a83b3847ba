package com.bxm.system.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CityInfoDTO {

    @ApiModelProperty("省市编码")
    private String code;

    @ApiModelProperty("省市名称")
    private String name;

    @ApiModelProperty("子集")
    private List<CityInfoDTO> children;
}
