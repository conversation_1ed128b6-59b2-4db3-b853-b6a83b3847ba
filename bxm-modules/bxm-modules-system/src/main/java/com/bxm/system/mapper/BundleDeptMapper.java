package com.bxm.system.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bxm.system.domain.BundleDept;
import org.apache.ibatis.annotations.Param;

/**
 * 套餐组织关系Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-04-17
 */
@Mapper
public interface BundleDeptMapper extends BaseMapper<BundleDept>
{
    /**
     * 查询套餐组织关系
     * 
     * @param id 套餐组织关系主键
     * @return 套餐组织关系
     */
    public BundleDept selectBundleDeptById(Long id);

    /**
     * 查询套餐组织关系列表
     * 
     * @param bundleDept 套餐组织关系
     * @return 套餐组织关系集合
     */
    public List<BundleDept> selectBundleDeptList(BundleDept bundleDept);

    /**
     * 新增套餐组织关系
     * 
     * @param bundleDept 套餐组织关系
     * @return 结果
     */
    public int insertBundleDept(BundleDept bundleDept);

    /**
     * 修改套餐组织关系
     * 
     * @param bundleDept 套餐组织关系
     * @return 结果
     */
    public int updateBundleDept(BundleDept bundleDept);

    /**
     * 删除套餐组织关系
     * 
     * @param id 套餐组织关系主键
     * @return 结果
     */
    public int deleteBundleDeptById(Long id);

    /**
     * 批量删除套餐组织关系
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteBundleDeptByIds(Long[] ids);

    void updateBundleIdByDeptIds(@Param("deptIds") List<Long> deptIds,
                                 @Param("bundleId") Long bundleId);

    List<Long> selectNotSameBundleIdDeptIds(@Param("deptIds") List<Long> deptIds,
                                            @Param("bundleId") Long bundleId);
}
