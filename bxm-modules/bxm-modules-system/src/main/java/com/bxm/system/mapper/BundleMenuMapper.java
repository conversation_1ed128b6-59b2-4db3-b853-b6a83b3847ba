package com.bxm.system.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bxm.system.domain.BundleMenu;

/**
 * 套餐菜单关系Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-04-17
 */
@Mapper
public interface BundleMenuMapper extends BaseMapper<BundleMenu>
{
    /**
     * 查询套餐菜单关系
     * 
     * @param id 套餐菜单关系主键
     * @return 套餐菜单关系
     */
    public BundleMenu selectBundleMenuById(Long id);

    /**
     * 查询套餐菜单关系列表
     * 
     * @param bundleMenu 套餐菜单关系
     * @return 套餐菜单关系集合
     */
    public List<BundleMenu> selectBundleMenuList(BundleMenu bundleMenu);

    /**
     * 新增套餐菜单关系
     * 
     * @param bundleMenu 套餐菜单关系
     * @return 结果
     */
    public int insertBundleMenu(BundleMenu bundleMenu);

    /**
     * 修改套餐菜单关系
     * 
     * @param bundleMenu 套餐菜单关系
     * @return 结果
     */
    public int updateBundleMenu(BundleMenu bundleMenu);

    /**
     * 删除套餐菜单关系
     * 
     * @param id 套餐菜单关系主键
     * @return 结果
     */
    public int deleteBundleMenuById(Long id);

    /**
     * 批量删除套餐菜单关系
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteBundleMenuByIds(Long[] ids);
}
