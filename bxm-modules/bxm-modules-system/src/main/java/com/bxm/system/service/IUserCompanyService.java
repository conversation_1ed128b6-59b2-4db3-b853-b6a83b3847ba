package com.bxm.system.service;

import java.util.List;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bxm.system.domain.UserCompany;

/**
 * 用户和公司关联Service接口
 * 
 * <AUTHOR>
 * @date 2024-05-31
 */
public interface IUserCompanyService extends IService<UserCompany>
{
    /**
     * 查询用户和公司关联
     * 
     * @param id 用户和公司关联主键
     * @return 用户和公司关联
     */
    public UserCompany selectUserCompanyById(Long id);

    /**
     * 查询用户和公司关联列表
     * 
     * @param userCompany 用户和公司关联
     * @return 用户和公司关联集合
     */
    public List<UserCompany> selectUserCompanyList(UserCompany userCompany);

    /**
     * 新增用户和公司关联
     * 
     * @param userCompany 用户和公司关联
     * @return 结果
     */
    public int insertUserCompany(UserCompany userCompany);

    /**
     * 修改用户和公司关联
     * 
     * @param userCompany 用户和公司关联
     * @return 结果
     */
    public int updateUserCompany(UserCompany userCompany);

    /**
     * 批量删除用户和公司关联
     * 
     * @param ids 需要删除的用户和公司关联主键集合
     * @return 结果
     */
    public int deleteUserCompanyByIds(Long[] ids);

    /**
     * 删除用户和公司关联信息
     * 
     * @param id 用户和公司关联主键
     * @return 结果
     */
    public int deleteUserCompanyById(Long id);

    UserCompany getByUserIdAndCompanyId(Long userId, Long companyId);
}
