package com.bxm.system.controller;

import com.bxm.common.core.domain.Result;
import com.bxm.system.domain.dto.companyWechat.CompanyWechatBindDTO;
import com.bxm.system.domain.dto.companyWechat.CompanyWechatDeptInfo;
import com.bxm.system.service.CompanyWechatService;
import com.bxm.thirdpart.api.domain.WechatUserInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/companyWechat")
@Api(tags = "企业微信相关")
public class CompanyWechatController {

    @Autowired
    private CompanyWechatService companyWechatService;

    @GetMapping("/getWechatUserInfo")
    @ApiOperation("获取企微用户信息")
    public Result<WechatUserInfo> getWechatUserInfo(@RequestParam @ApiParam("企微code") String code,
                                                    @RequestParam @ApiParam("企微corpId") String corpId) {
        return Result.ok(companyWechatService.getWechatUserInfo(code, corpId));
    }

    @PostMapping("/bind")
    @ApiOperation("企微侧边栏登陆，参数formData")
    public Result<CompanyWechatDeptInfo> bindUser(@RequestBody CompanyWechatBindDTO dto) {
        return Result.ok(companyWechatService.bindUser(dto));
    }

    @PostMapping("/getBindInfoByCode")
    @ApiOperation("获取绑定信息（根据企微code获取）")
    public Result<CompanyWechatDeptInfo> getBindInfoByCode(@RequestBody CompanyWechatBindDTO dto) {
        return Result.ok(companyWechatService.getBindInfoByCode(dto));
    }

    @GetMapping("/getBindInfoByUserId")
    @ApiOperation("获取绑定信息（根据系统userId获取）")
    public Result<CompanyWechatDeptInfo> getBindInfoByUserId(@RequestParam("userId") @ApiParam("用户id") Long userId) {
        return Result.ok(companyWechatService.getBindInfoByUserId(userId));
    }

    @PostMapping("/unBind/{userId}")
    @ApiOperation("取消绑定")
    public Result unBind(@PathVariable("userId") @ApiParam("用户id") Long userId) {
        companyWechatService.unBind(userId);
        return Result.ok();
    }
}
