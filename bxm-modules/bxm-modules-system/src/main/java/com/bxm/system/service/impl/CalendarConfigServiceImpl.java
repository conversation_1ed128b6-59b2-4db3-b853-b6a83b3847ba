package com.bxm.system.service.impl;

import java.time.LocalDate;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.common.core.utils.DateUtils;
import com.bxm.common.core.utils.StringUtils;
import com.bxm.system.domain.dto.calendar.CalendarConfigDTO;
import com.bxm.system.domain.dto.calendar.CalendarDayDTO;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.bxm.system.mapper.CalendarConfigMapper;
import com.bxm.system.domain.CalendarConfig;
import com.bxm.system.service.ICalendarConfigService;

/**
 * 日历配置Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-05-27
 */
@Service
public class CalendarConfigServiceImpl extends ServiceImpl<CalendarConfigMapper, CalendarConfig> implements ICalendarConfigService
{
    @Autowired
    private CalendarConfigMapper calendarConfigMapper;

    private static final Integer WORK_DAY_TYPE = 1;

    private static final Integer WEEK_DAY_TYPE = 2;

    /**
     * 查询日历配置
     * 
     * @param id 日历配置主键
     * @return 日历配置
     */
    @Override
    public CalendarConfig selectCalendarConfigById(Long id)
    {
        return calendarConfigMapper.selectCalendarConfigById(id);
    }

    /**
     * 查询日历配置列表
     * 
     * @param calendarConfig 日历配置
     * @return 日历配置
     */
    @Override
    public List<CalendarConfig> selectCalendarConfigList(CalendarConfig calendarConfig)
    {
        return calendarConfigMapper.selectCalendarConfigList(calendarConfig);
    }

    /**
     * 新增日历配置
     * 
     * @param calendarConfig 日历配置
     * @return 结果
     */
    @Override
    public int insertCalendarConfig(CalendarConfig calendarConfig)
    {
        calendarConfig.setCreateTime(DateUtils.getNowDate());
        return calendarConfigMapper.insertCalendarConfig(calendarConfig);
    }

    /**
     * 修改日历配置
     * 
     * @param calendarConfig 日历配置
     * @return 结果
     */
    @Override
    public int updateCalendarConfig(CalendarConfig calendarConfig)
    {
        calendarConfig.setUpdateTime(DateUtils.getNowDate());
        return calendarConfigMapper.updateCalendarConfig(calendarConfig);
    }

    /**
     * 批量删除日历配置
     * 
     * @param ids 需要删除的日历配置主键
     * @return 结果
     */
    @Override
    public int deleteCalendarConfigByIds(Long[] ids)
    {
        return calendarConfigMapper.deleteCalendarConfigByIds(ids);
    }

    /**
     * 删除日历配置信息
     * 
     * @param id 日历配置主键
     * @return 结果
     */
    @Override
    public int deleteCalendarConfigById(Long id)
    {
        return calendarConfigMapper.deleteCalendarConfigById(id);
    }

    @Override
    public CalendarConfigDTO getCalendarConfig(Integer year, Integer month) {
        // 获取当月所有配置
        List<CalendarConfig> configs = list(new LambdaQueryWrapper<CalendarConfig>()
                .eq(CalendarConfig::getYear, year).eq(CalendarConfig::getMonth, month));
        Map<Integer, CalendarConfig> configMap = configs.stream().collect(Collectors.toMap(CalendarConfig::getDay, Function.identity()));
        Map<Integer, CalendarDayDTO> monthDays = new LinkedHashMap<>();
        List<String> calendarConfigs = Lists.newArrayList();
        LocalDate startDate = LocalDate.of(year, month, 1);
        // 获取这个月有多少天
        int lengthOfMonth = startDate.lengthOfMonth();
        // 遍历每一天
        for (int i = 0; i < lengthOfMonth; i++) {
            LocalDate currentDate = startDate.plusDays(i);
            Integer day = currentDate.getDayOfMonth();
            CalendarConfig calendarConfig = configMap.get(day);
            Integer dayType;
            boolean isDeadDay = false;
            if (Objects.isNull(calendarConfig)) {
                // 按照默认，周六周日为休息日，其余为工作日
                if (currentDate.getDayOfWeek().getValue() == 6 || currentDate.getDayOfWeek().getValue() == 7) {
                    dayType = WEEK_DAY_TYPE;
                } else {
                    dayType = WORK_DAY_TYPE;
                }
            } else {
                StringBuilder configBuilder = new StringBuilder();
                configBuilder.append(currentDate);
                boolean hasDayType = false;
                boolean hasIsDeadDay = false;
                if (!Objects.isNull(calendarConfig.getDayType())) {
                    if (Objects.equals(WORK_DAY_TYPE, calendarConfig.getDayType())) {
                        dayType = WORK_DAY_TYPE;
                        configBuilder.append(" 工作日");
                    } else {
                        dayType = WEEK_DAY_TYPE;
                        configBuilder.append(" 休息日");
                    }
                    hasDayType = true;
                } else {
                    // 按照默认，周六周日为休息日，其余为工作日
                    if (currentDate.getDayOfWeek().getValue() == 6 || currentDate.getDayOfWeek().getValue() == 7) {
                        dayType = WEEK_DAY_TYPE;
                    } else {
                        dayType = WORK_DAY_TYPE;
                    }
                }
                if (!Objects.isNull(calendarConfig.getIsDeadDay()) && calendarConfig.getIsDeadDay()) {
                    isDeadDay = true;
                    if (hasDayType) {
                        configBuilder.append("&截止日");
                    } else {
                        configBuilder.append(" 截止日");
                    }
                    hasIsDeadDay = true;
                }
                if (!StringUtils.isEmpty(calendarConfig.getRemark())) {
                    if (hasDayType || hasIsDeadDay) {
                        configBuilder.append("，");
                    } else {
                        configBuilder.append(" ");
                    }
                    configBuilder.append(calendarConfig.getRemark());
                }
                calendarConfigs.add(configBuilder.toString());
            }
            monthDays.put(day, CalendarDayDTO.builder().isDeadDay(isDeadDay).dayType(dayType).build());
        }
        return CalendarConfigDTO.builder()
                .monthDays(monthDays)
                .calendarConfigs(calendarConfigs)
                .build();
    }

    @Override
    public Boolean checkDateIsWorkDay(Integer year, Integer month, Integer day) {
        CalendarConfig config = getOne(new LambdaQueryWrapper<CalendarConfig>()
                .eq(CalendarConfig::getYear, year).eq(CalendarConfig::getMonth, month).eq(CalendarConfig::getDay, day));
        if (!Objects.isNull(config) && !Objects.isNull(config.getDayType())) {
            return Objects.equals(config.getDayType(), WORK_DAY_TYPE);
        }
        LocalDate date = LocalDate.of(year, month, day);
        int value = date.getDayOfWeek().getValue();
        return value <= 5;
    }

    public static void iterateAllDates(int year, int monthValue) {
        // 创建当月的第一天
        LocalDate startDate = LocalDate.of(year, monthValue, 1);

        // 获取这个月有多少天
        int lengthOfMonth = startDate.lengthOfMonth();

        // 遍历每一天
        for (int i = 0; i < lengthOfMonth; i++) {
            LocalDate currentDate = startDate.plusDays(i);
            System.out.println(currentDate.getDayOfMonth());
        }
    }

    // 测试一下
    public static void main(String[] args) {
        int year = 2025;
        int month = 2; // 四月
        iterateAllDates(year, month);
    }
}
