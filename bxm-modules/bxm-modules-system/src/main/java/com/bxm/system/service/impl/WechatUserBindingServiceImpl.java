package com.bxm.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.common.core.utils.DateUtils;
import com.bxm.system.service.IWechatUserBindingService;
import com.bxm.system.domain.WechatUserBinding;
import com.bxm.system.mapper.WechatUserBindingMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 企微用户与系统用户关联Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-06-23
 */
@Service
public class WechatUserBindingServiceImpl extends ServiceImpl<WechatUserBindingMapper, WechatUserBinding> implements IWechatUserBindingService
{
    @Autowired
    private WechatUserBindingMapper wechatUserBindingMapper;

    /**
     * 查询企微用户与系统用户关联
     * 
     * @param id 企微用户与系统用户关联主键
     * @return 企微用户与系统用户关联
     */
    @Override
    public WechatUserBinding selectWechatUserBindingById(Long id)
    {
        return wechatUserBindingMapper.selectWechatUserBindingById(id);
    }

    /**
     * 查询企微用户与系统用户关联列表
     * 
     * @param wechatUserBinding 企微用户与系统用户关联
     * @return 企微用户与系统用户关联
     */
    @Override
    public List<WechatUserBinding> selectWechatUserBindingList(WechatUserBinding wechatUserBinding)
    {
        return wechatUserBindingMapper.selectWechatUserBindingList(wechatUserBinding);
    }

    /**
     * 新增企微用户与系统用户关联
     * 
     * @param wechatUserBinding 企微用户与系统用户关联
     * @return 结果
     */
    @Override
    public int insertWechatUserBinding(WechatUserBinding wechatUserBinding)
    {
        wechatUserBinding.setCreateTime(DateUtils.getNowDate());
        return wechatUserBindingMapper.insertWechatUserBinding(wechatUserBinding);
    }

    /**
     * 修改企微用户与系统用户关联
     * 
     * @param wechatUserBinding 企微用户与系统用户关联
     * @return 结果
     */
    @Override
    public int updateWechatUserBinding(WechatUserBinding wechatUserBinding)
    {
        wechatUserBinding.setUpdateTime(DateUtils.getNowDate());
        return wechatUserBindingMapper.updateWechatUserBinding(wechatUserBinding);
    }

    /**
     * 批量删除企微用户与系统用户关联
     * 
     * @param ids 需要删除的企微用户与系统用户关联主键
     * @return 结果
     */
    @Override
    public int deleteWechatUserBindingByIds(Long[] ids)
    {
        return wechatUserBindingMapper.deleteWechatUserBindingByIds(ids);
    }

    /**
     * 删除企微用户与系统用户关联信息
     * 
     * @param id 企微用户与系统用户关联主键
     * @return 结果
     */
    @Override
    public int deleteWechatUserBindingById(Long id)
    {
        return wechatUserBindingMapper.deleteWechatUserBindingById(id);
    }
}
