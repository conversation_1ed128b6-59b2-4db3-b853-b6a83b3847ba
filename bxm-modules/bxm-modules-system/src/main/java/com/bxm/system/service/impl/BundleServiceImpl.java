package com.bxm.system.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.common.core.enums.BusinessLogBusinessType;
import com.bxm.common.core.exception.ServiceException;
import com.bxm.common.core.utils.DateUtils;
import com.bxm.common.core.utils.StringUtils;
import com.bxm.common.core.web.domain.CommonOperDTO;
import com.bxm.common.core.web.domain.CommonOperateResultDTO;
import com.bxm.common.security.utils.SecurityUtils;
import com.bxm.system.api.domain.SysUser;
import com.bxm.system.domain.*;
import com.bxm.system.domain.dto.bundle.BundleDTO;
import com.bxm.system.domain.dto.bundle.BundleDetailDTO;
import com.bxm.system.domain.vo.bundle.BundleAddVO;
import com.bxm.system.domain.vo.bundle.BundleDeleteVO;
import com.bxm.system.domain.vo.bundle.BundleModifyVO;
import com.bxm.system.domain.vo.bundle.BundleUpdateMenuVO;
import com.bxm.system.mapper.BundleDeptMapper;
import com.bxm.system.mapper.BundleMapper;
import com.bxm.system.mapper.SysDeptMenuMapper;
import com.bxm.system.mapper.SysMenuMapper;
import com.bxm.system.service.IBundleMenuService;
import com.bxm.system.service.IBundleService;
import com.bxm.system.service.IBusinessLogService;
import com.google.common.collect.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 套餐Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-04-17
 */
@Service
public class BundleServiceImpl extends ServiceImpl<BundleMapper, Bundle> implements IBundleService
{
    @Autowired
    private BundleMapper bundleMapper;

    @Autowired
    private BundleDeptMapper bundleDeptMapper;

    @Autowired
    private IBundleMenuService bundleMenuService;

    @Autowired
    private IBusinessLogService businessLogService;

    @Autowired
    private SysMenuMapper sysMenuMapper;

    @Autowired
    private SysDeptMenuMapper sysDeptMenuMapper;

    /**
     * 查询套餐
     * 
     * @param id 套餐主键
     * @return 套餐
     */
    @Override
    public Bundle selectBundleById(Long id)
    {
        return bundleMapper.selectBundleById(id);
    }

    /**
     * 查询套餐列表
     * 
     * @param bundle 套餐
     * @return 套餐
     */
    @Override
    public List<Bundle> selectBundleList(Bundle bundle)
    {
        return bundleMapper.selectBundleList(bundle);
    }

    /**
     * 新增套餐
     * 
     * @param bundle 套餐
     * @return 结果
     */
    @Override
    public int insertBundle(Bundle bundle)
    {
        bundle.setCreateTime(DateUtils.getNowDate());
        return bundleMapper.insertBundle(bundle);
    }

    /**
     * 修改套餐
     * 
     * @param bundle 套餐
     * @return 结果
     */
    @Override
    public int updateBundle(Bundle bundle)
    {
        bundle.setUpdateTime(DateUtils.getNowDate());
        return bundleMapper.updateBundle(bundle);
    }

    /**
     * 批量删除套餐
     * 
     * @param ids 需要删除的套餐主键
     * @return 结果
     */
    @Override
    public int deleteBundleByIds(Long[] ids)
    {
        return bundleMapper.deleteBundleByIds(ids);
    }

    /**
     * 删除套餐信息
     * 
     * @param id 套餐主键
     * @return 结果
     */
    @Override
    public int deleteBundleById(Long id)
    {
        return bundleMapper.deleteBundleById(id);
    }

    @Override
    public IPage<BundleDTO> bundleList(Integer pageNum, Integer pageSize, String bundleName) {
        IPage<BundleDTO> result = new Page<>();
        IPage<Bundle> iPage = page(new Page<>(pageNum, pageSize), new LambdaQueryWrapper<Bundle>()
                .eq(Bundle::getIsDel, false)
                .like(!StringUtils.isEmpty(bundleName), Bundle::getBundleName, bundleName)
                .orderByDesc(Bundle::getId));
        BeanUtils.copyProperties(iPage, result);
        if (!ObjectUtils.isEmpty(iPage.getRecords())) {
            Map<Long, List<BundleDept>> bundleDeptMap = bundleDeptMapper.selectList(new LambdaQueryWrapper<BundleDept>()
                            .eq(BundleDept::getIsDel, false)
                            .in(BundleDept::getBundleId, iPage.getRecords().stream().map(Bundle::getId).collect(Collectors.toList())))
                    .stream().collect(Collectors.groupingBy(BundleDept::getBundleId));
            result.setRecords(iPage.getRecords().stream().map(row -> {
                List<BundleDept> bundleDepts = bundleDeptMap.get(row.getId());
                return BundleDTO.builder()
                        .id(row.getId())
                        .bundleName(row.getBundleName())
                        .remark(row.getRemark())
                        .deptCount(ObjectUtils.isEmpty(bundleDepts) ? 0 : bundleDepts.size())
                        .build();
            }).collect(Collectors.toList()));
        }
        return result;
    }

    @Override
    @Transactional
    public void createBundle(BundleAddVO vo, Long deptId) {
        if (ObjectUtils.isEmpty(vo.getMenuIds())) {
            throw new ServiceException("权限不能为空");
        }
        if (checkBundleNameExists(vo.getBundleName(), null)) {
            throw new ServiceException("套餐名称已存在");
        }
        SysUser currentUser = SecurityUtils.getLoginUser().getSysUser();
        Bundle bundle = new Bundle().setBundleName(vo.getBundleName())
                .setRemark(StringUtils.isEmpty(vo.getRemark()) ? "" : vo.getRemark());
        bundle.setCreateBy(currentUser.getUserName());
        save(bundle);

        // 保存bundle和bundleMenu的关系
        Long bundleId = bundle.getId();
        saveBundleMenu(vo.getMenuIds(), bundleId);
        saveBusinessLog(bundleId, currentUser, "新建", deptId, null, null);
    }

    @Override
    @Transactional
    public void modifyBundle(BundleModifyVO vo, Long deptId) {
        if (ObjectUtils.isEmpty(vo.getMenuIds())) {
            throw new ServiceException("权限不能为空");
        }
        Bundle bundle = selectById(vo.getId());
        if (Objects.isNull(bundle)) {
            throw new ServiceException("套餐不存在");
        }
        if (checkBundleNameExists(vo.getBundleName(), vo.getId())) {
            throw new ServiceException("套餐名称已存在");
        }
        SysUser currentUser = SecurityUtils.getLoginUser().getSysUser();
        Bundle update = new Bundle().setId(vo.getId()).setBundleName(vo.getBundleName())
                .setRemark(StringUtils.isEmpty(vo.getRemark()) ? "" : vo.getRemark());
        bundle.setUpdateBy(currentUser.getUserName());
        updateById(update);

        List<SysMenu> oldMenus = bundleMenuService.selectMenusByBundleId(vo.getId());
        List<SysMenu> newMenus = sysMenuMapper.selectBatchIds(vo.getMenuIds());
        bundleMenuService.logicDeleteByBundleIdAndMenuIds(vo.getId(), oldMenus.stream().map(SysMenu::getMenuId).collect(Collectors.toList()));
        saveBundleMenu(vo.getMenuIds(), vo.getId());
        List<Long> bundleDeptIds = bundleDeptMapper.selectList(new LambdaQueryWrapper<BundleDept>()
                .eq(BundleDept::getBundleId, vo.getId()).eq(BundleDept::getIsDel, false).select(BundleDept::getDeptId))
                .stream().map(BundleDept::getDeptId).distinct().collect(Collectors.toList());
        if (!ObjectUtils.isEmpty(bundleDeptIds)) {
            sysDeptMenuMapper.deleteByDeptIds(bundleDeptIds);
            sysDeptMenuMapper.saveDeptMenuByDeptIdsAndBundleId(bundleDeptIds, vo.getId());
        }
        Map<String, Object> operContent = buildModifyOperContent(bundle, vo, oldMenus, newMenus);
        saveBusinessLog(vo.getId(), currentUser, "编辑", deptId, operContent, null);
    }

    @Override
    @Transactional
    public CommonOperateResultDTO deleteBundle(BundleDeleteVO vo, Long deptId) {
        SysUser currentUser = SecurityUtils.getLoginUser().getSysUser();
        if (!Objects.isNull(vo.getId())) {
            deleteBundleSingle(vo.getId(), currentUser, deptId);
            return null;
        } else {
            CommonOperateResultDTO result = new CommonOperateResultDTO();
            List<Bundle> totalList = list(new LambdaQueryWrapper<Bundle>().eq(Bundle::getIsDel, false)
                    .in(Bundle::getId, vo.getIds()));
            if (ObjectUtils.isEmpty(totalList)) {
                return result;
            }
            List<CommonOperDTO> total = totalList.stream().map(row -> CommonOperDTO.builder().id(row.getId()).name(row.getBundleName()).build()).collect(Collectors.toList());
            result.setTotalList(total);
            List<CommonOperDTO> success = Lists.newArrayList();
            List<CommonOperDTO> fail = Lists.newArrayList();
            Map<Long, List<BundleDept>> bundleDeptMap = bundleDeptMapper.selectList(new LambdaQueryWrapper<BundleDept>()
                            .eq(BundleDept::getIsDel, false)
                            .in(BundleDept::getBundleId, total.stream().map(CommonOperDTO::getId).collect(Collectors.toList())))
                    .stream().collect(Collectors.groupingBy(BundleDept::getBundleId));
            total.forEach(row -> {
                if (bundleDeptMap.containsKey(row.getId())) {
                    fail.add(row);
                } else {
                    success.add(row);
                }
            });
            if (!ObjectUtils.isEmpty(success)) {
                success.forEach(row -> deleteBundle(row.getId(), currentUser, deptId));
            }
            result.setSuccessList(success);
            result.setFailList(fail);
            return result;
        }
    }

    @Override
    @Transactional
    public void addBundleMenu(BundleUpdateMenuVO vo, Long deptId) {
        if (ObjectUtils.isEmpty(vo.getIds())) {
            throw new ServiceException("请选择要操作的套餐");
        }
        if (ObjectUtils.isEmpty(vo.getMenuIds())) {
            throw new ServiceException("请选择要添加的权限");
        }
        List<SysMenu> menus = sysMenuMapper.selectBatchIds(vo.getMenuIds());
        if (ObjectUtils.isEmpty(menus)) {
            throw new ServiceException("权限不存在");
        }
        List<Bundle> bundles = list(new LambdaQueryWrapper<Bundle>().eq(Bundle::getIsDel, false)
                .in(Bundle::getId, vo.getIds()));
        if (ObjectUtils.isEmpty(bundles)) {
            return;
        }
        SysUser currentUser = SecurityUtils.getLoginUser().getSysUser();
        Map<Long, List<BundleMenu>> bundleMenuMap = bundleMenuService.selectByBundleIds(bundles.stream().map(Bundle::getId).collect(Collectors.toList()))
                .stream().collect(Collectors.groupingBy(BundleMenu::getBundleId));
        bundles.forEach(bundle -> createBundleMenu(bundle, bundleMenuMap, menus, currentUser, deptId));
    }

    @Override
    @Transactional
    public void deleteBundleMenu(BundleUpdateMenuVO vo, Long deptId) {
        if (ObjectUtils.isEmpty(vo.getIds())) {
            throw new ServiceException("请选择要操作的套餐");
        }
        if (ObjectUtils.isEmpty(vo.getMenuIds())) {
            throw new ServiceException("请选择要删除的权限");
        }
        List<SysMenu> menus = sysMenuMapper.selectBatchIds(vo.getMenuIds());
        if (ObjectUtils.isEmpty(menus)) {
            throw new ServiceException("权限不存在");
        }
        List<Bundle> bundles = list(new LambdaQueryWrapper<Bundle>().eq(Bundle::getIsDel, false)
                .in(Bundle::getId, vo.getIds()));
        if (ObjectUtils.isEmpty(bundles)) {
            return;
        }
        SysUser currentUser = SecurityUtils.getLoginUser().getSysUser();
        Map<Long, List<BundleMenu>> bundleMenuMap = bundleMenuService.selectByBundleIds(bundles.stream().map(Bundle::getId).collect(Collectors.toList()))
                .stream().collect(Collectors.groupingBy(BundleMenu::getBundleId));
        bundles.forEach(bundle -> removeBundleMenu(bundle, bundleMenuMap, menus, currentUser, deptId));
    }

    @Override
    public List<BundleDTO> bundleSelectList() {
        return list(new LambdaQueryWrapper<Bundle>()
                .eq(Bundle::getIsDel, false)).stream().map(row -> BundleDTO.builder()
                .id(row.getId())
                .bundleName(row.getBundleName())
                .build()).collect(Collectors.toList());
    }

    @Override
    public BundleDetailDTO detail(Long id) {
        Bundle bundle = selectById(id);
        if (Objects.isNull(bundle)) {
            throw new ServiceException("套餐不存在");
        }
        return BundleDetailDTO.builder()
                .id(bundle.getId())
                .bundleName(bundle.getBundleName())
                .remark(bundle.getRemark())
                .menuIds(bundleMenuService.selectMenuIdsByBundleId(bundle.getId()))
                .build();
    }

    public Bundle selectById(Long bundleId) {
        if (Objects.isNull(bundleId)) {
            return null;
        }
        return getOne(new LambdaQueryWrapper<Bundle>().eq(Bundle::getId, bundleId).eq(Bundle::getIsDel, false));
    }

    private Boolean checkBundleNameExists(String bundleName, Long id) {
        return count(new LambdaQueryWrapper<Bundle>()
                .eq(Bundle::getIsDel, false)
                .eq(Bundle::getBundleName, bundleName)
                .ne(!Objects.isNull(id), Bundle::getId, id)) > 0;
    }

    private void saveBusinessLog(Long businessId, SysUser currentUser, String operType, Long deptId, Map<String, Object> operContent, String operRemark) {
        BusinessLog businessLog = new BusinessLog().setBusinessId(businessId)
                .setBusinessType(BusinessLogBusinessType.BUNDLE.getCode())
                .setOperName(currentUser.getNickName())
                .setOperType(operType)
                .setOperUserId(currentUser.getUserId())
                .setDeptName("")
                .setDeptId(deptId)
                .setOperContent(ObjectUtils.isEmpty(operContent) ? "" : JSONObject.toJSONString(operContent))
                .setOperRemark(operRemark);

        businessLogService.insertBusinessLog(businessLog);
    }

    private void saveBundleMenu(List<Long> menuIds, Long bundleId) {
        if (ObjectUtils.isEmpty(menuIds) || Objects.isNull(bundleId)) {
            return;
        }
        bundleMenuService.saveBatch(menuIds.stream().map(menuId -> new BundleMenu().setBundleId(bundleId).setMenuId(menuId).setIsDel(false)).collect(Collectors.toList()));
    }

    private Map<String, Object> buildModifyOperContent(Bundle bundle, BundleModifyVO vo, List<SysMenu> oldMenus, List<SysMenu> newMenus) {
        Map<String, Object> operContent = new LinkedHashMap<>();

        // 检查套餐名是否变更
        if (!Objects.equals(bundle.getBundleName(), vo.getBundleName())) {
            operContent.put("套餐名", vo.getBundleName());
        }

        // 检查备注是否变更
        if (!Objects.equals(bundle.getRemark(), vo.getRemark())) {
            if (StringUtils.isEmpty(vo.getRemark())) {
                operContent.put("备注", "清空");
            } else {
                operContent.put("备注", vo.getRemark());
            }
        }

        // 获取旧菜单和新菜单的 ID 集合
        Set<Long> oldMenuIds = oldMenus.stream().map(SysMenu::getMenuId).collect(Collectors.toSet());
        Set<Long> newMenuIds = newMenus.stream().map(SysMenu::getMenuId).collect(Collectors.toSet());

        // 找出新增的菜单 ID 和删除的菜单 ID
        Set<Long> addedMenuIds = new HashSet<>(newMenuIds);
        addedMenuIds.removeAll(oldMenuIds);

        Set<Long> removedMenuIds = new HashSet<>(oldMenuIds);
        removedMenuIds.removeAll(newMenuIds);

        // 构建新增权限信息
        if (!addedMenuIds.isEmpty()) {
            String addedMenus = newMenus.stream()
                    .filter(menu -> addedMenuIds.contains(menu.getMenuId()))
                    .map(menu -> menu.getMenuId() + menu.getMenuName())
                    .collect(Collectors.joining("，"));
            operContent.put("增加权限", addedMenus);
        }

        // 构建删除权限信息
        if (!removedMenuIds.isEmpty()) {
            String removedMenus = oldMenus.stream()
                    .filter(menu -> removedMenuIds.contains(menu.getMenuId()))
                    .map(menu -> menu.getMenuId() + menu.getMenuName())
                    .collect(Collectors.joining("，"));
            operContent.put("删除权限", removedMenus);
        }

        return operContent;
    }

    private void deleteBundleSingle(Long id, SysUser currentUser, Long deptId) {
        Bundle bundle = selectById(id);
        if (Objects.isNull(bundle)) {
            throw new ServiceException("套餐不存在");
        }
        if (existsBundleDept(id)) {
            throw new ServiceException("套餐已被组织应用，无法删除");
        }
        deleteBundle(id, currentUser, deptId);
    }

    private void deleteBundle(Long id, SysUser currentUser, Long deptId) {
        Bundle update = new Bundle().setId(id).setIsDel(true);
        update.setUpdateBy(currentUser.getUserName());
        updateById(update);
        bundleMenuService.logicDeleteByBundleId(id);
        saveBusinessLog(id, currentUser, "删除", deptId, null, null);
    }

    private boolean existsBundleDept(Long id) {
        return bundleDeptMapper.selectCount(new LambdaQueryWrapper<BundleDept>()
                .eq(BundleDept::getBundleId, id)
                .eq(BundleDept::getIsDel, false)) > 0;
    }

    private void createBundleMenu(Bundle bundle, Map<Long, List<BundleMenu>> bundleMenuMap, List<SysMenu> menus, SysUser currentUser, Long deptId) {
        List<Long> oldMenuIds = bundleMenuMap.getOrDefault(bundle.getId(), Lists.newArrayList())
                .stream().map(BundleMenu::getMenuId).collect(Collectors.toList());
        List<Long> newMenuIds = menus.stream().map(SysMenu::getMenuId).collect(Collectors.toList());
        List<Long> addMenuIds = newMenuIds.stream().filter(menuId -> !oldMenuIds.contains(menuId)).collect(Collectors.toList());
        if (ObjectUtils.isEmpty(addMenuIds)) {
            return;
        }
        bundleMenuService.saveBatch(addMenuIds.stream().map(menuId -> new BundleMenu().setBundleId(bundle.getId()).setMenuId(menuId).setIsDel(false)).collect(Collectors.toList()));
        List<Long> bundleDeptIds = bundleDeptMapper.selectList(new LambdaQueryWrapper<BundleDept>()
                        .eq(BundleDept::getBundleId, bundle.getId()).eq(BundleDept::getIsDel, false).select(BundleDept::getDeptId))
                .stream().map(BundleDept::getDeptId).distinct().collect(Collectors.toList());
        if (!ObjectUtils.isEmpty(bundleDeptIds)) {
            sysDeptMenuMapper.deleteByDeptIds(bundleDeptIds);
            sysDeptMenuMapper.saveDeptMenuByDeptIdsAndBundleId(bundleDeptIds, bundle.getId());
        }
        Map<String, Object> operContent = new LinkedHashMap<>();
        String addedMenus = menus.stream()
                .filter(menu -> addMenuIds.contains(menu.getMenuId()))
                .map(menu -> menu.getMenuId() + menu.getMenuName())
                .collect(Collectors.joining("，"));
        operContent.put("增加权限", addedMenus);
        saveBusinessLog(bundle.getId(), currentUser, "批量增加权限", deptId, operContent, null);
    }

    private void removeBundleMenu(Bundle bundle, Map<Long, List<BundleMenu>> bundleMenuMap, List<SysMenu> menus, SysUser currentUser, Long deptId) {
        List<Long> oldMenuIds = bundleMenuMap.getOrDefault(bundle.getId(), Lists.newArrayList())
                .stream().map(BundleMenu::getMenuId).collect(Collectors.toList());
        List<Long> newMenuIds = menus.stream().map(SysMenu::getMenuId).collect(Collectors.toList());
        List<Long> removeMenuIds = newMenuIds.stream().filter(oldMenuIds::contains).collect(Collectors.toList());
        if (ObjectUtils.isEmpty(removeMenuIds)) {
            return;
        }
        bundleMenuService.logicDeleteByBundleIdAndMenuIds(bundle.getId(), removeMenuIds);
        List<Long> bundleDeptIds = bundleDeptMapper.selectList(new LambdaQueryWrapper<BundleDept>()
                        .eq(BundleDept::getBundleId, bundle.getId()).eq(BundleDept::getIsDel, false).select(BundleDept::getDeptId))
                .stream().map(BundleDept::getDeptId).distinct().collect(Collectors.toList());
        if (!ObjectUtils.isEmpty(bundleDeptIds)) {
            sysDeptMenuMapper.deleteByDeptIds(bundleDeptIds);
            sysDeptMenuMapper.saveDeptMenuByDeptIdsAndBundleId(bundleDeptIds, bundle.getId());
        }
        Map<String, Object> operContent = new LinkedHashMap<>();
        String removedMenus = menus.stream()
                .filter(menu -> removeMenuIds.contains(menu.getMenuId()))
                .map(menu -> menu.getMenuId() + menu.getMenuName())
                .collect(Collectors.joining("，"));
        operContent.put("删除权限", removedMenus);
        saveBusinessLog(bundle.getId(), currentUser, "批量删除权限", deptId, operContent, null);
    }
}
