package com.bxm.system.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DeptTreeRequest {
    @ApiModelProperty("起点组织级别，1、2、3、4")
    private Integer startLevel; // 起点组织级别
    @ApiModelProperty("终点组织级别，1、2、3、4")
    private Integer endLevel;   // 终点组织级别
    @ApiModelProperty("是否显示员工名，true、false")
    private Boolean showEmployees; // 是否显示员工名
    @ApiModelProperty("部门类型，1-业务组织，2-工厂组织")
    private Integer deptType;   // 部门类型（可选）
    @ApiModelProperty("是否过滤总部，true、false")
    private Boolean filterHeadquarters; // 是否过滤总部
    @ApiModelProperty("是否过滤职能，true、false")
    private Boolean filterFunction; // 是否过滤总部
    @ApiModelProperty("数据权限类型，1-全局，2-默认数据权限")
    private Integer dataScopeType;//
    @ApiModelProperty("指定集团id")
    private Long topDeptId;
    @ApiModelProperty("是否显示停用员工，true、false")
    private Boolean isShowInValidEmployee;

    // Getters and Setters
}