package com.bxm.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bxm.common.core.annotation.Excel;
import com.bxm.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 业务公司余额变动明细对象 sys_dept_account_balance_detail
 * 
 * <AUTHOR>
 * @date 2024-09-20
 */
@Data
@ApiModel("业务公司余额变动明细对象")
@Accessors(chain = true)
@TableName("sys_dept_account_balance_detail")
public class SysDeptAccountBalanceDetail extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    @TableId(value = "id", type = IdType.AUTO)
    @Excel(name = "id")
    @ApiModelProperty(value = "id")
    private Long id;

    /** 业务公司id */
    @Excel(name = "业务公司id")
    @TableField("business_dept_id")
    @ApiModelProperty(value = "业务公司id")
    private Long businessDeptId;

    /** 来源类型，1-结算单（新户预收），2-账单（预存抵扣），3-调入，4-拨出 */
    @Excel(name = "来源类型，1-结算单", readConverterExp = "新=户预收")
    @TableField("source_type")
    @ApiModelProperty(value = "来源类型，1-结算单（新户预收），2-账单（预存抵扣），3-调入，4-拨出")
    private Integer sourceType;

    /** 业务编号 */
    @Excel(name = "业务编号")
    @TableField("business_no")
    @ApiModelProperty(value = "业务编号")
    private String businessNo;

    /** 业务类型，1-结算单，2-账单 */
    @Excel(name = "业务类型，1-结算单，2-账单")
    @TableField("business_type")
    @ApiModelProperty(value = "业务类型，1-结算单，2-账单")
    private Integer businessType;

    /** 业务id */
    @Excel(name = "业务id")
    @TableField("business_id")
    @ApiModelProperty(value = "业务id")
    private Long businessId;

    /** 收支类型，1-收入，2-支出 */
    @Excel(name = "收支类型，1-收入，2-支出")
    @TableField("income_type")
    @ApiModelProperty(value = "收支类型，1-收入，2-支出")
    private Integer incomeType;

    /** 变化金额 */
    @Excel(name = "变化金额")
    @TableField("change_amount")
    @ApiModelProperty(value = "变化金额")
    private BigDecimal changeAmount;

    /** 状态，1-待确认，2-已取消，3-已确认 */
    @Excel(name = "状态，1-待确认，2-已取消，3-已确认")
    @TableField("status")
    @ApiModelProperty(value = "状态，1-待确认，2-已取消，3-已确认")
    private Integer status;

    /** 备注 */
    @Excel(name = "备注")
    @TableField("remark")
    @ApiModelProperty(value = "备注")
    private String remark;

}
