package com.bxm.system.domain.vo.companyWechat;

import com.bxm.system.domain.dto.companyWechat.WechatFileResultDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SendMessageVO {

    @ApiModelProperty("发送对象用户id列表")
    private List<Long> userIds;

    @ApiModelProperty("小组id")
    private Long deptId;

    @ApiModelProperty("小组名称")
    private String deptName;

    @ApiModelProperty("关联数据")
    private String relationContent;

    @ApiModelProperty("消息内容")
    private String content;

    @ApiModelProperty("附件，传上传素材之后返回的数组")
    private List<WechatFileResultDTO> files;

    @ApiModelProperty("业务id")
    private Long businessId;

    @ApiModelProperty("业务类型，和获取操作记录传值一致")
    private Integer businessType;

    @ApiModelProperty("交付单状态，和miniList传值一致即可")
    private Integer deliverStatus;
}
