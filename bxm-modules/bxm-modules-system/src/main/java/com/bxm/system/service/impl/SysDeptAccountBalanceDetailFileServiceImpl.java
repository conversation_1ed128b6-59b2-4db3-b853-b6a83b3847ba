package com.bxm.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.common.core.utils.DateUtils;
import com.bxm.system.domain.SysDeptAccountBalanceDetailFile;
import com.bxm.system.mapper.SysDeptAccountBalanceDetailFileMapper;
import com.bxm.system.service.ISysDeptAccountBalanceDetailFileService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * 业务公司余额变动明细附件Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-09-20
 */
@Service
public class SysDeptAccountBalanceDetailFileServiceImpl extends ServiceImpl<SysDeptAccountBalanceDetailFileMapper, SysDeptAccountBalanceDetailFile> implements ISysDeptAccountBalanceDetailFileService
{
    @Autowired
    private SysDeptAccountBalanceDetailFileMapper sysDeptAccountBalanceDetailFileMapper;

    /**
     * 查询业务公司余额变动明细附件
     * 
     * @param id 业务公司余额变动明细附件主键
     * @return 业务公司余额变动明细附件
     */
    @Override
    public SysDeptAccountBalanceDetailFile selectSysDeptAccountBalanceDetailFileById(Long id)
    {
        return sysDeptAccountBalanceDetailFileMapper.selectSysDeptAccountBalanceDetailFileById(id);
    }

    /**
     * 查询业务公司余额变动明细附件列表
     * 
     * @param sysDeptAccountBalanceDetailFile 业务公司余额变动明细附件
     * @return 业务公司余额变动明细附件
     */
    @Override
    public List<SysDeptAccountBalanceDetailFile> selectSysDeptAccountBalanceDetailFileList(SysDeptAccountBalanceDetailFile sysDeptAccountBalanceDetailFile)
    {
        return sysDeptAccountBalanceDetailFileMapper.selectSysDeptAccountBalanceDetailFileList(sysDeptAccountBalanceDetailFile);
    }

    /**
     * 新增业务公司余额变动明细附件
     * 
     * @param sysDeptAccountBalanceDetailFile 业务公司余额变动明细附件
     * @return 结果
     */
    @Override
    public int insertSysDeptAccountBalanceDetailFile(SysDeptAccountBalanceDetailFile sysDeptAccountBalanceDetailFile)
    {
        sysDeptAccountBalanceDetailFile.setCreateTime(DateUtils.getNowDate());
        return sysDeptAccountBalanceDetailFileMapper.insertSysDeptAccountBalanceDetailFile(sysDeptAccountBalanceDetailFile);
    }

    /**
     * 修改业务公司余额变动明细附件
     * 
     * @param sysDeptAccountBalanceDetailFile 业务公司余额变动明细附件
     * @return 结果
     */
    @Override
    public int updateSysDeptAccountBalanceDetailFile(SysDeptAccountBalanceDetailFile sysDeptAccountBalanceDetailFile)
    {
        sysDeptAccountBalanceDetailFile.setUpdateTime(DateUtils.getNowDate());
        return sysDeptAccountBalanceDetailFileMapper.updateSysDeptAccountBalanceDetailFile(sysDeptAccountBalanceDetailFile);
    }

    /**
     * 批量删除业务公司余额变动明细附件
     * 
     * @param ids 需要删除的业务公司余额变动明细附件主键
     * @return 结果
     */
    @Override
    public int deleteSysDeptAccountBalanceDetailFileByIds(Long[] ids)
    {
        return sysDeptAccountBalanceDetailFileMapper.deleteSysDeptAccountBalanceDetailFileByIds(ids);
    }

    /**
     * 删除业务公司余额变动明细附件信息
     * 
     * @param id 业务公司余额变动明细附件主键
     * @return 结果
     */
    @Override
    public int deleteSysDeptAccountBalanceDetailFileById(Long id)
    {
        return sysDeptAccountBalanceDetailFileMapper.deleteSysDeptAccountBalanceDetailFileById(id);
    }

    @Override
    public List<SysDeptAccountBalanceDetailFile> selectBatchByDetailIdAndFileType(List<Long> detailIds, Integer fileType) {
        if (ObjectUtils.isEmpty(detailIds)) {
            return Collections.emptyList();
        }
        return list(new LambdaQueryWrapper<SysDeptAccountBalanceDetailFile>()
                .in(SysDeptAccountBalanceDetailFile::getBalanceDetailId, detailIds)
                .eq(SysDeptAccountBalanceDetailFile::getFileType, fileType));
    }

    @Override
    public List<SysDeptAccountBalanceDetailFile> selectByDetailIdAndFileType(Long detailId, Integer fileType) {
        if (Objects.isNull(detailId)) {
            return Collections.emptyList();
        }
        return list(new LambdaQueryWrapper<SysDeptAccountBalanceDetailFile>()
                .eq(SysDeptAccountBalanceDetailFile::getBalanceDetailId, detailId)
                .eq(SysDeptAccountBalanceDetailFile::getFileType, fileType));
    }
}
