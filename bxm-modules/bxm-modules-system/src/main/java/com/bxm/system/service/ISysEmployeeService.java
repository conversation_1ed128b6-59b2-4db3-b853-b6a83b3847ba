package com.bxm.system.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bxm.system.api.domain.SysDept;
import com.bxm.system.api.domain.SysEmployee;
import com.bxm.system.api.domain.SysUser;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 员工Service接口
 * 
 * <AUTHOR>
 * @date 2024-05-08
 */
public interface ISysEmployeeService extends IService<SysEmployee>
{
    /**
     * 查询员工
     * 
     * @param employeeId 员工主键
     * @return 员工
     */
    public SysEmployee selectSysEmployeeByEmployeeId(Long employeeId);

    /**
     * 查询员工列表
     * 
     * @param sysEmployee 员工
     * @return 员工集合
     */
    public List<SysEmployee> selectSysEmployeeList(SysEmployee sysEmployee);

    /**
     * 新增员工
     * 
     * @param sysEmployee 员工
     * @return 结果
     */
    public int insertSysEmployee(SysEmployee sysEmployee);

    /**
     * 修改员工
     * 
     * @param sysEmployee 员工
     * @return 结果
     */
    public int updateSysEmployee(SysEmployee sysEmployee);

    /**
     * 批量删除员工
     * 
     * @param employeeIds 需要删除的员工主键集合
     * @return 结果
     */
    public int deleteSysEmployeeByEmployeeIds(Long[] employeeIds);

    /**
     * 删除员工信息
     * 
     * @param employeeId 员工主键
     * @return 结果
     */
    public int deleteSysEmployeeByEmployeeId(Long employeeId);

    SysEmployee getEmployeeByUserIdAndDeptId(Long userId, Long deptId);

    List<SysEmployee> getEmployeeListByDeptId(Long deptId);

    List<SysEmployee> getBatchEmployeeByDeptIds(List<Long> deptIds);

    List<SysEmployee> selectBatchByDeptIds(List<Long> deptIds);

    IPage<SysEmployee> employeeList(SysEmployee sysEmployee, Long deptId, Integer pageNum, Integer pageSize);

    List<SysEmployee> getEmployeeListByHeaderDeptId(Long deptId);

    List<SysEmployee> getBatchEmployeeByIds(List<Long> employeeIds);

    List<SysEmployee> getBindWechatEmployeesByDeptId(Long deptId);

    List<SysEmployee> getBatchByUserIds(List<Long> userIds);

    void saveUserDept(SysUser sysUser, List<Long> deptIds, Boolean isLeader, String nickName, String phonenumber);

    List<SysDept> selectDeptsByUserId(Long userId);

    void removeByUserId(Long userId);
}
