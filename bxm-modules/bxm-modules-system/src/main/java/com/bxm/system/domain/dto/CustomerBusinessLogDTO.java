package com.bxm.system.domain.dto;

import com.bxm.common.core.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CustomerBusinessLogDTO {

    @ApiModelProperty("操作记录id")
    private Long id;

    @ApiModelProperty("服务id")
    private Long customerServiceId;

    @ApiModelProperty("客户企业名称")
    @Excel(name = "客户企业名称")
    private String customerName;

    @ApiModelProperty("客户名称")
    @Excel(name = "客户名称")
    private String customerCompanyName;

    @ApiModelProperty("操作人")
    @Excel(name = "操作人")
    private String operName;

    @ApiModelProperty("操作时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime operTime;

    @Excel(name = "操作时间")
    private String operateTime;

    @ApiModelProperty("操作类型")
    @Excel(name = "操作类型")
    private String operType;

    @ApiModelProperty("操作内容")
    @Excel(name = "操作内容")
    private String operContent;
}
