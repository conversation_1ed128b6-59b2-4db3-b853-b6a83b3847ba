package com.bxm.system.domain.dto.dept;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DeptDetailDTO {

    @ApiModelProperty("组织id")
    private Long id;

    @ApiModelProperty("组织层级，1、2")
    private Integer level;

    @ApiModelProperty("归属总部id")
    private Long parentId;

    @ApiModelProperty("组织名称")
    private String deptName;

    @ApiModelProperty("组织类型，1-业务公司，2-会计工厂")
    private Integer deptType;

    @ApiModelProperty("数据权限类型，1-总部，2-职能，3-工作台")
    private Integer dataScopeType;

    @ApiModelProperty("套餐id")
    private Long bundleId;

    @ApiModelProperty("备注")
    private String remark;
}
