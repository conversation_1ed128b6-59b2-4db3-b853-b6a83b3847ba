package com.bxm.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bxm.system.domain.SysDeptAccountBalanceDetailFile;

import java.util.List;

/**
 * 业务公司余额变动明细附件Service接口
 * 
 * <AUTHOR>
 * @date 2024-09-20
 */
public interface ISysDeptAccountBalanceDetailFileService extends IService<SysDeptAccountBalanceDetailFile>
{
    /**
     * 查询业务公司余额变动明细附件
     * 
     * @param id 业务公司余额变动明细附件主键
     * @return 业务公司余额变动明细附件
     */
    public SysDeptAccountBalanceDetailFile selectSysDeptAccountBalanceDetailFileById(Long id);

    /**
     * 查询业务公司余额变动明细附件列表
     * 
     * @param sysDeptAccountBalanceDetailFile 业务公司余额变动明细附件
     * @return 业务公司余额变动明细附件集合
     */
    public List<SysDeptAccountBalanceDetailFile> selectSysDeptAccountBalanceDetailFileList(SysDeptAccountBalanceDetailFile sysDeptAccountBalanceDetailFile);

    /**
     * 新增业务公司余额变动明细附件
     * 
     * @param sysDeptAccountBalanceDetailFile 业务公司余额变动明细附件
     * @return 结果
     */
    public int insertSysDeptAccountBalanceDetailFile(SysDeptAccountBalanceDetailFile sysDeptAccountBalanceDetailFile);

    /**
     * 修改业务公司余额变动明细附件
     * 
     * @param sysDeptAccountBalanceDetailFile 业务公司余额变动明细附件
     * @return 结果
     */
    public int updateSysDeptAccountBalanceDetailFile(SysDeptAccountBalanceDetailFile sysDeptAccountBalanceDetailFile);

    /**
     * 批量删除业务公司余额变动明细附件
     * 
     * @param ids 需要删除的业务公司余额变动明细附件主键集合
     * @return 结果
     */
    public int deleteSysDeptAccountBalanceDetailFileByIds(Long[] ids);

    /**
     * 删除业务公司余额变动明细附件信息
     * 
     * @param id 业务公司余额变动明细附件主键
     * @return 结果
     */
    public int deleteSysDeptAccountBalanceDetailFileById(Long id);

    List<SysDeptAccountBalanceDetailFile> selectBatchByDetailIdAndFileType(List<Long> detailIds, Integer fileType);

    List<SysDeptAccountBalanceDetailFile> selectByDetailIdAndFileType(Long detailId, Integer fileType);
}
