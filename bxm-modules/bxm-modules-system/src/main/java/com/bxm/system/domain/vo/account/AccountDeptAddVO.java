package com.bxm.system.domain.vo.account;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AccountDeptAddVO {

    @ApiModelProperty("上级组织id")
    @NotNull(message = "上级组织不能为空")
    private Long parentId;

    @ApiModelProperty("集团id")
    @NotNull(message = "集团id不能为空")
    private Long topDeptId;

    @ApiModelProperty("部门名")
    @NotEmpty(message = "部门名不能为空")
    @Length(max = 20, message = "部门名不能超过20个字")
    private String deptName;

    @ApiModelProperty("容量")
    private Long capacity;
}
