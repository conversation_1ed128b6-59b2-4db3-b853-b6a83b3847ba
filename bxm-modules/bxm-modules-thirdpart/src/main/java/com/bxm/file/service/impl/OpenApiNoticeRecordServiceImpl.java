package com.bxm.file.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.common.core.utils.DateUtils;
import com.bxm.file.domain.OpenApiNoticeRecord;
import com.bxm.file.mapper.OpenApiNoticeRecordMapper;
import com.bxm.file.service.IOpenApiNoticeRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 第三方通知/被通知记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-02-16
 */
@Service
public class OpenApiNoticeRecordServiceImpl extends ServiceImpl<OpenApiNoticeRecordMapper, OpenApiNoticeRecord> implements IOpenApiNoticeRecordService
{
    @Autowired
    private OpenApiNoticeRecordMapper openApiNoticeRecordMapper;

    /**
     * 查询第三方通知/被通知记录
     * 
     * @param id 第三方通知/被通知记录主键
     * @return 第三方通知/被通知记录
     */
    @Override
    public OpenApiNoticeRecord selectOpenApiNoticeRecordById(Long id)
    {
        return openApiNoticeRecordMapper.selectOpenApiNoticeRecordById(id);
    }

    /**
     * 查询第三方通知/被通知记录列表
     * 
     * @param openApiNoticeRecord 第三方通知/被通知记录
     * @return 第三方通知/被通知记录
     */
    @Override
    public List<OpenApiNoticeRecord> selectOpenApiNoticeRecordList(OpenApiNoticeRecord openApiNoticeRecord)
    {
        return openApiNoticeRecordMapper.selectOpenApiNoticeRecordList(openApiNoticeRecord);
    }

    /**
     * 新增第三方通知/被通知记录
     * 
     * @param openApiNoticeRecord 第三方通知/被通知记录
     * @return 结果
     */
    @Override
    public int insertOpenApiNoticeRecord(OpenApiNoticeRecord openApiNoticeRecord)
    {
        openApiNoticeRecord.setCreateTime(DateUtils.getNowDate());
        return openApiNoticeRecordMapper.insertOpenApiNoticeRecord(openApiNoticeRecord);
    }

    /**
     * 修改第三方通知/被通知记录
     * 
     * @param openApiNoticeRecord 第三方通知/被通知记录
     * @return 结果
     */
    @Override
    public int updateOpenApiNoticeRecord(OpenApiNoticeRecord openApiNoticeRecord)
    {
        openApiNoticeRecord.setUpdateTime(DateUtils.getNowDate());
        return openApiNoticeRecordMapper.updateOpenApiNoticeRecord(openApiNoticeRecord);
    }

    /**
     * 批量删除第三方通知/被通知记录
     * 
     * @param ids 需要删除的第三方通知/被通知记录主键
     * @return 结果
     */
    @Override
    public int deleteOpenApiNoticeRecordByIds(Long[] ids)
    {
        return openApiNoticeRecordMapper.deleteOpenApiNoticeRecordByIds(ids);
    }

    /**
     * 删除第三方通知/被通知记录信息
     * 
     * @param id 第三方通知/被通知记录主键
     * @return 结果
     */
    @Override
    public int deleteOpenApiNoticeRecordById(Long id)
    {
        return openApiNoticeRecordMapper.deleteOpenApiNoticeRecordById(id);
    }

    @Override
    @Async
    public void saveRecord(OpenApiNoticeRecord record) {
        save(record);
    }
}
