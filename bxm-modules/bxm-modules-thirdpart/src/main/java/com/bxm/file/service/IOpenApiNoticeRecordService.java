package com.bxm.file.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bxm.file.domain.OpenApiNoticeRecord;

import java.util.List;

/**
 * 第三方通知/被通知记录Service接口
 * 
 * <AUTHOR>
 * @date 2025-02-16
 */
public interface IOpenApiNoticeRecordService extends IService<OpenApiNoticeRecord>
{
    /**
     * 查询第三方通知/被通知记录
     * 
     * @param id 第三方通知/被通知记录主键
     * @return 第三方通知/被通知记录
     */
    public OpenApiNoticeRecord selectOpenApiNoticeRecordById(Long id);

    /**
     * 查询第三方通知/被通知记录列表
     * 
     * @param openApiNoticeRecord 第三方通知/被通知记录
     * @return 第三方通知/被通知记录集合
     */
    public List<OpenApiNoticeRecord> selectOpenApiNoticeRecordList(OpenApiNoticeRecord openApiNoticeRecord);

    /**
     * 新增第三方通知/被通知记录
     * 
     * @param openApiNoticeRecord 第三方通知/被通知记录
     * @return 结果
     */
    public int insertOpenApiNoticeRecord(OpenApiNoticeRecord openApiNoticeRecord);

    /**
     * 修改第三方通知/被通知记录
     * 
     * @param openApiNoticeRecord 第三方通知/被通知记录
     * @return 结果
     */
    public int updateOpenApiNoticeRecord(OpenApiNoticeRecord openApiNoticeRecord);

    /**
     * 批量删除第三方通知/被通知记录
     * 
     * @param ids 需要删除的第三方通知/被通知记录主键集合
     * @return 结果
     */
    public int deleteOpenApiNoticeRecordByIds(Long[] ids);

    /**
     * 删除第三方通知/被通知记录信息
     * 
     * @param id 第三方通知/被通知记录主键
     * @return 结果
     */
    public int deleteOpenApiNoticeRecordById(Long id);

    void saveRecord(OpenApiNoticeRecord record);
}
