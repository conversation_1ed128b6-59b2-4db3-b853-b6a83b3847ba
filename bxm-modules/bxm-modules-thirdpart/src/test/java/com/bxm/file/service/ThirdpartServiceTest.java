package com.bxm.file.service;

import com.bxm.file.properties.XqyProperties;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
class ThirdpartServiceTest {

    @Autowired
    private ThirdpartService thirdpartService;

    @Value("${qcc.appkey}")
    private String appkey;

    @Value("${qcc.appsecret}")
    private String appsecret;

    @Value("${xqy.appid}")
    private String xqyAppkey;

    @Value("${xqy.appsecret}")
    private String xqyAppsecret;

    @Autowired
    private XqyProperties xqyProperties;

    @Test
    void xqyQueryTaxDeclarationData() {
        System.out.println(thirdpartService.xqyQueryTaxDeclarationData("91350100MACE5EWE5U","2024-07"));
    }
}