<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bxm.openapi.mapper.SysClientVersionMapper">

    <select id="exportAccountingNotCompleteTotal" resultType="com.bxm.openapi.domain.ExportDTO">
        WITH
            aggregated_ccspe AS (
                SELECT
                    period_id,
                    GROUP_CONCAT(employee_name SEPARATOR ', ') AS accountant_names
                FROM c_customer_service_period_employee
                WHERE period_employee_type = 2
                GROUP BY period_id
            ),
            detail_data AS (
                SELECT
                    ccspm.customer_name AS customer_name,
                    ccspm.credit_code AS credit_code,
                    sd.dept_name AS deptName,
                    aggregated_ccspe.accountant_names AS accountant_names,
                    ccspm.period AS period,
                    ccsia.bank_payment_input_time AS bankPaymentCreateTime,
                    ccsia.in_time AS inAccountCreateTime,
                    ccsia.end_time AS endTime
                FROM c_customer_service_period_month ccspm
                         LEFT JOIN c_customer_service_in_account ccsia ON ccspm.id = ccsia.customer_service_period_month_id AND ccsia.is_del = 0
                         LEFT JOIN aggregated_ccspe ON aggregated_ccspe.period_id = ccspm.id
                         LEFT JOIN sys_dept sd ON ccspm.business_dept_id = sd.dept_id
            )
        SELECT
            deptName AS deptName,
            COUNT(CASE WHEN LEFT(period, 4) = '2024' THEN 1 END) AS fullYearServicePeriods,
            COUNT(CASE WHEN LEFT(period, 4) = '2024' AND bankPaymentCreateTime IS NULL THEN 1 END) AS fullYearUnrecordedBankPayments,
            COUNT(CASE WHEN LEFT(period, 4) = '2024' AND inAccountCreateTime IS NULL THEN 1 END) AS fullYearUnaccountedPeriods,
            COUNT(CASE WHEN LEFT(period, 4) = '2024' AND endTime IS NULL THEN 1 END) AS fullYearUnclosedPeriods,
            COUNT(CASE WHEN period = '202401' THEN 1 END) AS januaryServiceCount,
            COUNT(CASE WHEN period = '202401' AND bankPaymentCreateTime IS NULL THEN 1 END) AS januaryUnrecordedBankPayments,
            COUNT(CASE WHEN period = '202401' AND inAccountCreateTime IS NULL THEN 1 END) AS januaryUnaccountedPeriods,
            COUNT(CASE WHEN period = '202401' AND endTime IS NULL THEN 1 END) AS januaryUnclosedPeriods,
            COUNT(CASE WHEN period = '202402' THEN 1 END) AS februaryServiceCount,
            COUNT(CASE WHEN period = '202402' AND bankPaymentCreateTime IS NULL THEN 1 END) AS februaryUnrecordedBankPayments,
            COUNT(CASE WHEN period = '202402' AND inAccountCreateTime IS NULL THEN 1 END) AS februaryUnaccountedPeriods,
            COUNT(CASE WHEN period = '202402' AND endTime IS NULL THEN 1 END) AS februaryUnclosedPeriods,
            COUNT(CASE WHEN period = '202403' THEN 1 END) AS marchServiceCount,
            COUNT(CASE WHEN period = '202403' AND bankPaymentCreateTime IS NULL THEN 1 END) AS marchUnrecordedBankPayments,
            COUNT(CASE WHEN period = '202403' AND inAccountCreateTime IS NULL THEN 1 END) AS marchUnaccountedPeriods,
            COUNT(CASE WHEN period = '202403' AND endTime IS NULL THEN 1 END) AS marchUnclosedPeriods,
            COUNT(CASE WHEN period = '202404' THEN 1 END) AS aprilServiceCount,
            COUNT(CASE WHEN period = '202404' AND bankPaymentCreateTime IS NULL THEN 1 END) AS aprilUnrecordedBankPayments,
            COUNT(CASE WHEN period = '202404' AND inAccountCreateTime IS NULL THEN 1 END) AS aprilUnaccountedPeriods,
            COUNT(CASE WHEN period = '202404' AND endTime IS NULL THEN 1 END) AS aprilUnclosedPeriods,
            COUNT(CASE WHEN period = '202405' THEN 1 END) AS mayServiceCount,
            COUNT(CASE WHEN period = '202405' AND bankPaymentCreateTime IS NULL THEN 1 END) AS mayUnrecordedBankPayments,
            COUNT(CASE WHEN period = '202405' AND inAccountCreateTime IS NULL THEN 1 END) AS mayUnaccountedPeriods,
            COUNT(CASE WHEN period = '202405' AND endTime IS NULL THEN 1 END) AS mayUnclosedPeriods,
            COUNT(CASE WHEN period = '202406' THEN 1 END) AS juneServiceCount,
            COUNT(CASE WHEN period = '202406' AND bankPaymentCreateTime IS NULL THEN 1 END) AS juneUnrecordedBankPayments,
            COUNT(CASE WHEN period = '202406' AND inAccountCreateTime IS NULL THEN 1 END) AS juneUnaccountedPeriods,
            COUNT(CASE WHEN period = '202406' AND endTime IS NULL THEN 1 END) AS juneUnclosedPeriods,
            COUNT(CASE WHEN period = '202407' THEN 1 END) AS julyServiceCount,
            COUNT(CASE WHEN period = '202407' AND bankPaymentCreateTime IS NULL THEN 1 END) AS julyUnrecordedBankPayments,
            COUNT(CASE WHEN period = '202407' AND inAccountCreateTime IS NULL THEN 1 END) AS julyUnaccountedPeriods,
            COUNT(CASE WHEN period = '202407' AND endTime IS NULL THEN 1 END) AS julyUnclosedPeriods,
            COUNT(CASE WHEN period = '202408' THEN 1 END) AS augustServiceCount,
            COUNT(CASE WHEN period = '202408' AND bankPaymentCreateTime IS NULL THEN 1 END) AS augustUnrecordedBankPayments,
            COUNT(CASE WHEN period = '202408' AND inAccountCreateTime IS NULL THEN 1 END) AS augustUnaccountedPeriods,
            COUNT(CASE WHEN period = '202408' AND endTime IS NULL THEN 1 END) AS augustUnclosedPeriods,
            COUNT(CASE WHEN period = '202409' THEN 1 END) AS septemberServiceCount,
            COUNT(CASE WHEN period = '202409' AND bankPaymentCreateTime IS NULL THEN 1 END) AS septemberUnrecordedBankPayments,
            COUNT(CASE WHEN period = '202409' AND inAccountCreateTime IS NULL THEN 1 END) AS septemberUnaccountedPeriods,
            COUNT(CASE WHEN period = '202409' AND endTime IS NULL THEN 1 END) AS septemberUnclosedPeriods,
            COUNT(CASE WHEN period = '202410' THEN 1 END) AS octoberServiceCount,
            COUNT(CASE WHEN period = '202410' AND bankPaymentCreateTime IS NULL THEN 1 END) AS octoberUnrecordedBankPayments,
            COUNT(CASE WHEN period = '202410' AND inAccountCreateTime IS NULL THEN 1 END) AS octoberUnaccountedPeriods,
            COUNT(CASE WHEN period = '202410' AND endTime IS NULL THEN 1 END) AS octoberUnclosedPeriods,
            COUNT(CASE WHEN period = '202411' THEN 1 END) AS novemberServiceCount,
            COUNT(CASE WHEN period = '202411' AND bankPaymentCreateTime IS NULL THEN 1 END) AS novemberUnrecordedBankPayments,
            COUNT(CASE WHEN period = '202411' AND inAccountCreateTime IS NULL THEN 1 END) AS novemberUnaccountedPeriods,
            COUNT(CASE WHEN period = '202411' AND endTime IS NULL THEN 1 END) AS novemberUnclosedPeriods,
            COUNT(CASE WHEN period = '202412' THEN 1 END) AS decemberServiceCount,
            COUNT(CASE WHEN period = '202412' AND bankPaymentCreateTime IS NULL THEN 1 END) AS decemberUnrecordedBankPayments,
            COUNT(CASE WHEN period = '202412' AND inAccountCreateTime IS NULL THEN 1 END) AS decemberUnaccountedPeriods,
            COUNT(CASE WHEN period = '202412' AND endTime IS NULL THEN 1 END) AS decemberUnclosedPeriods
        FROM detail_data
        GROUP BY deptName
        ORDER BY deptName
    </select>
    <select id="exportAccountingNotComplete0shenbao" resultType="com.bxm.openapi.domain.ExportDTO">
        WITH aggregated_ccspe AS (
            SELECT
                period_id,
                GROUP_CONCAT(employee_name SEPARATOR ', ') AS accountant_names
            FROM c_customer_service_period_employee
            WHERE period_employee_type = 2
            GROUP BY period_id
        ),
             aggregated_cbtr AS (
                 SELECT
                     business_id,
                     GROUP_CONCAT(tag_id SEPARATOR ', ') AS tag_ids
                 FROM c_business_tag_relation
                 WHERE business_type = 2 AND tag_id IN (1, 13)
                 GROUP BY business_id
             ),
             detail_data AS (
                 SELECT DISTINCT
                     ccspm.customer_name AS customer_name,
                     ccspm.credit_code AS credit_code,
                     sd.dept_name AS deptName,
                     aggregated_ccspe.accountant_names AS accountant_names,
                     ccspm.period AS period,
                     ccsia.bank_payment_input_time AS bankPaymentCreateTime,
                     ccsia.in_time AS inAccountCreateTime,
                     ccsia.end_time AS endTime,
                     aggregated_cbtr.tag_ids AS tag_ids
                 FROM c_customer_service_period_month ccspm
                          LEFT JOIN c_customer_service_in_account ccsia ON ccspm.id = ccsia.customer_service_period_month_id AND ccsia.is_del = 0
                          LEFT JOIN aggregated_ccspe ON aggregated_ccspe.period_id = ccspm.id
                          LEFT JOIN sys_dept sd ON ccspm.business_dept_id = sd.dept_id
                          LEFT JOIN aggregated_cbtr ON aggregated_cbtr.business_id = ccspm.id
                 WHERE aggregated_cbtr.tag_ids IS NOT NULL
             )

        SELECT
            deptName AS deptName,
            COUNT(CASE WHEN LEFT(period, 4) = '2024' THEN 1 END) AS fullYearServicePeriods,
            COUNT(CASE WHEN LEFT(period, 4) = '2024' AND bankPaymentCreateTime IS NULL THEN 1 END) AS fullYearUnrecordedBankPayments,
            COUNT(CASE WHEN LEFT(period, 4) = '2024' AND inAccountCreateTime IS NULL THEN 1 END) AS fullYearUnaccountedPeriods,
            COUNT(CASE WHEN LEFT(period, 4) = '2024' AND endTime IS NULL THEN 1 END) AS fullYearUnclosedPeriods,
            COUNT(CASE WHEN period = '202401' THEN 1 END) AS januaryServiceCount,
            COUNT(CASE WHEN period = '202401' AND bankPaymentCreateTime IS NULL THEN 1 END) AS januaryUnrecordedBankPayments,
            COUNT(CASE WHEN period = '202401' AND inAccountCreateTime IS NULL THEN 1 END) AS januaryUnaccountedPeriods,
            COUNT(CASE WHEN period = '202401' AND endTime IS NULL THEN 1 END) AS januaryUnclosedPeriods,
            COUNT(CASE WHEN period = '202402' THEN 1 END) AS februaryServiceCount,
            COUNT(CASE WHEN period = '202402' AND bankPaymentCreateTime IS NULL THEN 1 END) AS februaryUnrecordedBankPayments,
            COUNT(CASE WHEN period = '202402' AND inAccountCreateTime IS NULL THEN 1 END) AS februaryUnaccountedPeriods,
            COUNT(CASE WHEN period = '202402' AND endTime IS NULL THEN 1 END) AS februaryUnclosedPeriods,
            COUNT(CASE WHEN period = '202403' THEN 1 END) AS marchServiceCount,
            COUNT(CASE WHEN period = '202403' AND bankPaymentCreateTime IS NULL THEN 1 END) AS marchUnrecordedBankPayments,
            COUNT(CASE WHEN period = '202403' AND inAccountCreateTime IS NULL THEN 1 END) AS marchUnaccountedPeriods,
            COUNT(CASE WHEN period = '202403' AND endTime IS NULL THEN 1 END) AS marchUnclosedPeriods,
            COUNT(CASE WHEN period = '202404' THEN 1 END) AS aprilServiceCount,
            COUNT(CASE WHEN period = '202404' AND bankPaymentCreateTime IS NULL THEN 1 END) AS aprilUnrecordedBankPayments,
            COUNT(CASE WHEN period = '202404' AND inAccountCreateTime IS NULL THEN 1 END) AS aprilUnaccountedPeriods,
            COUNT(CASE WHEN period = '202404' AND endTime IS NULL THEN 1 END) AS aprilUnclosedPeriods,
            COUNT(CASE WHEN period = '202405' THEN 1 END) AS mayServiceCount,
            COUNT(CASE WHEN period = '202405' AND bankPaymentCreateTime IS NULL THEN 1 END) AS mayUnrecordedBankPayments,
            COUNT(CASE WHEN period = '202405' AND inAccountCreateTime IS NULL THEN 1 END) AS mayUnaccountedPeriods,
            COUNT(CASE WHEN period = '202405' AND endTime IS NULL THEN 1 END) AS mayUnclosedPeriods,
            COUNT(CASE WHEN period = '202406' THEN 1 END) AS juneServiceCount,
            COUNT(CASE WHEN period = '202406' AND bankPaymentCreateTime IS NULL THEN 1 END) AS juneUnrecordedBankPayments,
            COUNT(CASE WHEN period = '202406' AND inAccountCreateTime IS NULL THEN 1 END) AS juneUnaccountedPeriods,
            COUNT(CASE WHEN period = '202406' AND endTime IS NULL THEN 1 END) AS juneUnclosedPeriods,
            COUNT(CASE WHEN period = '202407' THEN 1 END) AS julyServiceCount,
            COUNT(CASE WHEN period = '202407' AND bankPaymentCreateTime IS NULL THEN 1 END) AS julyUnrecordedBankPayments,
            COUNT(CASE WHEN period = '202407' AND inAccountCreateTime IS NULL THEN 1 END) AS julyUnaccountedPeriods,
            COUNT(CASE WHEN period = '202407' AND endTime IS NULL THEN 1 END) AS julyUnclosedPeriods,
            COUNT(CASE WHEN period = '202408' THEN 1 END) AS augustServiceCount,
            COUNT(CASE WHEN period = '202408' AND bankPaymentCreateTime IS NULL THEN 1 END) AS augustUnrecordedBankPayments,
            COUNT(CASE WHEN period = '202408' AND inAccountCreateTime IS NULL THEN 1 END) AS augustUnaccountedPeriods,
            COUNT(CASE WHEN period = '202408' AND endTime IS NULL THEN 1 END) AS augustUnclosedPeriods,
            COUNT(CASE WHEN period = '202409' THEN 1 END) AS septemberServiceCount,
            COUNT(CASE WHEN period = '202409' AND bankPaymentCreateTime IS NULL THEN 1 END) AS septemberUnrecordedBankPayments,
            COUNT(CASE WHEN period = '202409' AND inAccountCreateTime IS NULL THEN 1 END) AS septemberUnaccountedPeriods,
            COUNT(CASE WHEN period = '202409' AND endTime IS NULL THEN 1 END) AS septemberUnclosedPeriods,
            COUNT(CASE WHEN period = '202410' THEN 1 END) AS octoberServiceCount,
            COUNT(CASE WHEN period = '202410' AND bankPaymentCreateTime IS NULL THEN 1 END) AS octoberUnrecordedBankPayments,
            COUNT(CASE WHEN period = '202410' AND inAccountCreateTime IS NULL THEN 1 END) AS octoberUnaccountedPeriods,
            COUNT(CASE WHEN period = '202410' AND endTime IS NULL THEN 1 END) AS octoberUnclosedPeriods,
            COUNT(CASE WHEN period = '202411' THEN 1 END) AS novemberServiceCount,
            COUNT(CASE WHEN period = '202411' AND bankPaymentCreateTime IS NULL THEN 1 END) AS novemberUnrecordedBankPayments,
            COUNT(CASE WHEN period = '202411' AND inAccountCreateTime IS NULL THEN 1 END) AS novemberUnaccountedPeriods,
            COUNT(CASE WHEN period = '202411' AND endTime IS NULL THEN 1 END) AS novemberUnclosedPeriods,
            COUNT(CASE WHEN period = '202412' THEN 1 END) AS decemberServiceCount,
            COUNT(CASE WHEN period = '202412' AND bankPaymentCreateTime IS NULL THEN 1 END) AS decemberUnrecordedBankPayments,
            COUNT(CASE WHEN period = '202412' AND inAccountCreateTime IS NULL THEN 1 END) AS decemberUnaccountedPeriods,
            COUNT(CASE WHEN period = '202412' AND endTime IS NULL THEN 1 END) AS decemberUnclosedPeriods

        FROM detail_data
        GROUP BY deptName
        order by deptName
    </select>
    <select id="exportAccountingNotCompletexiaoguimo" resultType="com.bxm.openapi.domain.ExportDTO">
        WITH aggregated_ccspe AS (
            SELECT
                period_id,
                GROUP_CONCAT(employee_name SEPARATOR ', ') AS accountant_names
            FROM c_customer_service_period_employee
            WHERE period_employee_type = 2
            GROUP BY period_id
        ),
             aggregated_cbtr AS (
                 SELECT
                     business_id,
                     GROUP_CONCAT(tag_id SEPARATOR ', ') AS tag_ids
                 FROM c_business_tag_relation
                 WHERE business_type = 2 AND tag_id IN (1, 13)
                 GROUP BY business_id
             ),
             detail_data AS (
                 SELECT DISTINCT
                     ccspm.customer_name AS customer_name,
                     ccspm.credit_code AS credit_code,
                     sd.dept_name AS deptName,
                     aggregated_ccspe.accountant_names AS accountant_names,
                     ccspm.period AS period,
                     ccsia.bank_payment_input_time AS bankPaymentCreateTime,
                     ccsia.in_time AS inAccountCreateTime,
                     ccsia.end_time AS endTime,
                     aggregated_cbtr.tag_ids AS tag_ids
                 FROM c_customer_service_period_month ccspm
                          LEFT JOIN c_customer_service_in_account ccsia ON ccspm.id = ccsia.customer_service_period_month_id AND ccsia.is_del = 0
                          LEFT JOIN aggregated_ccspe ON aggregated_ccspe.period_id = ccspm.id
                          LEFT JOIN sys_dept sd ON ccspm.business_dept_id = sd.dept_id
                          LEFT JOIN aggregated_cbtr ON aggregated_cbtr.business_id = ccspm.id
                 WHERE aggregated_cbtr.tag_ids IS null and ccspm.tax_type = 1
             )

        SELECT
            deptName AS deptName,
            COUNT(CASE WHEN LEFT(period, 4) = '2024' THEN 1 END) AS fullYearServicePeriods,
            COUNT(CASE WHEN LEFT(period, 4) = '2024' AND bankPaymentCreateTime IS NULL THEN 1 END) AS fullYearUnrecordedBankPayments,
            COUNT(CASE WHEN LEFT(period, 4) = '2024' AND inAccountCreateTime IS NULL THEN 1 END) AS fullYearUnaccountedPeriods,
            COUNT(CASE WHEN LEFT(period, 4) = '2024' AND endTime IS NULL THEN 1 END) AS fullYearUnclosedPeriods,
            COUNT(CASE WHEN period = '202401' THEN 1 END) AS januaryServiceCount,
            COUNT(CASE WHEN period = '202401' AND bankPaymentCreateTime IS NULL THEN 1 END) AS januaryUnrecordedBankPayments,
            COUNT(CASE WHEN period = '202401' AND inAccountCreateTime IS NULL THEN 1 END) AS januaryUnaccountedPeriods,
            COUNT(CASE WHEN period = '202401' AND endTime IS NULL THEN 1 END) AS januaryUnclosedPeriods,
            COUNT(CASE WHEN period = '202402' THEN 1 END) AS februaryServiceCount,
            COUNT(CASE WHEN period = '202402' AND bankPaymentCreateTime IS NULL THEN 1 END) AS februaryUnrecordedBankPayments,
            COUNT(CASE WHEN period = '202402' AND inAccountCreateTime IS NULL THEN 1 END) AS februaryUnaccountedPeriods,
            COUNT(CASE WHEN period = '202402' AND endTime IS NULL THEN 1 END) AS februaryUnclosedPeriods,
            COUNT(CASE WHEN period = '202403' THEN 1 END) AS marchServiceCount,
            COUNT(CASE WHEN period = '202403' AND bankPaymentCreateTime IS NULL THEN 1 END) AS marchUnrecordedBankPayments,
            COUNT(CASE WHEN period = '202403' AND inAccountCreateTime IS NULL THEN 1 END) AS marchUnaccountedPeriods,
            COUNT(CASE WHEN period = '202403' AND endTime IS NULL THEN 1 END) AS marchUnclosedPeriods,
            COUNT(CASE WHEN period = '202404' THEN 1 END) AS aprilServiceCount,
            COUNT(CASE WHEN period = '202404' AND bankPaymentCreateTime IS NULL THEN 1 END) AS aprilUnrecordedBankPayments,
            COUNT(CASE WHEN period = '202404' AND inAccountCreateTime IS NULL THEN 1 END) AS aprilUnaccountedPeriods,
            COUNT(CASE WHEN period = '202404' AND endTime IS NULL THEN 1 END) AS aprilUnclosedPeriods,
            COUNT(CASE WHEN period = '202405' THEN 1 END) AS mayServiceCount,
            COUNT(CASE WHEN period = '202405' AND bankPaymentCreateTime IS NULL THEN 1 END) AS mayUnrecordedBankPayments,
            COUNT(CASE WHEN period = '202405' AND inAccountCreateTime IS NULL THEN 1 END) AS mayUnaccountedPeriods,
            COUNT(CASE WHEN period = '202405' AND endTime IS NULL THEN 1 END) AS mayUnclosedPeriods,
            COUNT(CASE WHEN period = '202406' THEN 1 END) AS juneServiceCount,
            COUNT(CASE WHEN period = '202406' AND bankPaymentCreateTime IS NULL THEN 1 END) AS juneUnrecordedBankPayments,
            COUNT(CASE WHEN period = '202406' AND inAccountCreateTime IS NULL THEN 1 END) AS juneUnaccountedPeriods,
            COUNT(CASE WHEN period = '202406' AND endTime IS NULL THEN 1 END) AS juneUnclosedPeriods,
            COUNT(CASE WHEN period = '202407' THEN 1 END) AS julyServiceCount,
            COUNT(CASE WHEN period = '202407' AND bankPaymentCreateTime IS NULL THEN 1 END) AS julyUnrecordedBankPayments,
            COUNT(CASE WHEN period = '202407' AND inAccountCreateTime IS NULL THEN 1 END) AS julyUnaccountedPeriods,
            COUNT(CASE WHEN period = '202407' AND endTime IS NULL THEN 1 END) AS julyUnclosedPeriods,
            COUNT(CASE WHEN period = '202408' THEN 1 END) AS augustServiceCount,
            COUNT(CASE WHEN period = '202408' AND bankPaymentCreateTime IS NULL THEN 1 END) AS augustUnrecordedBankPayments,
            COUNT(CASE WHEN period = '202408' AND inAccountCreateTime IS NULL THEN 1 END) AS augustUnaccountedPeriods,
            COUNT(CASE WHEN period = '202408' AND endTime IS NULL THEN 1 END) AS augustUnclosedPeriods,
            COUNT(CASE WHEN period = '202409' THEN 1 END) AS septemberServiceCount,
            COUNT(CASE WHEN period = '202409' AND bankPaymentCreateTime IS NULL THEN 1 END) AS septemberUnrecordedBankPayments,
            COUNT(CASE WHEN period = '202409' AND inAccountCreateTime IS NULL THEN 1 END) AS septemberUnaccountedPeriods,
            COUNT(CASE WHEN period = '202409' AND endTime IS NULL THEN 1 END) AS septemberUnclosedPeriods,
            COUNT(CASE WHEN period = '202410' THEN 1 END) AS octoberServiceCount,
            COUNT(CASE WHEN period = '202410' AND bankPaymentCreateTime IS NULL THEN 1 END) AS octoberUnrecordedBankPayments,
            COUNT(CASE WHEN period = '202410' AND inAccountCreateTime IS NULL THEN 1 END) AS octoberUnaccountedPeriods,
            COUNT(CASE WHEN period = '202410' AND endTime IS NULL THEN 1 END) AS octoberUnclosedPeriods,
            COUNT(CASE WHEN period = '202411' THEN 1 END) AS novemberServiceCount,
            COUNT(CASE WHEN period = '202411' AND bankPaymentCreateTime IS NULL THEN 1 END) AS novemberUnrecordedBankPayments,
            COUNT(CASE WHEN period = '202411' AND inAccountCreateTime IS NULL THEN 1 END) AS novemberUnaccountedPeriods,
            COUNT(CASE WHEN period = '202411' AND endTime IS NULL THEN 1 END) AS novemberUnclosedPeriods,
            COUNT(CASE WHEN period = '202412' THEN 1 END) AS decemberServiceCount,
            COUNT(CASE WHEN period = '202412' AND bankPaymentCreateTime IS NULL THEN 1 END) AS decemberUnrecordedBankPayments,
            COUNT(CASE WHEN period = '202412' AND inAccountCreateTime IS NULL THEN 1 END) AS decemberUnaccountedPeriods,
            COUNT(CASE WHEN period = '202412' AND endTime IS NULL THEN 1 END) AS decemberUnclosedPeriods

        FROM detail_data
        GROUP BY deptName
        order by deptName
    </select>
    <select id="exportAccountingNotCompleteyiban" resultType="com.bxm.openapi.domain.ExportDTO">
        WITH aggregated_ccspe AS (
            SELECT
                period_id,
                GROUP_CONCAT(employee_name SEPARATOR ', ') AS accountant_names
            FROM c_customer_service_period_employee
            WHERE period_employee_type = 2
            GROUP BY period_id
        ),
             aggregated_cbtr AS (
                 SELECT
                     business_id,
                     GROUP_CONCAT(tag_id SEPARATOR ', ') AS tag_ids
                 FROM c_business_tag_relation
                 WHERE business_type = 2 AND tag_id IN (1, 13)
                 GROUP BY business_id
             ),
             detail_data AS (
                 SELECT DISTINCT
                     ccspm.customer_name AS customer_name,
                     ccspm.credit_code AS credit_code,
                     sd.dept_name AS deptName,
                     aggregated_ccspe.accountant_names AS accountant_names,
                     ccspm.period AS period,
                     ccsia.bank_payment_input_time AS bankPaymentCreateTime,
                     ccsia.in_time AS inAccountCreateTime,
                     ccsia.end_time AS endTime,
                     aggregated_cbtr.tag_ids AS tag_ids
                 FROM c_customer_service_period_month ccspm
                          LEFT JOIN c_customer_service_in_account ccsia ON ccspm.id = ccsia.customer_service_period_month_id AND ccsia.is_del = 0
                          LEFT JOIN aggregated_ccspe ON aggregated_ccspe.period_id = ccspm.id
                          LEFT JOIN sys_dept sd ON ccspm.business_dept_id = sd.dept_id
                          LEFT JOIN aggregated_cbtr ON aggregated_cbtr.business_id = ccspm.id
                 WHERE aggregated_cbtr.tag_ids IS null and ccspm.tax_type = 2
             )

        SELECT
            deptName AS deptName,
            COUNT(CASE WHEN LEFT(period, 4) = '2024' THEN 1 END) AS fullYearServicePeriods,
            COUNT(CASE WHEN LEFT(period, 4) = '2024' AND bankPaymentCreateTime IS NULL THEN 1 END) AS fullYearUnrecordedBankPayments,
            COUNT(CASE WHEN LEFT(period, 4) = '2024' AND inAccountCreateTime IS NULL THEN 1 END) AS fullYearUnaccountedPeriods,
            COUNT(CASE WHEN LEFT(period, 4) = '2024' AND endTime IS NULL THEN 1 END) AS fullYearUnclosedPeriods,
            COUNT(CASE WHEN period = '202401' THEN 1 END) AS januaryServiceCount,
            COUNT(CASE WHEN period = '202401' AND bankPaymentCreateTime IS NULL THEN 1 END) AS januaryUnrecordedBankPayments,
            COUNT(CASE WHEN period = '202401' AND inAccountCreateTime IS NULL THEN 1 END) AS januaryUnaccountedPeriods,
            COUNT(CASE WHEN period = '202401' AND endTime IS NULL THEN 1 END) AS januaryUnclosedPeriods,
            COUNT(CASE WHEN period = '202402' THEN 1 END) AS februaryServiceCount,
            COUNT(CASE WHEN period = '202402' AND bankPaymentCreateTime IS NULL THEN 1 END) AS februaryUnrecordedBankPayments,
            COUNT(CASE WHEN period = '202402' AND inAccountCreateTime IS NULL THEN 1 END) AS februaryUnaccountedPeriods,
            COUNT(CASE WHEN period = '202402' AND endTime IS NULL THEN 1 END) AS februaryUnclosedPeriods,
            COUNT(CASE WHEN period = '202403' THEN 1 END) AS marchServiceCount,
            COUNT(CASE WHEN period = '202403' AND bankPaymentCreateTime IS NULL THEN 1 END) AS marchUnrecordedBankPayments,
            COUNT(CASE WHEN period = '202403' AND inAccountCreateTime IS NULL THEN 1 END) AS marchUnaccountedPeriods,
            COUNT(CASE WHEN period = '202403' AND endTime IS NULL THEN 1 END) AS marchUnclosedPeriods,
            COUNT(CASE WHEN period = '202404' THEN 1 END) AS aprilServiceCount,
            COUNT(CASE WHEN period = '202404' AND bankPaymentCreateTime IS NULL THEN 1 END) AS aprilUnrecordedBankPayments,
            COUNT(CASE WHEN period = '202404' AND inAccountCreateTime IS NULL THEN 1 END) AS aprilUnaccountedPeriods,
            COUNT(CASE WHEN period = '202404' AND endTime IS NULL THEN 1 END) AS aprilUnclosedPeriods,
            COUNT(CASE WHEN period = '202405' THEN 1 END) AS mayServiceCount,
            COUNT(CASE WHEN period = '202405' AND bankPaymentCreateTime IS NULL THEN 1 END) AS mayUnrecordedBankPayments,
            COUNT(CASE WHEN period = '202405' AND inAccountCreateTime IS NULL THEN 1 END) AS mayUnaccountedPeriods,
            COUNT(CASE WHEN period = '202405' AND endTime IS NULL THEN 1 END) AS mayUnclosedPeriods,
            COUNT(CASE WHEN period = '202406' THEN 1 END) AS juneServiceCount,
            COUNT(CASE WHEN period = '202406' AND bankPaymentCreateTime IS NULL THEN 1 END) AS juneUnrecordedBankPayments,
            COUNT(CASE WHEN period = '202406' AND inAccountCreateTime IS NULL THEN 1 END) AS juneUnaccountedPeriods,
            COUNT(CASE WHEN period = '202406' AND endTime IS NULL THEN 1 END) AS juneUnclosedPeriods,
            COUNT(CASE WHEN period = '202407' THEN 1 END) AS julyServiceCount,
            COUNT(CASE WHEN period = '202407' AND bankPaymentCreateTime IS NULL THEN 1 END) AS julyUnrecordedBankPayments,
            COUNT(CASE WHEN period = '202407' AND inAccountCreateTime IS NULL THEN 1 END) AS julyUnaccountedPeriods,
            COUNT(CASE WHEN period = '202407' AND endTime IS NULL THEN 1 END) AS julyUnclosedPeriods,
            COUNT(CASE WHEN period = '202408' THEN 1 END) AS augustServiceCount,
            COUNT(CASE WHEN period = '202408' AND bankPaymentCreateTime IS NULL THEN 1 END) AS augustUnrecordedBankPayments,
            COUNT(CASE WHEN period = '202408' AND inAccountCreateTime IS NULL THEN 1 END) AS augustUnaccountedPeriods,
            COUNT(CASE WHEN period = '202408' AND endTime IS NULL THEN 1 END) AS augustUnclosedPeriods,
            COUNT(CASE WHEN period = '202409' THEN 1 END) AS septemberServiceCount,
            COUNT(CASE WHEN period = '202409' AND bankPaymentCreateTime IS NULL THEN 1 END) AS septemberUnrecordedBankPayments,
            COUNT(CASE WHEN period = '202409' AND inAccountCreateTime IS NULL THEN 1 END) AS septemberUnaccountedPeriods,
            COUNT(CASE WHEN period = '202409' AND endTime IS NULL THEN 1 END) AS septemberUnclosedPeriods,
            COUNT(CASE WHEN period = '202410' THEN 1 END) AS octoberServiceCount,
            COUNT(CASE WHEN period = '202410' AND bankPaymentCreateTime IS NULL THEN 1 END) AS octoberUnrecordedBankPayments,
            COUNT(CASE WHEN period = '202410' AND inAccountCreateTime IS NULL THEN 1 END) AS octoberUnaccountedPeriods,
            COUNT(CASE WHEN period = '202410' AND endTime IS NULL THEN 1 END) AS octoberUnclosedPeriods,
            COUNT(CASE WHEN period = '202411' THEN 1 END) AS novemberServiceCount,
            COUNT(CASE WHEN period = '202411' AND bankPaymentCreateTime IS NULL THEN 1 END) AS novemberUnrecordedBankPayments,
            COUNT(CASE WHEN period = '202411' AND inAccountCreateTime IS NULL THEN 1 END) AS novemberUnaccountedPeriods,
            COUNT(CASE WHEN period = '202411' AND endTime IS NULL THEN 1 END) AS novemberUnclosedPeriods,
            COUNT(CASE WHEN period = '202412' THEN 1 END) AS decemberServiceCount,
            COUNT(CASE WHEN period = '202412' AND bankPaymentCreateTime IS NULL THEN 1 END) AS decemberUnrecordedBankPayments,
            COUNT(CASE WHEN period = '202412' AND inAccountCreateTime IS NULL THEN 1 END) AS decemberUnaccountedPeriods,
            COUNT(CASE WHEN period = '202412' AND endTime IS NULL THEN 1 END) AS decemberUnclosedPeriods

        FROM detail_data
        GROUP BY deptName
        order by deptName
    </select>
</mapper>