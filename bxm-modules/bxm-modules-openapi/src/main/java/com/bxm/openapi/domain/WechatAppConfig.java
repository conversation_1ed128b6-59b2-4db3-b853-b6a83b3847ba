package com.bxm.openapi.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bxm.common.core.annotation.Excel;
import com.bxm.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 企微信息对象 system_wechat_app_config
 * 
 * <AUTHOR>
 * @date 2024-06-23
 */
@Data
@ApiModel("企微信息对象")
@Accessors(chain = true)
@TableName("sys_wechat_app_config")
public class WechatAppConfig extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键id */
    @TableId(value = "id", type = IdType.AUTO)
    @Excel(name = "主键id")
    @ApiModelProperty(value = "主键id")
    private Long id;

    /** 企业微信corpId */
    @Excel(name = "企业微信corpId")
    @TableField("corp_id")
    @ApiModelProperty(value = "企业微信corpId")
    private String corpId;

    /** 企业微信secret */
    @Excel(name = "企业微信secret")
    @TableField("corp_secret")
    @ApiModelProperty(value = "企业微信secret")
    private String corpSecret;

    /** 应用id */
    @Excel(name = "应用id")
    @TableField("agent_id")
    @ApiModelProperty(value = "应用id")
    private String agentId;

}
