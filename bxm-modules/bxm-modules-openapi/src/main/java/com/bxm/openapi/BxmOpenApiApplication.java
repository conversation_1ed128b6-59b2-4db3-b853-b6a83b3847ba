package com.bxm.openapi;

import com.bxm.common.security.annotation.EnableCustomConfig;
import com.bxm.common.security.annotation.EnableJsFeignClients;
import com.bxm.common.swagger.annotation.EnableCustomSwagger2;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableAsync;

/**
 * 开放接口模块
 * 
 * <AUTHOR>
 */
@EnableCustomConfig
@EnableJsFeignClients
@EnableCustomSwagger2
@SpringBootApplication
@EnableAsync
public class BxmOpenApiApplication
{
    public static void main(String[] args)
    {
        SpringApplication.run(BxmOpenApiApplication.class, args);
        System.out.println("(♥◠‿◠)ﾉﾞ  开放接口模块启动成功   ");
    }
}
