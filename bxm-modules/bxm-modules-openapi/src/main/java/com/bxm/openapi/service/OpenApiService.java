package com.bxm.openapi.service;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.bxm.common.core.enums.OpenApiAppRelations;
import com.bxm.common.core.exception.ServiceException;
import com.bxm.common.core.utils.DateUtils;
import com.bxm.common.core.utils.StringUtils;
import com.bxm.common.rocketmq.service.ExtRocketMQTemplate;
import com.bxm.openapi.domain.ExportDTO;
import com.bxm.openapi.domain.OpenApiNoticeRecord;
import com.bxm.openapi.domain.XqyReportVO;
import com.bxm.openapi.mapper.SysClientVersionMapper;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.SendResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessagingException;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Service
@Slf4j
public class OpenApiService {

    @Autowired
    private ExtRocketMQTemplate extRocketMQTemplate;

    @Autowired
    private SysClientVersionMapper sysClientVersionMapper;

    @Value("${spring.profiles.active}")
    private String environment;

    @Autowired
    private IOpenApiNoticeRecordService openApiNoticeRecordService;

    public void report(Map<String, Object> params, String appId) {
        openApiNoticeRecordService.saveRecord(new OpenApiNoticeRecord().setNoticeSource("鑫启易").setNoticeTarget("慧进账")
                .setNoticeFunction("openapiReport_" + environment).setNoticeContent(JSONObject.toJSONString(params)).setIsDel(false));
        Message<String> msg = MessageBuilder.withPayload(JSONObject.toJSONString(params))
                        .setHeader("KEYS", params.get("taxNumber"))
                        .build();
        try {
            SendResult sendResult = extRocketMQTemplate.syncSend("openapiReport_" + environment + ":" + OpenApiAppRelations.getByAppId(appId).getTag(), msg);
            log.info("鑫启易申报：{},发送消息结果:{}", params, sendResult);
        } catch (MessagingException e) {
            log.error("鑫启易申报消息发送失败：{}", e.getFailedMessage());
            throw new ServiceException("消息发送失败");
        }
    }

    public void supplementFiles(Map<String, Object> params, String appId) {
        openApiNoticeRecordService.saveRecord(new OpenApiNoticeRecord().setNoticeSource("鑫启易").setNoticeTarget("慧进账")
                .setNoticeFunction("openapiSupplement_" + environment).setNoticeContent(JSONObject.toJSONString(params)).setIsDel(false));
        Message<String> msg = MessageBuilder.withPayload(JSONObject.toJSONString(params))
                .setHeader("KEYS", params.get("taxNumber"))
                .build();
        try {
            SendResult sendResult = extRocketMQTemplate.syncSend("openapiSupplement_" + environment + ":" + OpenApiAppRelations.getByAppId(appId).getTag(), msg);
            log.info("鑫启易补充：{},发送消息结果:{}", params, sendResult);
        } catch (MessagingException e) {
            log.error("鑫启易补充消息发送失败：{}", e.getFailedMessage());
            throw new ServiceException("消息发送失败");
        }
    }

    public void commonReport(Map<String, Object> params, String appId) {
        try {
            OpenApiAppRelations relation = OpenApiAppRelations.getByAppId(appId);
            params.put("sourceName", relation.getName());
            params.put("source", relation.getId());
            openApiNoticeRecordService.saveRecord(new OpenApiNoticeRecord().setNoticeSource(relation.getName()).setNoticeTarget("慧进账")
                    .setNoticeFunction("openapiCommonReport_" + environment).setNoticeContent(JSONObject.toJSONString(params)).setIsDel(false));

            Message<String> msg = MessageBuilder.withPayload(JSONObject.toJSONString(params))
                    .setHeader("KEYS", params.get("taxNumber"))
                    .build();
            SendResult sendResult = extRocketMQTemplate.syncSend("openapiCommonReport_" + environment + ":" + relation.getTag(), msg);
            log.info("公共申报：{},发送消息结果:{}", params, sendResult);
        } catch (MessagingException e) {
            log.error("公共申报消息发送失败：{}", e.getFailedMessage());
            throw new ServiceException("消息发送失败");
        }
    }

    public void commonSupplementFiles(Map<String, Object> params, String appId) {
        try {
            OpenApiAppRelations relation = OpenApiAppRelations.getByAppId(appId);
            params.put("sourceName", relation.getName());
            params.put("source", relation.getId());
            openApiNoticeRecordService.saveRecord(new OpenApiNoticeRecord().setNoticeSource(relation.getName()).setNoticeTarget("慧进账")
                    .setNoticeFunction("openapiCommonSupplement_" + environment).setNoticeContent(JSONObject.toJSONString(params)).setIsDel(false));

            Message<String> msg = MessageBuilder.withPayload(JSONObject.toJSONString(params))
                    .setHeader("KEYS", params.get("taxNumber"))
                    .build();
            SendResult sendResult = extRocketMQTemplate.syncSend("openapiCommonSupplement_" + environment + ":" + relation.getTag(), msg);
            log.info("公共补充：{},发送消息结果:{}", params, sendResult);
        } catch (MessagingException e) {
            log.error("公共补充消息发送失败：{}", e.getFailedMessage());
            throw new ServiceException("消息发送失败");
        }
    }

    public void commonDeduction(Map<String, Object> params, String appId) {
        try {
            OpenApiAppRelations relation = OpenApiAppRelations.getByAppId(appId);
            params.put("sourceName", relation.getName());
            params.put("source", relation.getId());
            openApiNoticeRecordService.saveRecord(new OpenApiNoticeRecord().setNoticeSource(relation.getName()).setNoticeTarget("慧进账")
                    .setNoticeFunction("openapiCommonDeduction_" + environment).setNoticeContent(JSONObject.toJSONString(params)).setIsDel(false));

            Message<String> msg = MessageBuilder.withPayload(JSONObject.toJSONString(params))
                    .setHeader("KEYS", params.get("taxNumber"))
                    .build();
            SendResult sendResult = extRocketMQTemplate.syncSend("openapiCommonDeduction_" + environment + ":" + relation.getTag(), msg);
            log.info("公共扣款：{},发送消息结果:{}", params, sendResult);
        } catch (MessagingException e) {
            log.error("公共扣款消息发送失败：{}", e.getFailedMessage());
            throw new ServiceException("消息发送失败");
        }
    }

    public Map<String, Object> xqyNotice(Map<String, Object> params, String appid) {
        openApiNoticeRecordService.saveRecord(new OpenApiNoticeRecord().setNoticeSource("鑫启易").setNoticeTarget("慧进账")
                .setNoticeFunction("openapiReport_" + environment).setNoticeContent(JSONObject.toJSONString(params)).setIsDel(false));
        Map<String, Object> result = new HashMap<>();
        Object noticeCodeObj = params.get("noticeCode");
        Object operatorObj = params.get("operator");
        if (Objects.isNull(noticeCodeObj)) {
            log.error("noticeCode为空");
            result.put("Success", false);
            result.put("Msg", "noticeCode为空");
            result.put("Value", null);
            return result;
        }
        Object noticeParameterObj = params.get("noticeParameter");
        if (Objects.isNull(noticeParameterObj)) {
            log.error("noticeParameter为空");
            result.put("Success", false);
            result.put("Msg", "noticeParameter为空");
            result.put("Value", null);
            return result;
        }
        String noticeCode = noticeCodeObj.toString();
        // 解析 JSON 为 Map<String, Object>
        Map<String, Object> tempMap = (Map<String, Object>) noticeParameterObj;

        // 将所有值转换为字符串类型
        Map<String, String> noticeParamsMap = new HashMap<>();
        for (Map.Entry<String, Object> entry : tempMap.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();
            if (value == null) {
                noticeParamsMap.put(key, null);
            } else {
                noticeParamsMap.put(key, value.toString());
            }
        }
        String taxNumber = noticeParamsMap.get("taxNumber");
        if (StringUtils.isBlank(taxNumber)) {
            log.error("taxNumber为空");
            result.put("Success", false);
            result.put("Msg", "taxNumber为空");
            result.put("Value", null);
            return result;
        }
        Message<String> msg;
        XqyReportVO xqyReportVO;
        switch (noticeCode) {
            case "TaxItemConfirmDownload":
                // 税种认定下载 调用原接口 不处理
            case "CompletionStatusOfTaxDeclarationDownload":
                // 报税完成情况下载 调用原接口 不处理
            case "SocialSecurityStatementDownload":
                // 社保对账单下载 无处理逻辑 不处理
                break;
            case "InvoiceDownload":
                // 发票下载
                String endMonth = noticeParamsMap.get("endMonth");
                String startMonth = noticeParamsMap.get("startMonth");
                if (StringUtils.isBlank(endMonth)) {
                    log.error("endMonth为空");
                    result.put("Success", false);
                    result.put("Msg", "endMonth为空");
                    result.put("Value", null);
                    return result;
                }
                if (StringUtils.isBlank(startMonth)) {
                    log.error("startMonth为空");
                    result.put("Success", false);
                    result.put("Msg", "startMonth为空");
                    result.put("Value", null);
                    return result;
                }
                LocalDate startDate = LocalDate.parse(startMonth + "-01", DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                LocalDate endDate = LocalDate.parse(endMonth + "-01", DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                xqyReportVO = XqyReportVO.builder()
                        .taxNumber(taxNumber)
                        .operateName(Objects.isNull(operatorObj) ? "" : operatorObj.toString())
                        .operateType(5)
                        .batchNo("")
                        .startDate(startDate.withDayOfMonth(1).toString())
                        .endDate(endDate.withDayOfMonth(endDate.lengthOfMonth()).toString())
                        .build();
                msg = MessageBuilder.withPayload(JSONObject.toJSONString(xqyReportVO))
                        .setHeader("KEYS", taxNumber)
                        .build();
                try {
                    SendResult sendResult = extRocketMQTemplate.syncSend("openapiReport_" + environment + ":" + OpenApiAppRelations.getByAppId(appid).getTag(), msg);
                    log.info("鑫启易通知：{},发送消息结果:{}", params, sendResult);
                } catch (MessagingException e) {
                    log.error("鑫启易通知消息发送失败：{}", e.getFailedMessage());
                    result.put("Success", false);
                    result.put("Msg", "消息发送失败");
                    result.put("Value", null);
                    return result;
                }
                break;
            case "VATTelatedTaxNotification":
                String belongMonth = noticeParamsMap.get("belongMonth");
                String notificationText = noticeParamsMap.get("notificationText");
                String purchaseInvoiceScope = noticeParamsMap.get("purchaseInvoiceScope");
                if (StringUtils.isBlank(belongMonth)) {
                    log.error("belongMonth为空");
                    result.put("Success", false);
                    result.put("Msg", "belongMonth为空");
                    result.put("Value", null);
                    return result;
                }
//                if (StringUtils.isEmpty(notificationText)) {
//                    log.error("notificationText为空，不处理");
//                    break;
//                }
                xqyReportVO = XqyReportVO.builder()
                        .taxNumberList(Lists.newArrayList(taxNumber))
                        .reportPeriod(belongMonth)
                        .operateName(Objects.isNull(operatorObj) ? "" : operatorObj.toString())
                        .operateType(4)
                        .batchNo("")
                        .notificationText(notificationText)
                        .purchaseInvoiceScope(purchaseInvoiceScope)
                        .build();
                msg = MessageBuilder.withPayload(JSONObject.toJSONString(xqyReportVO))
                        .setHeader("KEYS", taxNumber)
                        .build();
                try {
                    SendResult sendResult = extRocketMQTemplate.syncSend("openapiReport_" + environment + ":" + OpenApiAppRelations.getByAppId(appid).getTag(), msg);
                    log.info("鑫启易通知：{},发送消息结果:{}", params, sendResult);
                } catch (MessagingException e) {
                    log.error("鑫启易通知消息发送失败：{}", e.getFailedMessage());
                    result.put("Success", false);
                    result.put("Msg", "消息发送失败");
                    result.put("Value", null);
                    return result;
                }
                break;
            case "CompletionStatusOfGongShangAnnualReport":
                String belongYear = noticeParamsMap.get("belongYear");
                String notificationText1 = noticeParamsMap.get("notificationText");
                if (StringUtils.isBlank(belongYear)) {
                    log.error("belongYear为空");
                    result.put("Success", false);
                    result.put("Msg", "belongYear为空");
                    result.put("Value", null);
                    return result;
                }
                String isCompleted = noticeParamsMap.get("isCompleted");
                xqyReportVO = XqyReportVO.builder()
                        .taxNumberList(Lists.newArrayList(taxNumber))
                        .belongYear(belongYear)
                        .operateName(Objects.isNull(operatorObj) ? "" : operatorObj.toString())
                        .operateType(6)
                        .batchNo("")
                        .notificationText(notificationText1)
                        .isCompleted(StringUtils.isEmpty(isCompleted) || Boolean.parseBoolean(isCompleted))
                        .build();
                msg = MessageBuilder.withPayload(JSONObject.toJSONString(xqyReportVO))
                        .setHeader("KEYS", taxNumber)
                        .build();
                try {
                    SendResult sendResult = extRocketMQTemplate.syncSend("openapiReport_" + environment + ":" + OpenApiAppRelations.getByAppId(appid).getTag(), msg);
                    log.info("鑫启易通知：{},发送消息结果:{}", params, sendResult);
                } catch (MessagingException e) {
                    log.error("鑫启易通知消息发送失败：{}", e.getFailedMessage());
                    result.put("Success", false);
                    result.put("Msg", "消息发送失败");
                    result.put("Value", null);
                    return result;
                }
                break;
            default:
                log.error("未知的noticeCode:{}", noticeCode);
                break;
        }
        result.put("Success", true);
        result.put("Msg", "上报成功");
        result.put("Value", null);
        return result;
    }

    public void commonNotice(Map<String, Object> params, String appId) {
        OpenApiAppRelations relation = OpenApiAppRelations.getByAppId(appId);
        params.put("sourceName", relation.getName());
        params.put("source", relation.getId());
        Object uuidObj = params.get("uuid");
        String topic = "openapiCommonNotice_" + environment;
        if (!Objects.isNull(uuidObj)) {
            openApiNoticeRecordService.saveOrUpdate(relation.getName(), topic, params, uuidObj.toString());
        } else {
            openApiNoticeRecordService.saveRecord(new OpenApiNoticeRecord().setNoticeSource(relation.getName()).setNoticeTarget("慧进账")
                    .setNoticeFunction(topic).setNoticeContent(JSONObject.toJSONString(params)).setIsDel(false));
        }
        Object noticeCodeObj = params.get("noticeCode");
        if (Objects.isNull(noticeCodeObj)) {
            log.error("noticeCode为空");
            throw new ServiceException("noticeCode为空");
        }
//        Object noticeParameterObj = params.get("noticeParameter");
//        if (Objects.isNull(noticeParameterObj)) {
//            log.error("noticeParameter为空");
//            throw new ServiceException("noticeParameter为空");
//        }
        try {
            Message<String> msg = MessageBuilder.withPayload(JSONObject.toJSONString(params))
                    .setHeader("KEYS", params.get("taxNumber"))
                    .build();
            SendResult sendResult = extRocketMQTemplate.syncSend("openapiCommonNotice_" + environment + ":" + relation.getTag(), msg);
            log.info("公共消息推送：{},发送消息结果:{}", params, sendResult);
        } catch (MessagingException e) {
            log.error("公共消息发送失败：{}", e.getFailedMessage());
            throw new ServiceException("公共消息发送失败");
        }
    }

    public List<ExportDTO> exportAccountingNotComplete(Integer type) {
        switch (type) {
            case 1:
                return sysClientVersionMapper.exportAccountingNotCompleteTotal();
            case 2:
                return sysClientVersionMapper.exportAccountingNotComplete0shenbao();
            case 3:
                return sysClientVersionMapper.exportAccountingNotCompletexiaoguimo();
            case 4:
                return sysClientVersionMapper.exportAccountingNotCompleteyiban();
            default:
                return Lists.newArrayList();
        }
    }

    public static void main(String[] args) {
        String belongYear = "2024";
        System.out.println((Integer.parseInt(belongYear) + 1) + "-01");
    }
}
