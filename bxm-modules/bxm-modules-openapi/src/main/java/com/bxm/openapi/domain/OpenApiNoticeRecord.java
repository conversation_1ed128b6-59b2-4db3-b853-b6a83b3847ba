package com.bxm.openapi.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bxm.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.bxm.common.core.web.domain.BaseEntity;
import lombok.experimental.Accessors;

/**
 * 第三方通知/被通知记录对象 c_open_api_notice_record
 * 
 * <AUTHOR>
 * @date 2025-02-16
 */
@Data
@ApiModel("第三方通知/被通知记录对象")
@Accessors(chain = true)
@TableName("c_open_api_notice_record")
public class OpenApiNoticeRecord extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    @TableId(value = "id", type = IdType.AUTO)
    @Excel(name = "id")
    @ApiModelProperty(value = "id")
    private Long id;

    /** 通知发起方 */
    @Excel(name = "通知发起方")
    @TableField("notice_source")
    @ApiModelProperty(value = "通知发起方")
    private String noticeSource;

    /** 通知接收方 */
    @Excel(name = "通知接收方")
    @TableField("notice_target")
    @ApiModelProperty(value = "通知接收方")
    private String noticeTarget;

    /** 通知内容 */
    @Excel(name = "通知内容")
    @TableField("notice_content")
    @ApiModelProperty(value = "通知内容")
    private String noticeContent;

    /** 通知接口 */
    @Excel(name = "通知接口")
    @TableField("notice_function")
    @ApiModelProperty(value = "通知接口")
    private String noticeFunction;

    /** 通知结果 */
    @Excel(name = "通知结果")
    @TableField("notice_result")
    @ApiModelProperty(value = "通知结果")
    private String noticeResult;

    /** 是否删除,0-否，1-是 */
    @Excel(name = "是否删除,0-否，1-是")
    @TableField("is_del")
    @ApiModelProperty(value = "是否删除,0-否，1-是")
    private Boolean isDel;

    /** 是否RPA，0-否，1-是 */
    @Excel(name = "是否RPA，0-否，1-是")
    @TableField("is_rpa")
    @ApiModelProperty(value = "是否RPA，0-否，1-是")
    private Boolean isRpa;

    /** 客户名 */
    @Excel(name = "客户名")
    @TableField("customer_name")
    @ApiModelProperty(value = "客户名")
    private String customerName;

    /** 客户id */
    @Excel(name = "客户id")
    @TableField("customer_service_id")
    @ApiModelProperty(value = "客户id")
    private Long customerServiceId;

    /** 客户账期id */
    @Excel(name = "客户账期id")
    @TableField("customer_service_period_month_id")
    @ApiModelProperty(value = "客户账期id")
    private Long customerServicePeriodMonthId;

    /** 账期 */
    @Excel(name = "账期")
    @TableField("period")
    @ApiModelProperty(value = "账期")
    private Integer period;

    /** 任务类型 */
    @Excel(name = "任务类型")
    @TableField("task_type")
    @ApiModelProperty(value = "任务类型")
    private String taskType;

    /** 发起人 */
    @Excel(name = "发起人")
    @TableField("source")
    @ApiModelProperty(value = "发起人")
    private String source;

    /** 唯一编号 */
    @Excel(name = "唯一编号")
    @TableField("uuid")
    @ApiModelProperty(value = "唯一编号")
    private String uuid;

    /** 是否符合通知条件 */
    @Excel(name = "是否符合通知条件")
    @TableField("notice_condition")
    @ApiModelProperty(value = "是否符合通知条件")
    private Boolean noticeCondition;

    /** RPA处理结果，0-处理中，1-成功，2-失败 */
    @Excel(name = "RPA处理结果，0-处理中，1-成功，2-失败")
    @TableField("rpa_deal_result")
    @ApiModelProperty(value = "RPA处理结果，0-处理中，1-成功，2-失败")
    private Integer rpaDealResult;

    /** 系统交付结果，1-完成，2-失败 */
    @Excel(name = "系统交付结果，1-完成，2-失败")
    @TableField("sys_deliver_result")
    @ApiModelProperty(value = "系统交付结果，1-完成，2-失败")
    private Integer sysDeliverResult;

}
