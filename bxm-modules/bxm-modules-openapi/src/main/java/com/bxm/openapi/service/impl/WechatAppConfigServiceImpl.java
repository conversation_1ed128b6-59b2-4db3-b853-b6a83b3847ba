package com.bxm.openapi.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.common.core.utils.StringUtils;
import com.bxm.openapi.domain.WechatAppConfig;
import com.bxm.openapi.mapper.WechatAppConfigMapper;
import com.bxm.openapi.service.IWechatAppConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 企微信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-06-23
 */
@Service
public class WechatAppConfigServiceImpl extends ServiceImpl<WechatAppConfigMapper, WechatAppConfig> implements IWechatAppConfigService
{
    @Autowired
    private WechatAppConfigMapper wechatAppConfigMapper;

    @Override
    public WechatAppConfig selectByCorpId(String corpId) {
        if (StringUtils.isEmpty(corpId)) {
            return null;
        }
        return getOne(new LambdaQueryWrapper<WechatAppConfig>()
                .eq(WechatAppConfig::getCorpId, corpId), false);
    }
}
