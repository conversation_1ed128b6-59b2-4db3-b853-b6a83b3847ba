package com.bxm.openapi.controller;

import com.alibaba.fastjson.JSONObject;
import com.bxm.common.core.domain.R;
import com.bxm.common.core.domain.Result;
import com.bxm.common.core.enums.OpenApiAppRelations;
import com.bxm.common.core.utils.DateUtils;
import com.bxm.common.core.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.time.LocalDateTime;
import java.util.Base64;
import java.util.Map;
import java.util.Objects;
import java.util.TreeMap;

@Slf4j
public class BaseController {

    public Result checkSign(String timestamp, String sign, String appId, Object obj) throws Exception {
        return checkSign(timestamp, sign, appId, JSONObject.parseObject(JSONObject.toJSONString(obj)));
    }

    public Result checkSign(String timestamp, String sign, String appId, Map<String, Object> bizData) throws Exception {
        if (StringUtils.isEmpty(timestamp)) {
            return Result.fail(Result.REQUEST_TIMEOUT, "时间戳为空");
        }
        String currentTimestamp = DateUtils.localDateToMillSecond(LocalDateTime.now()) - DateUtils.localDateToMillSecond(LocalDateTime.of(1970, 1, 1, 0, 0, 0)) + "";
        if (Math.abs(Long.parseLong(timestamp) - Long.parseLong(currentTimestamp)) > 10 * 60 * 1000) {
            return Result.timeout();
        }
        OpenApiAppRelations relations = OpenApiAppRelations.getByAppId(appId);
        if (Objects.isNull(relations)) {
            return Result.fail(Result.SIGN_ERROR, "appId不存在");
        }
        String appSecret = relations.getAppSecret();
        if (StringUtils.isEmpty(sign)) {
            return Result.fail(Result.SIGN_ERROR, "签名为空");
        }
        String calcSIgn = getSign(timestamp, appId, bizData, appSecret);
        if (!sign.equals(calcSIgn)) {
            log.error("签名验证错误,sign:{},calcSign:{},params:{}", sign, calcSIgn, JSONObject.toJSONString(bizData));
            return Result.signError();
        }
        return null;
    }

    public String getSign(String timestamp, String appId, Map<String, Object> bizData, String appSecret) throws Exception {
        Map<String, String> map = new TreeMap<>();
        map.put("timestamp", timestamp);
        map.put("appid", appId);
        map.put("bizData", Objects.isNull(bizData) ? "" : JSONObject.toJSONString(bizData));
        map.put("appsecret", appSecret);

        String signData = getSignData(map);
//        log.info("==========================>signData:{}", signData);
        return hmacSHA256(signData, appSecret);
    }

    public String hmacSHA256(String data, String secret) throws Exception {
        Mac sha256Mac = Mac.getInstance("HmacSHA256");
        SecretKeySpec secretKey = new SecretKeySpec(secret.getBytes(), "HmacSHA256");
        sha256Mac.init(secretKey);
        byte[] encryptedBytes = sha256Mac.doFinal(data.getBytes());
        return Base64.getEncoder().encodeToString(encryptedBytes);
    }


    public String getSignData(Map<String, String> map) {
        StringBuilder builder = new StringBuilder();
        map.values().forEach(value -> {
            if (!StringUtils.isEmpty(value)) {
                builder.append(value.trim());
            }
        });
        return builder.toString();
    }

    public <T> Result<T> convert(R<T> response) {
        Result<T> result = new Result<>();
        result.setCode(response.getCode());
        result.setMsg(response.getMsg());
        result.setData(response.getData());
        return result;
    }
}
