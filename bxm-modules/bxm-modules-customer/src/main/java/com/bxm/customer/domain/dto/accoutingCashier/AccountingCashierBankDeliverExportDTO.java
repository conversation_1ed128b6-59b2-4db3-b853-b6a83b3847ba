package com.bxm.customer.domain.dto.accoutingCashier;

import com.bxm.common.core.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AccountingCashierBankDeliverExportDTO {

    @ApiModelProperty("客户名称")
    @Excel(name = "客户名")
    private String customerName;

    @ApiModelProperty("信用代码")
    @Excel(name = "信用代码")
    private String creditCode;

    @ApiModelProperty("服务标签")
    @Excel(name = "服务标签")
    private String customerServiceTags;

    @ApiModelProperty("账期业务公司")
    @Excel(name = "账期业务公司")
    private String businessDeptName;

    @ApiModelProperty("账期")
    @Excel(name = "账期")
    private Integer period;

    @ApiModelProperty("银行信息")
    @Excel(name = "银行")
    private String bankInfo;

    @ApiModelProperty("账期顾问信息")
    @Excel(name = "账期顾问")
    private String periodAdvisorInfo;

    @ApiModelProperty("账期会计信息")
    @Excel(name = "账期会计")
    private String periodAccountingInfo;

    @ApiModelProperty("是否有银行流水")
    @Excel(name = "银行流水")
    private String hasBankPaymentStr;

    @ApiModelProperty("交付状态")
    @Excel(name = "交付状态")
    private String deliverStatusStr;
}
