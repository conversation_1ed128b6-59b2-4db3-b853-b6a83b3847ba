package com.bxm.customer.domain.vo.xqy;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class XqyReportVO {

    private List<String> taxNumberList;

    private String taxNumber;

    private String reportPeriod;

    private String operateName;

    private Integer operateType;

    private String deliverType;

    private String batchNo;

    private String notificationText;

    private String startDate;

    private String endDate;

    private String purchaseInvoiceScope;

    private Boolean isCompleted;

    private String belongYear;

    private Long deptId;
}
