package com.bxm.customer.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bxm.common.core.annotation.Excel;
import com.bxm.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 材料交接单文件对象 c_material_deliver_file
 * 
 * <AUTHOR>
 * @date 2024-12-08
 */
@Data
@ApiModel("账期清单附件")
@Accessors(chain = true)
@TableName("c_material_deliver_period_inventory_file")
public class MaterialDeliverPeriodInventoryFile extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键id */
    @TableId(value = "id", type = IdType.AUTO)
    @Excel(name = "主键id")
    @ApiModelProperty(value = "主键id")
    private Long id;

    /** 账期清单id */
    @Excel(name = "账期清单id")
    @TableField("period_inventory_id")
    @ApiModelProperty(value = "账期清单id")
    private Long periodInventoryId;

    @Excel(name = "文件序号")
    @TableField("file_number")
    @ApiModelProperty(value = "文件序号")
    private String fileNumber;

    @Excel(name = "备注")
    @TableField("remark")
    @ApiModelProperty(value = "备注")
    private String remark;

    /** 文件地址 */
    @Excel(name = "文件地址")
    @TableField("file_url")
    @ApiModelProperty(value = "文件地址")
    private String fileUrl;

    /** 文件大小 */
    @Excel(name = "文件大小")
    @TableField("file_size")
    @ApiModelProperty(value = "文件大小")
    private Long fileSize;

    /** 文件名称 */
    @Excel(name = "文件名称")
    @TableField("file_name")
    @ApiModelProperty(value = "文件名称")
    private String fileName;

    /** 文件类型，1-对账单文件/入账材料，2-回单文件 */
    @Excel(name = "文件类型，1-对账单文件/入账材料，2-回单文件")
    @TableField("file_type")
    @ApiModelProperty(value = "文件类型，1-对账单文件/入账材料，2-回单文件")
    private Integer fileType;

    @ApiModelProperty(value = "是否删除，0-否，1-是")
    @TableField("is_del")
    private Boolean isDel;
}
