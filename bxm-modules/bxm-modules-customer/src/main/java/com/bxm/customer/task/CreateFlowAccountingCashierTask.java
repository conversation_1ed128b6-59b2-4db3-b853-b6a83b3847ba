package com.bxm.customer.task;

import com.bxm.customer.service.ICustomerServiceCashierAccountingService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class CreateFlowAccountingCashierTask {

    @Autowired
    private ICustomerServiceCashierAccountingService customerServiceCashierAccountingService;

    @XxlJob("createFlowAccountingCashierTask")
    public ReturnT<String> createFlowAccountingCashierTask(String param) {
        log.info("自动创建银行流水交付单任务开始=============");
        String jobParam = XxlJobHelper.getJobParam();
        customerServiceCashierAccountingService.createFlowAccountingCashier(jobParam);
        log.info("自动创建银行流水交付单任务结束=============");
        return ReturnT.SUCCESS;
    }
}
