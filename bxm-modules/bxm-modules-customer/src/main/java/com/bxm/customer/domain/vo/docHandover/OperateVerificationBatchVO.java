package com.bxm.customer.domain.vo.docHandover;

import com.bxm.common.core.web.domain.CommonFileVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/7/7 19:27
 * happy coding!
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OperateVerificationBatchVO {
    @ApiModelProperty("材料id")
    @NotEmpty
    private List<Long> ids;

    @ApiModelProperty(value = "完整度：1-已完整、2-缺但齐、3-有缺失待补充", allowableValues = "1,2,3")
    @NotNull
    private Integer wholeLevel;

    @ApiModelProperty(value = "核验说明")
    private String verificationRemark;

    @ApiModelProperty("核验附件")
    private List<CommonFileVO> files;
}
