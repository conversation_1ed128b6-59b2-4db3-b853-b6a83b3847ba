package com.bxm.customer.domain.dto.rpa;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AliFileDTO {

    @ApiModelProperty("文件大小")
    private long fileSize;

    @ApiModelProperty("文件名称")
    private String fileName;

    @ApiModelProperty("文件链接，提交给接口的值")
    private String url;

    @ApiModelProperty("文件类型")
    private String fileType;

    @ApiModelProperty("完整的文件链接")
    private String fullUrl;
}
