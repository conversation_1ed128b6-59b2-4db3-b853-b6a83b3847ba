package com.bxm.customer.domain.dto.newCustomerTransfer;

import com.bxm.common.core.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class NewCustomerWaitTransferDTO {

    @Excel(name = "客户名")
    private String customerName;

    @Excel(name = "信用代码")
    private String creditCode;

    @Excel(name = "档案编号")
    private String serviceNumber;
}
