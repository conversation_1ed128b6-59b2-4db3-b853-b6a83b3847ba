package com.bxm.customer.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bxm.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.bxm.common.core.web.domain.BaseEntity;
import lombok.experimental.Accessors;

/**
 * 申报税种对应对象 c_report_table_tax_config
 * 
 * <AUTHOR>
 * @date 2025-06-10
 */
@Data
@ApiModel("申报税种对应对象")
@Accessors(chain = true)
@TableName("c_report_table_tax_config")
public class ReportTableTaxConfig extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键id */
    @TableId(value = "id", type = IdType.AUTO)
    @Excel(name = "主键id")
    @ApiModelProperty(value = "主键id")
    private Long id;

    /** 报表名 */
    @Excel(name = "报表名")
    @TableField("table_name")
    @ApiModelProperty(value = "报表名")
    private String tableName;

    /** 主税种名 */
    @Excel(name = "主税种名")
    @TableField("category_name")
    @ApiModelProperty(value = "主税种名")
    private String categoryName;

    /** 子税种名 */
    @Excel(name = "子税种名")
    @TableField("item_name")
    @ApiModelProperty(value = "子税种名")
    private String itemName;

    /** 申报周期 */
    @Excel(name = "申报周期")
    @TableField("report_type")
    @ApiModelProperty(value = "申报周期")
    private String reportType;

}
