package com.bxm.customer.domain.dto.batchDeliver;

import com.bxm.common.core.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SettleAccountsCreateTemplateDTO {

    @Excel(name = "企业名称")
    private String customerName;

    @Excel(name = "信用代码")
    private String creditCode;

    @Excel(name = "备注")
    private String remark;

    @Excel(name = "DDL（期望完成时间）")
    private String ddl;
}
