package com.bxm.customer.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CustomerServiceUnFrozeVO {

    @ApiModelProperty("选择的服务id列表")
    private List<Long> ids;

    @ApiModelProperty("1-上月结束，2-本月结束")
    private Integer unFrozePeriod;
}
