package com.bxm.customer.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/7/14 11:32
 * happy coding!
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CustomerServiceUnFrozeV2VO {
    @ApiModelProperty("选择的服务id列表")
    private List<Long> ids;

    //@ApiModelProperty("1-上月结束 或 之前月份，这是服务端内部使用的参数，2-本月结束")
    //1-上月结束 或 之前月份，这是服务端内部使用的参数，2-本月结束
    private Integer unFrozePeriod;

    @ApiModelProperty("解冻账期，格式 202407")
    private Integer unFrozePeriodValue;
}
