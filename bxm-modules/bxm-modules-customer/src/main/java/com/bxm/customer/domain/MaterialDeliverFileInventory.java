package com.bxm.customer.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bxm.common.core.annotation.Excel;
import com.bxm.common.core.web.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDate;

/**
 * 材料交接单文件清单对象 c_material_deliver_file_inventory
 * 
 * <AUTHOR>
 * @date 2024-12-09
 */
@Data
@ApiModel("材料交接单文件清单对象")
@Accessors(chain = true)
@TableName("c_material_deliver_file_inventory")
public class MaterialDeliverFileInventory extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键id */
    @TableId(value = "id", type = IdType.AUTO)
    @Excel(name = "主键id")
    @ApiModelProperty(value = "主键id")
    private Long id;

    /** 材料交接单id */
    @Excel(name = "材料交接单id")
    @TableField("material_deliver_id")
    @ApiModelProperty(value = "材料交接单id")
    private Long materialDeliverId;

    /** 文件名称 */
    @Excel(name = "文件名称")
    @TableField("material_file_name")
    @ApiModelProperty(value = "文件名称")
    private String materialFileName;

    /** 客户名 */
    @Excel(name = "客户名")
    @TableField("customer_name")
    @ApiModelProperty(value = "客户名")
    private String customerName;

    /** 信用代码 */
    @Excel(name = "信用代码")
    @TableField("credit_code")
    @ApiModelProperty(value = "信用代码")
    private String creditCode;

    /** 银行名称 */
    @Excel(name = "银行名称")
    @TableField("bank_name")
    @ApiModelProperty(value = "银行名称")
    private String bankName;

    /** 银行账号 */
    @Excel(name = "银行账号")
    @TableField("bank_account_number")
    @ApiModelProperty(value = "银行账号")
    private String bankAccountNumber;

    /** 开始时间 */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @Excel(name = "开始时间", width = 30, dateFormat = "yyyy-MM-dd")
    @TableField("start_date")
    @ApiModelProperty(value = "开始时间")
    private LocalDate startDate;

    /** 结束时间 */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @Excel(name = "结束时间", width = 30, dateFormat = "yyyy-MM-dd")
    @TableField("end_date")
    @ApiModelProperty(value = "结束时间")
    private LocalDate endDate;

    /** 账期 */
    @Excel(name = "账期")
    @TableField("period")
    @ApiModelProperty(value = "账期")
    private String period;

    /** 文件序号 */
    @Excel(name = "文件序号")
    @TableField("file_number")
    @ApiModelProperty(value = "文件序号")
    private String fileNumber;

    /** 备注 */
    @Excel(name = "备注")
    @TableField("remark")
    @ApiModelProperty(value = "备注")
    private String remark;

    /** 文件地址 */
    @Excel(name = "文件地址")
    @TableField("file_url")
    @ApiModelProperty(value = "文件地址")
    private String fileUrl;

    /** 文件大小 */
    @Excel(name = "文件大小")
    @TableField("file_size")
    @ApiModelProperty(value = "文件大小")
    private Long fileSize;

    /** 文件名称 */
    @Excel(name = "文件名称")
    @TableField("file_name")
    @ApiModelProperty(value = "文件名称")
    private String fileName;

    /** 客户id */
    @Excel(name = "客户id")
    @TableField("customer_service_id")
    @ApiModelProperty(value = "客户id")
    private Long customerServiceId;

    /** 错误原因 */
    @Excel(name = "错误原因")
    @TableField("error_msg")
    @ApiModelProperty(value = "错误原因")
    private String errorMsg;

    /** 解析结果，1-正常，2-异常 */
    @Excel(name = "解析结果，1-正常，2-异常")
    @TableField("analysis_result")
    @ApiModelProperty(value = "解析结果，1-正常，2-异常")
    private Integer analysisResult;

    /** 推送结果，1-成功，2-失败*/
    @Excel(name = "推送结果，1-成功，2-失败")
    @TableField("push_result")
    @ApiModelProperty(value = "推送结果，1-成功，2-失败")
    private Integer pushResult;

    /** 重复id */
    @Excel(name = "重复id")
    @TableField("repeat_id")
    @ApiModelProperty(value = "重复id")
    private Long repeatId;

    /** 是否删除，0-否，1-是 */
    @Excel(name = "是否删除，0-否，1-是")
    @TableField("is_del")
    @ApiModelProperty(value = "是否删除，0-否，1-是")
    private Boolean isDel;
}
