package com.bxm.customer.domain.vo.repairAccount;

import com.bxm.common.core.web.domain.BaseVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

/**
 * <AUTHOR>
 * @date 2024/7/17 14:06
 * happy coding!
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RepairAccountVO extends BaseVO {
    @ApiModelProperty("关键词")
    private String keyWord;

    @ApiModelProperty("标签名称")
    private String tagName;

    @ApiModelProperty(value = "标签是否包含，1-包含，0-不包含", allowableValues = "0,1")
    private Integer tagIncludeFlag;

    @ApiModelProperty(value = "服务编号、档案编码、客户编码")
    private String serviceNumber;

    @ApiModelProperty("提交人")
    private String submitEmployee;

    @ApiModelProperty("提交时间 开始")
    private String submitTimeStart;

    @ApiModelProperty("提交时间 结束")
    private String submitTimeEnd;

    @ApiModelProperty("会计")
    private String accountingEmployee;

    @ApiModelProperty(value = "状态/分派状态: 1待完善、2待提交、3待重提、4待分派、5已分派", allowableValues = "1,2,3,4,5")
    private Integer status;

    /*
     * 当补账还是待分派时，交付状态=待交付
     * 当补账是已分派，且所有关联的账期的入账交付单都是未入账未结账时，交付状态=待交付
     * 当补账是已分派，且所有关联的账期的入账交付单都是已入账已结账时，交付状态=已完成
     * 其他状态为交付中
     */
    @ApiModelProperty(value = "交付状态：1-待交付、2-交付中、3-已完成", allowableValues = "1,2,3")
    private Integer deliverStatus;

    private Long deptId;

    @ApiModelProperty(value = "来源：1-补账菜单，2-miniList", allowableValues = "1,2")
    private Integer source;
}
