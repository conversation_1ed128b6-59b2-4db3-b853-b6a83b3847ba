package com.bxm.customer.domain.dto.accoutingCashier;

import com.bxm.common.core.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AccountingCashierInAccountErrorExportDTO {

    @ApiModelProperty("客户名称")
    @Excel(name = "客户名")
    private String customerName;

    @ApiModelProperty("信用代码")
    @Excel(name = "信用代码")
    private String creditCode;

    @ApiModelProperty("账期")
    @Excel(name = "账期")
    private Integer period;

    @ApiModelProperty("服务纳税人性质")
    @Excel(name = "服务纳税人性质")
    private String customerServiceTaxTypeStr;

    @ApiModelProperty("客户标签")
    @Excel(name = "服务标签")
    private String customerServiceTagNames;

    @ApiModelProperty("账期纳税人性质")
    @Excel(name = "账期纳税人性质")
    private String periodTaxTypeStr;

    @ApiModelProperty("账期标签")
    @Excel(name = "账期标签")
    private String periodTagNames;

    @ApiModelProperty("服务业务公司名称")
    @Excel(name = "服务业务公司")
    private String customerServiceBusinessDeptName;

    @ApiModelProperty("服务会计区域名称")
    @Excel(name = "服务会计区域")
    private String customerServiceAccountingTopDeptName;

    @ApiModelProperty("服务顾问")
    @Excel(name = "服务顾问")
    private String customerServiceAdvisorInfo;

    @ApiModelProperty("服务会计")
    @Excel(name = "服务会计")
    private String customerServiceAccountingInfo;

    @ApiModelProperty("账期会计")
    @Excel(name = "账期会计")
    private String periodAccountingInfo;

    @ApiModelProperty("是否凭票入账（文案）")
    @Excel(name = "凭票入账")
    private String hasTicketStr;

    @ApiModelProperty("交付要求")
    @Excel(name = "交付要求")
    private String deliverRequire;

    @ApiModelProperty("材料介质（文案）")
    @Excel(name = "材料介质")
    private String materialMediaStr;

    @ApiModelProperty("介质材料数量")
    @Excel(name = "材料数")
    private Long materialMediaFileCount;

    @ApiModelProperty("交付状态名称")
    @Excel(name = "交付状态")
    private String deliverStatusStr;

    @ApiModelProperty("交付结果名称")
    @Excel(name = "交付结果")
    private String deliverResultStr;

    @ApiModelProperty("完成时间（格式化后的）")
    @Excel(name = "完成时间")
    private String completeTimeStr;

    @ApiModelProperty("交付备注")
    @Excel(name = "交付备注")
    private String deliverRemark;

    @ApiModelProperty("交付附件数")
    @Excel(name = "交付附件数")
    private Long deliverFileCount;

    @ApiModelProperty("利润取数更新时间（格式化后的）")
    @Excel(name = "利润取数更新时间")
    private String profitGetTimeStr;

    @ApiModelProperty(value = "本年累计主营收入（格式化后的）")
    @Excel(name = "本年累计主营收入")
    private String majorIncomeTotalStr;

    @ApiModelProperty(value = "本年累计主营成本（格式化后的）")
    @Excel(name = "本年累计主营成本")
    private String majorCostTotalStr;

    @ApiModelProperty(value = "本年累计会计利润（格式化后的）")
    @Excel(name = "本年累计会计利润")
    private String profitTotalStr;

    /** 本年费用调增 */
    @ApiModelProperty(value = "本年费用调增")
    @Excel(name = "本年费用调增")
    private String priorYearExpenseIncrease;

    /** 个税申报人数 */
    @ApiModelProperty(value = "个税申报人数")
    @Excel(name = "个税申报人数")
    private Integer taxReportCount;

    @ApiModelProperty(value = "本年个税申报工资总额（格式化后的）")
    @Excel(name = "本年个税申报工资总额")
    private String taxReportSalaryTotalStr;

    @ApiModelProperty(value = "报表状态是否平衡（文案）")
    @Excel(name = "报表状态是否平衡")
    private String tableStatusBalanceStr;

    @ApiModelProperty(value = "RPA执行结果（文案）")
    @Excel(name = "RPA执行情况")
    private String rpaExeResultStr;

    @ApiModelProperty(value = "RPA查询时间（格式化后的）")
    @Excel(name = "RPA查询时间")
    private String rpaSearchTimeStr;

    @Excel(name = "异常原因")
    private String errorMsg;
}
