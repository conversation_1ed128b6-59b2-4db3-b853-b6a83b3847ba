package com.bxm.customer.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bxm.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.bxm.common.core.web.domain.BaseEntity;
import lombok.experimental.Accessors;

/**
 * 借阅附件对象 c_borrow_attachment
 * 
 * <AUTHOR>
 * @date 2024-07-20
 */
@Data
@ApiModel("借阅附件对象")
@Accessors(chain = true)
@TableName("c_borrow_attachment")
public class BorrowAttachment extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @TableId(value = "id", type = IdType.AUTO)
    @Excel(name = "主键ID")
    @ApiModelProperty(value = "主键ID")
    private Long id;

    /** 借阅单ID */
    @Excel(name = "借阅单ID")
    @TableField("borrow_order_id")
    @ApiModelProperty(value = "借阅单ID")
    private Long borrowOrderId;

    /** 文件名称 */
    @Excel(name = "文件名称")
    @TableField("file_name")
    @ApiModelProperty(value = "文件名称")
    private String fileName;

    /** 文件URL */
    @Excel(name = "文件URL")
    @TableField("file_url")
    @ApiModelProperty(value = "文件URL")
    private String fileUrl;

    /** 文件类型 */
    @Excel(name = "文件类型")
    @TableField("file_type")
    @ApiModelProperty(value = "文件类型，1-出站附件，2-退回附件，3-归还附件，4-验收附件")
    private Integer fileType;

    /** 是否删除，0-否，1-是 */
    @Excel(name = "是否删除，0-否，1-是")
    @TableField("is_del")
    @ApiModelProperty(value = "是否删除，0-否，1-是")
    private Boolean isDel;

}
