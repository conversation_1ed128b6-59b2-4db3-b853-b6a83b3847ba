package com.bxm.customer.domain.dto.rpa;

import com.bxm.common.core.annotation.Excel;

import java.util.Date;
import java.util.List;

public class RpaCountryTaxReportSheet1Data implements RpaEnterpriseData {

    @Excel(name = "顾问名称")
    private String advisorName;

    @Excel(name = "公司名称")
    private String enterpriseName;

    @Excel(name = "纳税识别号")
    private String creditCode;

    @Excel(name = "税务局密码")
    private String password;

    @Excel(name = "手机号码")
    private String phone;

    @Excel(name = "实名人")
    private String realName;

    @Excel(name = "清缴税款金额")
    private String reportAmount;

    @Excel(name = "执行情况")
    private String reportResult;

    @Excel(name = "增值税")
    private String valueAddTaxAmount;

    @Excel(name = "印花税")
    private String stampTaxAmount;

    @Excel(name = "工会经费")
    private String unionFundsAmount;

    @Excel(name = "文化税")
    private String cultureTaxAmount;

    @Excel(name = "财报")
    private String moneyReportAmount;

    @Excel(name = "资源税")
    private String resourceTaxAmount;

    @Excel(name = "城镇土地税")
    private String landTaxAmount;

    @Excel(name = "房产税")
    private String houseTaxAmount;

    @Excel(name = "消费及附加税")
    private String consumptionTaxAmount;

    @Excel(name = "企业所得税")
    private String incomeTaxAmount;

    @Excel(name = "水利税")
    private String waterTaxAmount;

    @Excel(name = "申报时间", dateFormat = "yyyy/M/d HH:mm:ss")
    private Date reportTime;

    @Excel(name = "异常信息")
    private String checkError;

    private List<AliFileDTO> files;

    private Long customerServiceId;

    private Long periodId;

    private Long deliverId;

    private String sheetIndex;

    @Override
    public String getSheetIndex() {
        return sheetIndex;
    }

    public void setSheetIndex(String sheetIndex) {
        this.sheetIndex = sheetIndex;
    }

    @Override
    public boolean hasErrors() {
        return checkError != null && !checkError.isEmpty();
    }

    @Override
    public void setCheckError(String checkError) {
        this.checkError = checkError;
    }

    @Override
    public void addCheckError(String checkError) {
        if (this.checkError == null || this.checkError.isEmpty()) {
            this.checkError = checkError;
        } else {
            this.checkError += "; " + checkError;
        }
    }

    @Override
    public List<AliFileDTO> getFiles() {
        return files;
    }

    public String getAdvisorName() {
        return advisorName;
    }

    public void setAdvisorName(String advisorName) {
        this.advisorName = advisorName;
    }

    public String getEnterpriseName() {
        return enterpriseName;
    }

    public void setEnterpriseName(String enterpriseName) {
        this.enterpriseName = enterpriseName;
    }

    public String getCreditCode() {
        return creditCode;
    }

    public void setCreditCode(String creditCode) {
        this.creditCode = creditCode;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getRealName() {
        return realName;
    }

    public void setRealName(String realName) {
        this.realName = realName;
    }

    public String getReportAmount() {
        return reportAmount;
    }

    public void setReportAmount(String reportAmount) {
        this.reportAmount = reportAmount;
    }

    public String getReportResult() {
        return reportResult;
    }

    public void setReportResult(String reportResult) {
        this.reportResult = reportResult;
    }

    public String getValueAddTaxAmount() {
        return valueAddTaxAmount;
    }

    public void setValueAddTaxAmount(String valueAddTaxAmount) {
        this.valueAddTaxAmount = valueAddTaxAmount;
    }

    public String getStampTaxAmount() {
        return stampTaxAmount;
    }

    public void setStampTaxAmount(String stampTaxAmount) {
        this.stampTaxAmount = stampTaxAmount;
    }

    public String getUnionFundsAmount() {
        return unionFundsAmount;
    }

    public void setUnionFundsAmount(String unionFundsAmount) {
        this.unionFundsAmount = unionFundsAmount;
    }

    public String getCultureTaxAmount() {
        return cultureTaxAmount;
    }

    public void setCultureTaxAmount(String cultureTaxAmount) {
        this.cultureTaxAmount = cultureTaxAmount;
    }

    public String getMoneyReportAmount() {
        return moneyReportAmount;
    }

    public void setMoneyReportAmount(String moneyReportAmount) {
        this.moneyReportAmount = moneyReportAmount;
    }

    public String getResourceTaxAmount() {
        return resourceTaxAmount;
    }

    public void setResourceTaxAmount(String resourceTaxAmount) {
        this.resourceTaxAmount = resourceTaxAmount;
    }

    public String getLandTaxAmount() {
        return landTaxAmount;
    }

    public void setLandTaxAmount(String landTaxAmount) {
        this.landTaxAmount = landTaxAmount;
    }

    public String getHouseTaxAmount() {
        return houseTaxAmount;
    }

    public void setHouseTaxAmount(String houseTaxAmount) {
        this.houseTaxAmount = houseTaxAmount;
    }

    public String getConsumptionTaxAmount() {
        return consumptionTaxAmount;
    }

    public void setConsumptionTaxAmount(String consumptionTaxAmount) {
        this.consumptionTaxAmount = consumptionTaxAmount;
    }

    public String getIncomeTaxAmount() {
        return incomeTaxAmount;
    }

    public void setIncomeTaxAmount(String incomeTaxAmount) {
        this.incomeTaxAmount = incomeTaxAmount;
    }

    public String getWaterTaxAmount() {
        return waterTaxAmount;
    }

    public void setWaterTaxAmount(String waterTaxAmount) {
        this.waterTaxAmount = waterTaxAmount;
    }

    public Date getReportTime() {
        return reportTime;
    }

    public void setReportTime(Date reportTime) {
        this.reportTime = reportTime;
    }

    @Override
    public String getCheckError() {
        return checkError;
    }

    public void setFiles(List<AliFileDTO> files) {
        this.files = files;
    }

    public Long getCustomerServiceId() {
        return customerServiceId;
    }

    public void setCustomerServiceId(Long customerServiceId) {
        this.customerServiceId = customerServiceId;
    }

    public Long getPeriodId() {
        return periodId;
    }

    public void setPeriodId(Long periodId) {
        this.periodId = periodId;
    }

    public Long getDeliverId() {
        return deliverId;
    }

    public void setDeliverId(Long deliverId) {
        this.deliverId = deliverId;
    }
}
