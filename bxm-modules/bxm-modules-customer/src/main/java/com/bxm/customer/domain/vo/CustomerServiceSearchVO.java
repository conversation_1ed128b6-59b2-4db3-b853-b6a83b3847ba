package com.bxm.customer.domain.vo;

import com.bxm.common.core.web.domain.BaseVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CustomerServiceSearchVO extends BaseVO {

    @ApiModelProperty("客户名称")
    private String customerName;

    @ApiModelProperty("客户企业名称")
    private String customerCompanyName;

    @ApiModelProperty("关键字搜索")
    private String keyWord;

    @ApiModelProperty("服务编号")
    private String serviceNumber;

    @ApiModelProperty("纳税人性质，1-小规模，2-一般纳税人")
    private Integer taxType;

    @ApiModelProperty(value = "服务状态,1=服务中,2=已结束,3=冻结中")
    private Integer serviceStatus;

    @ApiModelProperty("标签名称搜索")
    private String tagName;

    @ApiModelProperty("标签是否包含，1-包含，0-不包含")
    private Integer tagIncludeFlag;

    @ApiModelProperty("业务公司id")
    private Long businessDeptId;

    @ApiModelProperty("会计区域id")
    private Long accountingTopDeptId;

    @ApiModelProperty("业务公司id")
    private String businessDeptIdList;

    @ApiModelProperty("会计区域id")
    private String accountingTopDeptIdList;

    @ApiModelProperty("顾问部门id")
    private Long advisorDeptId;

    @ApiModelProperty("会计部门id")
    private Long accountingDeptId;

    @ApiModelProperty("开始账期筛选开始值")
    private Integer startPeriodStart;

    @ApiModelProperty("开始账期筛选结束值")
    private Integer startPeriodEnd;

    @ApiModelProperty("结束账期筛选开始值")
    private Integer endPeriodStart;

    @ApiModelProperty("结束账期筛选结束值")
    private Integer endPeriodEnd;

    @ApiModelProperty("筛选部门ids")
    private List<Long> queryDeptIds;

    @ApiModelProperty("顾问输入框搜索部门ids")
    private List<Long> advisorSearchDeptIds;

    @ApiModelProperty("会计输入框搜索部门ids")
    private List<Long> accountingSearchDeptIds;

    @ApiModelProperty("开票取数截至时间开始")
    private String ticketTimeStart;

    @ApiModelProperty("开票取数截至时间结束")
    private String ticketTimeEnd;

    @ApiModelProperty("12个月收入筛选结束值")
    private BigDecimal this12MonthIncomeMax;

    @ApiModelProperty("本年收入筛选开始值")
    private BigDecimal thisYearIncomeMin;

    @ApiModelProperty("本年收入筛选结束值")
    private BigDecimal thisYearIncomeMax;

    @ApiModelProperty("本季收入筛选开始值")
    private BigDecimal thisSeasonIncomeMin;

    @ApiModelProperty("本季收入筛选结束值")
    private BigDecimal thisSeasonIncomeMax;

    @ApiModelProperty("本月收入筛选开始值")
    private BigDecimal thisMonthIncomeMin;

    @ApiModelProperty("本月收入筛选结束值")
    private BigDecimal thisMonthIncomeMax;

    @ApiModelProperty("排序值，12个月收入排序-this12MonthIncome，本年收入排序-thisYearIncome，本季收入排序-thisSeasonIncome，本月收入排序-thisMonthIncome")
    private String sortBy;

    @ApiModelProperty("排序方式，正序-ASC，倒序-DESC")
    private String sortType;

    private Integer deptType;

    @ApiModelProperty("批量查询批次号")
    private String batchNo;

    private Long deptId;

    @ApiModelProperty("利润更新时间开始，yyyy-MM-dd HH:mm:ss")
    private String profitGetTimeStart;

    @ApiModelProperty("利润更新时间结束，yyyy-MM-dd HH:mm:ss")
    private String profitGetTimeEnd;

    @ApiModelProperty("需要导出的数据，1-客户信息，2-银行，3-税种，4-系统账号，多个用英文逗号隔开")
    private String exportTypes;

    private Long userId;
}
