package com.bxm.customer.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bxm.common.core.annotation.Excel;
import com.bxm.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 第三方数据推送记录对象 c_open_api_data
 * 
 * <AUTHOR>
 * @date 2024-10-23
 */
@Data
@ApiModel("第三方数据推送记录对象")
@Accessors(chain = true)
@TableName("c_open_api_data")
public class OpenApiData extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键id */
    @TableId(value = "id", type = IdType.AUTO)
    @Excel(name = "主键id")
    @ApiModelProperty(value = "主键id")
    private Long id;

    /** 客户名称 */
    @Excel(name = "客户名称")
    @TableField("customer_name")
    @ApiModelProperty(value = "客户名称")
    private String customerName;

    /** 信用代码 */
    @Excel(name = "信用代码")
    @TableField("credit_code")
    @ApiModelProperty(value = "信用代码")
    private String creditCode;

    /** 纳税人识别号 */
    @Excel(name = "纳税人识别号")
    @TableField("tax_number")
    @ApiModelProperty(value = "纳税人识别号")
    private String taxNumber;

    /** 账期 */
    @Excel(name = "账期")
    @TableField("period")
    @ApiModelProperty(value = "账期")
    private Integer period;

    /** 类型，1-收入，2-发票 */
    @Excel(name = "类型，1-收入，2-发票")
    @TableField("type")
    @ApiModelProperty(value = "类型，1-收入，2-发票")
    private Integer type;

    @TableField("plat_type")
    private String platType;

    @TableField("group_id")
    private String groupId;

    @TableField("group_name")
    private String groupName;

    @TableField("oper_type")
    private Integer operType;

    @TableField("customer_service_id")
    private Long customerServiceId;

    /** 数据json */
    @Excel(name = "数据json")
    @TableField("data_json")
    @ApiModelProperty(value = "数据json")
    private String dataJson;

    /** 完整参数json */
    @Excel(name = "完整参数json")
    @TableField("all_param")
    @ApiModelProperty(value = "完整参数json")
    private String allParam;

    @TableField(exist = false)
    private String uuid;

    @TableField(exist = false)
    private Long deptId;
}
