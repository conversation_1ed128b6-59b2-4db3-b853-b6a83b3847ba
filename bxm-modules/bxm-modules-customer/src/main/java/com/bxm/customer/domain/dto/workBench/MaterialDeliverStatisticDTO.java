package com.bxm.customer.domain.dto.workBench;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
@AllArgsConstructor
public class MaterialDeliverStatisticDTO {

    @ApiModelProperty("解析失败数量，miniList调用材料交接单列表接口，默认参数materialDeliverAnalysisStatus=3，获取miniList的提交小组下拉枚举同理")
    private Long analysisFailCount;

    @ApiModelProperty("解析异常数量，miniList调用材料交接单列表接口，默认参数materialDeliverAnalysisStatus=2，materialDeliverAnalysisResult=2，获取miniList的提交小组下拉枚举同理")
    private Long analysisExceptionCount;

    @ApiModelProperty("解析中止数量，miniList调用材料交接单列表接口，默认参数materialDeliverAnalysisStatus=4，获取miniList的提交小组下拉枚举同理")
    private Long analysisStopCount;

    @ApiModelProperty("待推送数量，miniList调用材料交接单列表接口，默认参数materialDeliverAnalysisStatus=2，materialDeliverAnalysisResult=1，materialDeliverPushStatus=1，获取miniList的提交小组下拉枚举同理")
    private Long waitPushCount;

    public MaterialDeliverStatisticDTO() {
        this.analysisFailCount = 0L;
        this.analysisExceptionCount = 0L;
        this.analysisStopCount = 0L;
        this.waitPushCount = 0L;
    }
}
