package com.bxm.customer.domain.vo;

import com.bxm.common.core.annotation.Excel;
import com.bxm.common.core.web.domain.BaseVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

/**
 * <AUTHOR>
 * @date 2024/6/11 21:07
 * happy coding!
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CustomerServicePeriodMonthSearchVO extends BaseVO {
    @ApiModelProperty("关键词")
    @Excel(name = "关键词")
    private String keyWord;

    @ApiModelProperty("客户名称")
    private String customerName;

    @ApiModelProperty("服务编号")
    private String serviceNumber;

//    @ApiModelProperty("月账期")
//    private Integer period;

    @ApiModelProperty("月账期开始")
    private Integer startPeriod;

    @ApiModelProperty("月账期结束")
    private Integer endPeriod;

    @ApiModelProperty("纳税人性质，1-小规模，2-一般纳税人")
    private Integer taxType;

    @ApiModelProperty("标签名称搜索")
    private String tagName;

    @ApiModelProperty("标签是否包含，1-包含，0-不包含")
    private Integer tagIncludeFlag;

    @ApiModelProperty(value = "服务状态,1=服务中,2=已结束,3=冻结中，确认后这里是针对客户服务状态值的筛选，不是对账期状态值的筛选。最新沟通：取账期的状态，且只有 1=服务中/正常,3=冻结中/冻结")
    private Integer serviceStatus;

    @ApiModelProperty("业务公司id")
    private Long businessDeptId;

    @ApiModelProperty("会计区域id")
    private Long accountingTopDeptId;

    @ApiModelProperty("业务公司id，多个用,隔开")
    private String businessDeptIdList;

    @ApiModelProperty("会计区域id")
    private String accountingTopDeptIdList;

    @ApiModelProperty("顾问部门id")
    private Long advisorDeptId;

    @ApiModelProperty("会计部门id")
    private Long accountingDeptId;


    @ApiModelProperty(value = "预认证", allowableValues = "-2,-1,0,1,2,3,4,5,6,7,8,9,10,101,102,103,104,105,106,107,999")
    private String preAuthStatus;

    @ApiModelProperty(value = "医保", allowableValues = "-2,-1,0,1,2,3,4,5,6,7,8,9,10,101,102,103,104,105,106,107,999")
    private String medicalInsuranceStatus;

    @ApiModelProperty(value = "社保", allowableValues = "-2,-1,0,1,2,3,4,5,6,7,8,9,10,101,102,103,104,105,106,107,999")
    private String socialInsuranceStatus;

    @ApiModelProperty(value = "个税（工资薪金）", allowableValues = "-2,-1,0,1,2,3,4,5,6,7,8,9,10,101,102,103,104,105,106,107,999")
    private String taxStatus;

    @ApiModelProperty(value = "个税（经营所得）", allowableValues = "-2,-1,0,1,2,3,4,5,6,7,8,9,10,101,102,103,104,105,106,107,999")
    private String taxOperatingStatus;

    @ApiModelProperty(value = "国税", allowableValues = "-2,-1,0,1,2,3,4,5,6,7,8,9,10,101,102,103,104,105,106,107,999")
    private String nationalTaxStatus;

    @ApiModelProperty(value = "入账状态，1-未入账，2-已入账未结账，3-已入账已结账", allowableValues = "1,2,3")
    private Integer inAccountStatus;

    //@ApiModelProperty(value = "材料完整度状态，0-无材料，1-已完整，2-缺但齐，3-有缺失，4-待核验", allowableValues = "0,1,2,3,4")
    @ApiModelProperty(value = "材料缺失：1-已完整、2-缺但齐、3-有缺失，  -2是未提交材料  4是待核验  5是无材料", allowableValues = "-2,1,2,3,4,5")
    private Integer docHandoverStatus;

    @ApiModelProperty("账务状态，1-正常，2-无需做账")
    private Integer accountingStatus;

    private Long deptId;

    private Long userId;

    @ApiModelProperty("记账类型，1-记账，2-补账")
    private Integer serviceType;

    @ApiModelProperty("预收状态，1-未预收，2-预收中，3-已预收")
    private Integer prepayStatus;

    @ApiModelProperty("结算状态，1-不可结算，2-待结算，3-结算中，4-已结算")
    private Integer settlementStatus;

    @ApiModelProperty("批量查询批次号")
    private String batchNo;

    @ApiModelProperty("服务纳税人性质，1-小规模，2-一般纳税人")
    private Integer customerServiceTaxType;

    @ApiModelProperty("服务标签名称搜索")
    private String customerServiceTagName;

    @ApiModelProperty("服务标签是否包含，1-包含，0-不包含")
    private Integer customerServiceTagIncludeFlag;

    @ApiModelProperty("银行流水结果，0-未开户，1-待创建，2-银行部分缺，3-异常，4-交付中，5-无需交付，6-无流水，7-正常完成，多个逗号隔开")
    private String bankPaymentResult;

    @ApiModelProperty("入账交付状态，1-待交付，2-交付完成，3-交付异常，4-待重提，多个逗号隔开")
    private String accountingCashierInAccountStatus;

    @ApiModelProperty("结账状态，1未入账、2已入账未结账、3已入账已结账，多个逗号隔开")
    private String settleAccountStatus;

    @ApiModelProperty("需要导出的数据，1-客户信息，2-银行，3-税种，4-系统账号，多个用英文逗号隔开")
    private String exportTypes;
}
