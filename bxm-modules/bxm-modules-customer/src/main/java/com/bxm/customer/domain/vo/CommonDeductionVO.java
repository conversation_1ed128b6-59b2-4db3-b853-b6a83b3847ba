package com.bxm.customer.domain.vo;

import com.bxm.customer.domain.vo.xqy.XqyFileVO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CommonDeductionVO {

    private String taxNumber;

    private String customerName;

    private String reportPeriod;

    private String operateName;

    private String deliverType;

    private String batchNo;

    private String remark;

    private String result;

    private List<XqyFileVO> files;

    private String sourceName;

    private Integer source;

    private Long deptId;
}
