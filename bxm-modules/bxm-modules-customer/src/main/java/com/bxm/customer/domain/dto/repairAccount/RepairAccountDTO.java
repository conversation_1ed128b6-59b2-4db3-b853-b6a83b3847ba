package com.bxm.customer.domain.dto.repairAccount;

import com.bxm.common.core.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/7/17 15:03
 * happy coding!
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RepairAccountDTO {
    @ApiModelProperty("入账交付id")
    private Long id;

    @ApiModelProperty(value = "客户服务ID")
    private Long customerServiceId;

    @Excel(name = "客户名")
    @ApiModelProperty(value = "客户名")
    private String customerName;

    @Excel(name = "档案编码")
    @ApiModelProperty(value = "服务编号、档案编码、客户编码")
    private String serviceNumber;

    @ApiModelProperty(value = "纳税人性质，1-小规模，2-一般纳税人")
    private Integer taxType;

    @Excel(name = "纳税人性质")
    @ApiModelProperty(value = "纳税人性质 字符串，1-小规模，2-一般纳税人")
    private String taxTypeStr;

    @ApiModelProperty(value = "业务公司id")
    private Long businessDeptId;

    @Excel(name = "业务公司")
    @ApiModelProperty(value = "业务公司")
    private String businessDeptName;

    @ApiModelProperty(value = "顾问部门id")
    private Long advisorDeptId;

    @Excel(name = "顾问")
    @ApiModelProperty("顾问信息总和")
    private String advisorEmployeeNameFull;

    /*@ApiModelProperty(value = "会计部门id")
    private Long accountingDeptId;

    @Excel(name = "会计")
    @ApiModelProperty("会计信息总和")
    private String accountingEmployeeNameFull;*/

    @ApiModelProperty(value = "分派员工部门ID")
    private Long assignAccountingDeptId;

    @Excel(name = "会计")
    @ApiModelProperty("会计信息总和")
    private String accountingEmployeeNameFull;

    @Excel(name = "开始账期")
    @ApiModelProperty(value = "开始账期")
    private Integer startPeriod;

    @Excel(name = "结束账期")
    @ApiModelProperty(value = "结束账期")
    private Integer endPeriod;

    //    @ApiModelProperty(value = "状态/分派状态: 1待完善、2待提交、3待分派、4已分派", allowableValues = "1,2,3,4")
    @ApiModelProperty(value = "状态/分派状态: 1待完善、2待提交、3待重提、4待分派、5已分派", allowableValues = "1,2,3,4,5")
    private Integer status;

    @Excel(name = "分派状态")
    @ApiModelProperty(value = "状态/分派状态: 1待完善、2待提交、3待重提、4待分派、5已分派")
    private String statusStr;

    /*
     * 当补账还是待分派时，交付状态=待交付
     * 当补账是已分派，且所有关联的账期的入账交付单都是未入账未结账时，交付状态=待交付
     * 当补账是已分派，且所有关联的账期的入账交付单都是已入账已结账时，交付状态=已完成
     * 其他状态为交付中
     */
    @ApiModelProperty(value = "交付状态：1-待交付、2-交付中、3-已完成", allowableValues = "1,2,3")
    private Integer deliverStatus;

    @Excel(name = "交付状态")
    @ApiModelProperty(value = "交付状态 字符串：1-待交付、2-交付中、3-已完成", allowableValues = "1,2,3")
    private String deliverStatusStr;
}
