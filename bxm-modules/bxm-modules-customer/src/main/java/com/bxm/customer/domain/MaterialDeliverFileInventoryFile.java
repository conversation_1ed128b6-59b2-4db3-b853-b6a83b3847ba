package com.bxm.customer.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bxm.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.bxm.common.core.web.domain.BaseEntity;
import lombok.experimental.Accessors;

/**
 * 文件清单附件对象 c_material_deliver_file_inventory_file
 * 
 * <AUTHOR>
 * @date 2025-02-12
 */
@Data
@ApiModel("文件清单附件对象")
@Accessors(chain = true)
@TableName("c_material_deliver_file_inventory_file")
public class MaterialDeliverFileInventoryFile extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键id */
    @TableId(value = "id", type = IdType.AUTO)
    @Excel(name = "主键id")
    @ApiModelProperty(value = "主键id")
    private Long id;

    /** 文件清单id */
    @Excel(name = "文件清单id")
    @TableField("file_inventory_id")
    @ApiModelProperty(value = "文件清单id")
    private Long fileInventoryId;

    /** 文件地址 */
    @Excel(name = "文件地址")
    @TableField("file_url")
    @ApiModelProperty(value = "文件地址")
    private String fileUrl;

    /** 文件大小 */
    @Excel(name = "文件大小")
    @TableField("file_size")
    @ApiModelProperty(value = "文件大小")
    private Long fileSize;

    /** 文件名称 */
    @Excel(name = "文件名称")
    @TableField("file_name")
    @ApiModelProperty(value = "文件名称")
    private String fileName;

    /** 文件类型，1-对账单文件/入账材料，2-回单文件 */
    @Excel(name = "文件类型，1-对账单文件/入账材料，2-回单文件")
    @TableField("file_type")
    @ApiModelProperty(value = "文件类型，1-对账单文件/入账材料，2-回单文件")
    private Integer fileType;

    /** 是否删除，0-否，1-是 */
    @Excel(name = "是否删除，0-否，1-是")
    @TableField("is_del")
    @ApiModelProperty(value = "是否删除，0-否，1-是")
    private Boolean isDel;

}
