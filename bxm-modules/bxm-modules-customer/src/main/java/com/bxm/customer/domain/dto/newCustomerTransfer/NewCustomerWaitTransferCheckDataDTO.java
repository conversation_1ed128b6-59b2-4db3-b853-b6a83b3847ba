package com.bxm.customer.domain.dto.newCustomerTransfer;

import com.bxm.common.core.annotation.Excel;
import com.bxm.common.core.utils.StringUtils;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Builder
@AllArgsConstructor
@NoArgsConstructor
public class NewCustomerWaitTransferCheckDataDTO {

    @Excel(name = "客户名")
    private String customerName;

    @Excel(name = "信用代码")
    private String creditCode;

    @Excel(name = "档案编号")
    private String serviceNumber;

    @Excel(name = "异常信息")
    private String checkError;

    private Long newCustomerTransferId;

    private Boolean isExistServiceNumber;

    private List<NewCustomerWaitTransferCheckExistServiceNumberDataDTO> existCustomerServiceList;

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getCreditCode() {
        return creditCode;
    }

    public void setCreditCode(String creditCode) {
        this.creditCode = creditCode;
    }

    public String getServiceNumber() {
        return serviceNumber;
    }

    public void setServiceNumber(String serviceNumber) {
        this.serviceNumber = serviceNumber;
    }

    public String getCheckError() {
        return checkError;
    }

    public void setCheckError(String checkError) {
        if (this.checkError == null || this.checkError.isEmpty()) {
            this.checkError = checkError;
        } else {
            this.checkError += "; " + checkError;
        }
    }

    public Long getNewCustomerTransferId() {
        return newCustomerTransferId;
    }

    public void setNewCustomerTransferId(Long newCustomerTransferId) {
        this.newCustomerTransferId = newCustomerTransferId;
    }

    public Boolean getIsExistServiceNumber() {
        return isExistServiceNumber;
    }

    public void setIsExistServiceNumber(Boolean existServiceNumber) {
        isExistServiceNumber = existServiceNumber;
    }

    public List<NewCustomerWaitTransferCheckExistServiceNumberDataDTO> getExistCustomerServiceList() {
        return existCustomerServiceList;
    }

    public void setExistCustomerServiceList(List<NewCustomerWaitTransferCheckExistServiceNumberDataDTO> existCustomerServiceList) {
        this.existCustomerServiceList = existCustomerServiceList;
    }
}
