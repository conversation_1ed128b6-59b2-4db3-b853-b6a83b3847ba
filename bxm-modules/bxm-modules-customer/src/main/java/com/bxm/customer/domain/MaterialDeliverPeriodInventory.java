package com.bxm.customer.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bxm.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.bxm.common.core.web.domain.BaseEntity;
import lombok.experimental.Accessors;

/**
 * 材料交接单账期清单对象 c_material_deliver_period_inventory
 * 
 * <AUTHOR>
 * @date 2024-12-09
 */
@Data
@ApiModel("材料交接单账期清单对象")
@Accessors(chain = true)
@TableName("c_material_deliver_period_inventory")
public class MaterialDeliverPeriodInventory extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键id */
    @TableId(value = "id", type = IdType.AUTO)
    @Excel(name = "主键id")
    @ApiModelProperty(value = "主键id")
    private Long id;

    /** 材料交接单id */
    @Excel(name = "材料交接单id")
    @TableField("material_deliver_id")
    @ApiModelProperty(value = "材料交接单id")
    private Long materialDeliverId;

    /** 交接单号 */
    @Excel(name = "交接单编号")
    @TableField("material_deliver_number")
    @ApiModelProperty(value = "交接单编号")
    private String materialDeliverNumber;

    /** 客户名 */
    @Excel(name = "客户名")
    @TableField("customer_name")
    @ApiModelProperty(value = "客户名")
    private String customerName;

    /** 银行名称 */
    @Excel(name = "银行名称")
    @TableField("bank_name")
    @ApiModelProperty(value = "银行名称")
    private String bankName;

    /** 银行账号 */
    @Excel(name = "银行账号")
    @TableField("bank_account_number")
    @ApiModelProperty(value = "银行账号")
    private String bankAccountNumber;

    /** 账期 */
    @Excel(name = "账期")
    @TableField("period")
    @ApiModelProperty(value = "账期")
    private Integer period;

    /** 客户id */
    @Excel(name = "客户id")
    @TableField("customer_service_id")
    @ApiModelProperty(value = "客户id")
    private Long customerServiceId;

    /** 客户账期id */
    @TableField("customer_service_period_month_id")
    @ApiModelProperty(value = "账期id")
    @Excel(name = "账期id")
    private Long customerServicePeriodMonthId;

    @Excel(name = "推送结果，1-成功，2-失败")
    @TableField("push_status")
    @ApiModelProperty(value = "推送结果，1-成功，2-失败")
    private Integer pushStatus;

    /** 推送结果 */
    @Excel(name = "推送结果")
    @TableField("push_result")
    @ApiModelProperty(value = "推送结果")
    private String pushResult;

    /** 是否删除，0-否，1-是 */
    @Excel(name = "是否删除，0-否，1-是")
    @TableField("is_del")
    @ApiModelProperty(value = "是否删除，0-否，1-是")
    private Boolean isDel;

    @TableField(exist = false)
    private Long accountingCashierId;
}
