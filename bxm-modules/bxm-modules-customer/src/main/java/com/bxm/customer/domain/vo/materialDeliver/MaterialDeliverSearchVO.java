package com.bxm.customer.domain.vo.materialDeliver;

import com.bxm.common.core.web.domain.BaseVO;
import com.bxm.common.customize.annotation.TimeField;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MaterialDeliverSearchVO extends BaseVO {

    @ApiModelProperty("标题搜索")
    private String title;

    @ApiModelProperty("交接单编号")
    private String materialDeliverNumber;

    @ApiModelProperty("交接单类型，1-银行流水，2-普通入账，3-凭票入账，多个用逗号隔开")
    private String materialDeliverType;

    @ApiModelProperty("解析状态，0-队列中，1-解析中，2-解析完成，3-解析失败，4-解析中止，多个用逗号隔开")
    private String materialDeliverAnalysisStatus;

    @ApiModelProperty("解析结果，1-正常，2-异常，多个用逗号隔开")
    private String materialDeliverAnalysisResult;

    @ApiModelProperty("推送状态，1-待推送，2-已推送，多个用逗号隔开")
    private String materialDeliverPushStatus;

    @ApiModelProperty("提交小组id，数据来源/bxmCustomer/select/getMaterialDeliverDeptSelectList")
    private Long commitDeptId;

    @ApiModelProperty("提交时间开始，yyyy-MM-dd")
    @TimeField(type = "start")
    private String createTimeStart;

    @ApiModelProperty("提交时间结束，yyyy-MM-dd")
    @TimeField(type = "end")
    private String createTimeEnd;

    @ApiModelProperty("最近操作人姓名")
    private String lastOperName;

    @ApiModelProperty("最近操作时间开始，yyyy-MM-dd")
    @TimeField(type = "start")
    private String lastOperTimeStart;

    @ApiModelProperty("最近操作时间结束，yyyy-MM-dd")
    @TimeField(type = "end")
    private String lastOperTimeEnd;

    @ApiModelProperty(hidden = true)
    private Long deptId;
}
