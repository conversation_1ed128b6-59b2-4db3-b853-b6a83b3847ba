package com.bxm.customer.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bxm.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.bxm.common.core.web.domain.BaseEntity;
import lombok.experimental.Accessors;

/**
 * 第三方申报同步客户对象 c_open_api_sync_customer
 * 
 * <AUTHOR>
 * @date 2024-08-23
 */
@Data
@ApiModel("第三方申报同步客户对象")
@Accessors(chain = true)
@TableName("c_open_api_sync_customer")
public class OpenApiSyncCustomer extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @TableId(value = "id", type = IdType.AUTO)
    @Excel(name = "主键ID")
    @ApiModelProperty(value = "主键ID")
    private Long id;

    /** 同步记录id */
    @Excel(name = "同步记录id")
    @TableField("syc_record_id")
    @ApiModelProperty(value = "同步记录id")
    private Long sycRecordId;

    /** 客户名称 */
    @Excel(name = "客户名称")
    @TableField("customer_name")
    @ApiModelProperty(value = "客户名称")
    private String customerName;

    /** 税号 */
    @Excel(name = "税号")
    @TableField("tax_number")
    @ApiModelProperty(value = "税号")
    private String taxNumber;

    /** 客户服务id */
    @Excel(name = "客户服务id")
    @TableField("customer_service_id")
    @ApiModelProperty(value = "客户服务id")
    private Long customerServiceId;

    @TableField("period_ids")
    @ApiModelProperty(value = "本月账期、上月账期id")
    private String periodIds;

    @TableField("income_id")
    @ApiModelProperty(value = "收入id")
    private Long incomeId;

    @TableField("income_result")
    @ApiModelProperty(value = "收入处理结果")
    private String incomeResult;

    @TableField("tax_check_error")
    @ApiModelProperty(value = "税种核定错误信息")
    private String taxCheckError;

    /** 国税处理结果 */
    @Excel(name = "国税处理结果")
    @TableField("national_tax_result")
    @ApiModelProperty(value = "国税处理结果")
    private String nationalTaxResult;

    @TableField("national_tax_deliver_id")
    @ApiModelProperty(value = "国税交付单id")
    private Long nationalTaxDeliverId;

    /** 个税（工资薪金）处理结果 */
    @Excel(name = "个税（工资薪金）处理结果")
    @TableField("person_tax_result")
    @ApiModelProperty(value = "个税（工资薪金）处理结果")
    private String personTaxResult;

    @TableField("person_tax_deliver_id")
    @ApiModelProperty(value = "个税（工资薪金）交付单id")
    private Long personTaxDeliverId;

    /** 个税（经营所得）处理结果 */
    @Excel(name = "个税（经营所得）处理结果")
    @TableField("tax_operating_result")
    @ApiModelProperty(value = "个税（经营所得）处理结果")
    private String taxOperatingResult;

    @TableField("tax_operating_deliver_id")
    @ApiModelProperty(value = "个税（经营所得）交付单id")
    private Long taxOperatingDeliverId;

    /** 医保处理结果 */
    @Excel(name = "医保处理结果")
    @TableField("medical_security_result")
    @ApiModelProperty(value = "医保处理结果")
    private String medicalSecurityResult;

    @TableField("medical_security_deliver_id")
    @ApiModelProperty(value = "医保交付单id")
    private Long medicalSecurityDeliverId;

    /** 社保处理结果 */
    @Excel(name = "社保处理结果")
    @TableField("social_security_result")
    @ApiModelProperty(value = "社保处理结果")
    private String socialSecurityResult;

    @TableField("social_security_deliver_id")
    @ApiModelProperty(value = "社保交付单id")
    private Long socialSecurityDeliverId;

    /** 预认证处理结果 */
    @Excel(name = "预认证处理结果")
    @TableField("pre_auth_result")
    @ApiModelProperty(value = "预认证处理结果")
    private String preAuthResult;

    @TableField("pre_auth_deliver_id")
    @ApiModelProperty(value = "预认证交付单id")
    private Long preAuthDeliverId;

    @TableField("pre_auth_remind")
    @ApiModelProperty(value = "认证提醒")
    private String preAuthRemind;

    /** 是否成功,0-否，1-是 */
    @Excel(name = "是否成功,0-否，1-是")
    @TableField("is_success")
    @ApiModelProperty(value = "是否成功,0-否，1-是")
    private Boolean isSuccess;

}
