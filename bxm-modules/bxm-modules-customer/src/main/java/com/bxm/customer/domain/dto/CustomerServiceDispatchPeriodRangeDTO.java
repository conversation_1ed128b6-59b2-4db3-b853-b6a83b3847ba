package com.bxm.customer.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CustomerServiceDispatchPeriodRangeDTO {

    @ApiModelProperty("账期可选范围最大值")
    private Integer maxPeriod;

    @ApiModelProperty("账期可选范围最小值")
    private Integer minPeriod;

    @ApiModelProperty("生效账期默认值")
    private Integer defaultPeriod;
}
