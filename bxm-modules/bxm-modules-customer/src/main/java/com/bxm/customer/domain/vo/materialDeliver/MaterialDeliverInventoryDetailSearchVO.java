package com.bxm.customer.domain.vo.materialDeliver;

import com.bxm.common.core.web.domain.BaseVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MaterialDeliverInventoryDetailSearchVO extends BaseVO {

    @ApiModelProperty("交接单id")
    private Long materialDeliverId;

    @ApiModelProperty("解析结果，1-正常，2-异常")
    private Integer analysisResult;

    @ApiModelProperty("异常原因")
    private List<String> errorMsgList;

    @ApiModelProperty("是否疑似重复,0-否,1-是")
    private Integer isRepeat;

    @ApiModelProperty("客户名是否不同,0-否,1-是")
    private Integer isCustomerNameNotSame;
}
