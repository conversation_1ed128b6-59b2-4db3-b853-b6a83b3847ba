package com.bxm.customer.domain.vo.accoutingCashier;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class InAccountDeliverVO {

    @ApiModelProperty("是否结账，0-否，1-是")
    private Integer isSettleAccount;

    @ApiModelProperty(value = "本年累计主营收入")
    private BigDecimal majorIncomeTotal;

    @ApiModelProperty(value = "本年累计主营成本")
    private BigDecimal majorCostTotal;

    @ApiModelProperty(value = "本年累计会计利润")
    private BigDecimal profitTotal;

    /** 本年费用调增 */
    @ApiModelProperty(value = "本年费用调增")
    private String priorYearExpenseIncrease;

    /** 个税申报人数 */
    @ApiModelProperty(value = "个税申报人数")
    private Integer taxReportCount;

    @ApiModelProperty(value = "本年个税申报工资总额")
    private BigDecimal taxReportSalaryTotal;

    @ApiModelProperty(value = "报表状态是否平衡，0-否，1-是")
    private String tableStatusBalance;
}
