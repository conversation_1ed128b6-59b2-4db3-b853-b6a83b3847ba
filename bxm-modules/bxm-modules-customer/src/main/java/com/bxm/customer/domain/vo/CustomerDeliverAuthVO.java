package com.bxm.customer.domain.vo;

import com.bxm.common.core.web.domain.CommonFileVO;
import com.bxm.customer.domain.dto.PreAuthInfoDTO;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CustomerDeliverAuthVO {

    @ApiModelProperty("交付单id")
    private Long id;

    @ApiModelProperty(value = "认证结果，1-正常，2-异常")
    private Integer authStatus;

    @ApiModelProperty(value = "进项计税范围：1-全部待抵进项用于计税、2-仅已勾选进项用于计税")
    private Integer preAuthInputTax;

    @ApiModelProperty("认证数据")
    private PreAuthInfoDTO preAuthInfoDTO;

    /** 认证备注 */
    @ApiModelProperty(value = "认证备注")
    private String authRemark;

    @ApiModelProperty("认证相关附件")
    private List<CommonFileVO> authFiles;

    @ApiModelProperty("事项备注")
    private String mattersNotes;

    @ApiModelProperty("提交类型，1-保存，2-提交")
    private Integer saveType;

    private String operName;

    private Integer source;

    @ApiModelProperty("认证提醒")
    private String preAuthRemind;

    private Long deptId;
}
