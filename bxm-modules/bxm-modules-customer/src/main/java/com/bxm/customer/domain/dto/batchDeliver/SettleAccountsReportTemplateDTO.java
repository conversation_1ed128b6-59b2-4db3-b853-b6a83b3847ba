package com.bxm.customer.domain.dto.batchDeliver;

import com.bxm.common.core.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SettleAccountsReportTemplateDTO {

    @Excel(name = "企业名称")
    private String customerName;

    @Excel(name = "信用代码")
    private String creditCode;

    @Excel(name = "申报结果：成功/异常")
    private String reportResult;

    @Excel(name = "本期")
    private String currentPeriodAmount;

    @Excel(name = "滞纳金")
    private String overdueAmount;

    @Excel(name = "往期")
    private String supplementAmount;

    @Excel(name = "申报备注")
    private String remark;
}
