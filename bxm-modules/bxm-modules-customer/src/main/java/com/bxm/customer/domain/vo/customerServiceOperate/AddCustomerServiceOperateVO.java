package com.bxm.customer.domain.vo.customerServiceOperate;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/9/12 12:49
 * happy coding!
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AddCustomerServiceOperateVO {
    @ApiModelProperty(value = "客户服务ID")
    private Long customerServiceId;

    @ApiModelProperty(value = "操作类型")
    private Integer operateType;
}
