package com.bxm.customer.domain.dto.batchDeliver;

import com.bxm.common.core.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ConfirmDeliverDTO {

    @Excel(name = "客户名")
    private String customerName;

    @Excel(name = "税号")
    private String creditCode;

    @Excel(name = "备注")
    private String remark;
}
