package com.bxm.customer.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bxm.common.core.annotation.Excel;
import com.bxm.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 客户服务工商年报信息对象 c_customer_service_annual_report_info
 * 
 * <AUTHOR>
 * @date 2024-07-01
 */
@Data
@ApiModel("客户服务工商年报信息对象")
@Accessors(chain = true)
@TableName("c_customer_service_annual_report_info")
public class CustomerServiceAnnualReportInfo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @TableId(value = "id", type = IdType.AUTO)
    @Excel(name = "主键ID")
    @ApiModelProperty(value = "主键ID")
    private Long id;

    /** 客户服务ID */
    @Excel(name = "客户服务ID")
    @TableField("customer_service_id")
    @ApiModelProperty(value = "客户服务ID")
    private Long customerServiceId;

    /** 状态, 1-已申报, 2-未申报 */
    @Excel(name = "状态, 1-已申报, 2-未申报")
    @TableField("status")
    @ApiModelProperty(value = "状态, 1-已申报, 2-未申报")
    private Integer status;

    /** 电子营业执照, 0-无, 1-有 */
    @Excel(name = "电子营业执照, 0-无, 1-有")
    @TableField("e_business_license")
    @ApiModelProperty(value = "电子营业执照, 0-无, 1-有")
    private Boolean eBusinessLicense;

    /** 联系人姓名 */
    @Excel(name = "联系人姓名")
    @TableField("contact_name")
    @ApiModelProperty(value = "联系人姓名")
    private String contactName;

    /** 联系人身份证 */
    @Excel(name = "联系人身份证")
    @TableField("contact_id_number")
    @ApiModelProperty(value = "联系人身份证")
    private String contactIdNumber;

    /** 备注 */
    @Excel(name = "备注")
    @TableField("notes")
    @ApiModelProperty(value = "备注")
    private String notes;


}
