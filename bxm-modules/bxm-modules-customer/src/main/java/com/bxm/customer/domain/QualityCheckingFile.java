package com.bxm.customer.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bxm.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.bxm.common.core.web.domain.BaseEntity;
import lombok.experimental.Accessors;

/**
 * 质检附件对象 c_quality_checking_file
 * 
 * <AUTHOR>
 * @date 2025-06-12
 */
@Data
@ApiModel("质检附件对象")
@Accessors(chain = true)
@TableName("c_quality_checking_file")
public class QualityCheckingFile extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键id */
    @TableId(value = "id", type = IdType.AUTO)
    @Excel(name = "主键id")
    @ApiModelProperty(value = "主键id")
    private Long id;

    /** 业务类型，1-质检结果，2-质检记录 */
    @Excel(name = "业务类型，1-质检结果，2-质检记录")
    @TableField("business_type")
    @ApiModelProperty(value = "业务类型，1-质检结果，2-质检记录")
    private Integer businessType;

    /** 质检结果/质检记录表id */
    @Excel(name = "质检结果/质检记录表id")
    @TableField("business_id")
    @ApiModelProperty(value = "质检结果/质检记录表id")
    private Long businessId;

    /** 文件地址 */
    @Excel(name = "文件地址")
    @TableField("file_url")
    @ApiModelProperty(value = "文件地址")
    private String fileUrl;

    /** 文件大小 */
    @Excel(name = "文件大小")
    @TableField("file_size")
    @ApiModelProperty(value = "文件大小")
    private Long fileSize;

    /** 文件名称 */
    @Excel(name = "文件名称")
    @TableField("file_name")
    @ApiModelProperty(value = "文件名称")
    private String fileName;

    /** 文件类型，1-主附件 */
    @Excel(name = "文件类型，1-主附件")
    @TableField("file_type")
    @ApiModelProperty(value = "文件类型，1-主附件")
    private Integer fileType;

    /** 是否删除，0-否，1-是 */
    @Excel(name = "是否删除，0-否，1-是")
    @TableField("is_del")
    @ApiModelProperty(value = "是否删除，0-否，1-是")
    private Boolean isDel;

}
