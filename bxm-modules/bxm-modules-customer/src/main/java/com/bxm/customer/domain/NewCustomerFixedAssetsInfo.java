package com.bxm.customer.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bxm.common.core.annotation.Excel;
import com.bxm.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 新户流转固定资产信息对象 c_new_customer_fixed_assets_info
 * 
 * <AUTHOR>
 * @date 2024-07-01
 */
@Data
@ApiModel("新户流转固定资产信息对象")
@Accessors(chain = true)
@TableName("c_new_customer_fixed_assets_info")
public class NewCustomerFixedAssetsInfo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @TableId(value = "id", type = IdType.AUTO)
    @Excel(name = "主键ID")
    @ApiModelProperty(value = "主键ID")
    private Long id;

    /** 客户ID */
    @Excel(name = "客户ID")
    @TableField("customer_id")
    @ApiModelProperty(value = "客户ID")
    private Long customerId;

    /** 一次性税前扣除固定资产 */
    @Excel(name = "一次性税前扣除固定资产")
    @TableField("asset_name")
    @ApiModelProperty(value = "一次性税前扣除固定资产")
    private String assetName;

    /** 发生年份 */
    @Excel(name = "发生年份")
    @TableField("occurrence_year")
    @ApiModelProperty(value = "发生年份")
    private String occurrenceYear;


}
