package com.bxm.customer.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bxm.common.core.annotation.Excel;
import com.bxm.common.core.web.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 入账、入账交付对象 c_customer_service_in_account
 *
 * <AUTHOR>
 * @date 2024-07-11
 */
@Data
@ApiModel("入账交付单编辑记录")
@Accessors(chain = true)
@TableName("c_customer_service_in_account_update_record")
public class CustomerServiceInAccountUpdateRecord extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @TableId(value = "id", type = IdType.AUTO)
    @Excel(name = "主键ID")
    @ApiModelProperty(value = "主键ID")
    private Long id;

    @TableField("operate_date")
    @ApiModelProperty("入账编辑日期")
    private Integer operateDate;

    @TableField("customer_service_id")
    @ApiModelProperty("客户服务ID")
    private Long customerServiceId;
}
