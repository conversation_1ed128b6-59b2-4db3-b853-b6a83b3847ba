package com.bxm.customer.domain.dto.batchDeliver;

import com.bxm.common.core.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MedicalSocialSecurityRejectTemplateDTO {

    @Excel(name = "企业名")
    private String customerName;

    @Excel(name = "信用代码")
    private String creditCode;

    @Excel(name = "医保驳回（是）")
    private String medicalRejectResult;

    @Excel(name = "医保备注")
    private String medicalRemark;

    @Excel(name = "社保驳回（是）")
    private String socialRejectResult;

    @Excel(name = "社保备注")
    private String socialRemark;
}
