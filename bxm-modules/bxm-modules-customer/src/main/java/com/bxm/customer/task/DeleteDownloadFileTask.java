package com.bxm.customer.task;

import com.bxm.customer.service.IDownloadRecordService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class DeleteDownloadFileTask {

    @Autowired
    private IDownloadRecordService downloadRecordService;

    @XxlJob("deleteDownloadFileTask")
    public ReturnT<String> deleteDownloadFile(String param) {
        log.info("每日删除导出记录文件任务开始=============");
        String jobParam = XxlJobHelper.getJobParam();
        downloadRecordService.deleteDownloadFile(jobParam);
        log.info("每日删除导出记录文件任务开始=============");
        return ReturnT.SUCCESS;
    }
}
