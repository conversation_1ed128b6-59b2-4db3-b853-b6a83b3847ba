package com.bxm.customer.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 增值交付单文件类型枚举
 *
 * <AUTHOR>
 * @date 2025-01-29
 */
@Getter
@AllArgsConstructor
public enum ValueAddedFileType {

    /**
     * 交付材料附件
     */
    DELIVERY_ATTACHMENT(1, "交付材料附件"),

    /**
     * 人员明细excel
     */
    PERSONNEL_EXCEL(2, "人员明细excel");

    private final Integer code;
    private final String name;

    /**
     * 根据代码获取枚举
     *
     * @param code 文件类型代码
     * @return 对应的枚举值
     */
    public static ValueAddedFileType getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (ValueAddedFileType fileType : values()) {
            if (fileType.getCode().equals(code)) {
                return fileType;
            }
        }
        return null;
    }

    /**
     * 验证文件类型代码是否有效
     *
     * @param code 文件类型代码
     * @return 是否有效
     */
    public static boolean isValid(Integer code) {
        return getByCode(code) != null;
    }
}
