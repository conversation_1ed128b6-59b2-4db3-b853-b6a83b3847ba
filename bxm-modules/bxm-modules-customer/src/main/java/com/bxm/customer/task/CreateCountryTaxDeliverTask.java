package com.bxm.customer.task;

import com.bxm.customer.service.ICustomerDeliverService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class CreateCountryTaxDeliverTask {

    @Autowired
    private ICustomerDeliverService customerDeliverService;

    @XxlJob("createCountryTaxDeliverTask")
    public ReturnT<String> createCountryTaxDeliver(String param) {
        log.info("每月自动创建国税交付单任务开始=============");
        String jobParam = XxlJobHelper.getJobParam();
        customerDeliverService.createCountryTaxDeliver(jobParam);
        log.info("每月自动创建国税交付单任务结束=============");
        return ReturnT.SUCCESS;
    }
}
