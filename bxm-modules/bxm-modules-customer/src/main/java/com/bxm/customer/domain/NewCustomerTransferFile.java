package com.bxm.customer.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bxm.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.bxm.common.core.web.domain.BaseEntity;
import lombok.experimental.Accessors;

/**
 * 新户流转客户文件对象 c_new_customer_transfer_file
 * 
 * <AUTHOR>
 * @date 2024-08-15
 */
@Data
@ApiModel("新户流转客户文件对象")
@Accessors(chain = true)
@TableName("c_new_customer_transfer_file")
public class NewCustomerTransferFile extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @TableId(value = "id", type = IdType.AUTO)
    @Excel(name = "主键ID")
    @ApiModelProperty(value = "主键ID")
    private Long id;

    /** 客户ID */
    @Excel(name = "客户ID")
    @TableField("customer_id")
    @ApiModelProperty(value = "客户ID")
    private Long customerId;

    /** 文件名 */
    @Excel(name = "文件名")
    @TableField("file_name")
    @ApiModelProperty(value = "文件名")
    private String fileName;

    /** 文件地址 */
    @Excel(name = "文件地址")
    @TableField("file_url")
    @ApiModelProperty(value = "文件地址")
    private String fileUrl;

    /** 文件类型, 1-流转附件 */
    @Excel(name = "文件类型, 1-流转附件")
    @TableField("file_type")
    @ApiModelProperty(value = "文件类型, 1-流转附件")
    private Integer fileType;

    /** 是否删除, 0-否, 1-是 */
    @Excel(name = "是否删除, 0-否, 1-是")
    @TableField("is_del")
    @ApiModelProperty(value = "是否删除, 0-否, 1-是")
    private Boolean isDel;

}
