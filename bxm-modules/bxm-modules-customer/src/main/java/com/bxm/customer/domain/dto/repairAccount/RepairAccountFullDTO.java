package com.bxm.customer.domain.dto.repairAccount;

import com.bxm.common.core.annotation.Excel;
import com.bxm.common.core.web.domain.CommonFileVO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/7/26 23:20
 * happy coding!
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RepairAccountFullDTO {
    @ApiModelProperty("服务类型")
    private String serviceTypeStr;

    @ApiModelProperty("服务账期")
    private String periodBetween;

    @Excel(name = "提交人")
    private String submitEmployeeNameFull;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "提交时间")
    private LocalDateTime submitTime;

    @ApiModelProperty("会计")
    private String accountingEmployeeNameFull;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty("附件")
    private List<CommonFileVO> files;

    @ApiModelProperty("账期列表")
    private List<RepairAccountFullPeriodDTO> periods;
}
