package com.bxm.customer.domain.vo.docHandover.file;

import com.bxm.common.core.enums.docHandover.DocHandoverFileType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/7/9 19:38
 * happy coding!
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class FileByDocHandoverVO {
    private Long customerServiceDocHandoverId;

    private List<DocHandoverFileType> docHandoverFileTypes;
}
