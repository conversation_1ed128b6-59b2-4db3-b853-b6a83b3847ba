package com.bxm.customer.domain.vo.businessTask;

import com.bxm.common.core.annotation.Excel;
import com.bxm.common.core.web.domain.BaseVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/25 17:06
 * happy coding!
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BusinessTaskVO extends BaseVO {
    @ApiModelProperty("关键词")
    @Excel(name = "关键词")
    private String keyWord;

    @ApiModelProperty("标签名称")
    private String tagName;

    @ApiModelProperty(value = "标签是否包含，1-包含，0-不包含", allowableValues = "0,1")
    private Integer tagIncludeFlag;

    @ApiModelProperty("服务标签名称")
    private String customerServiceTagName;

    @ApiModelProperty(value = "服务标签是否包含，1-包含，0-不包含", allowableValues = "0,1")
    private Integer customerServiceTagIncludeFlag;

    @ApiModelProperty(value = "纳税人性质，1-小规模，2-一般纳税人", allowableValues = "1,2")
    private Integer taxType;

    @ApiModelProperty("账期 开始")
    private Integer periodStart;

    @ApiModelProperty("账期 结束")
    private Integer periodEnd;

    @ApiModelProperty(value = "事项，1-银行流水")
    private List<Integer> itemTypeList;

    @ApiModelProperty(value = "监管人")
    private Long adminUserId;

    @ApiModelProperty(value = "执行人")
    private Long executeUserId;

    @ApiModelProperty(value = "会计部门id")
    private Long accountingDeptId;

    @ApiModelProperty(value = "任务状态：1-待完成、2-待审核、3-已完结、4-已关闭、5-异常", allowableValues = "1,2,3,4,5")
    private List<Integer> statusList;

    @ApiModelProperty(value = "完成结果：1-正常完成、2-已开户无流水、3-未开户、4-银行部分缺、5-无需交付、6-无法完成", allowableValues = "1,2,3,4,5,6")
    private Integer finishResult;

    @ApiModelProperty(value = "完成结果，1-正常完成、2-已开户无流水、3-未开户、4-银行部分缺、5-无需交付、6-无法完成，多个用逗号隔开")
    private String finishResultList;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("截止日期 开始，格式 2024-01-08")
    private LocalDate deadlineStart;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("截止日期 结束，格式 2024-01-08")
    private LocalDate deadlineEnd;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "执行时间 开始，格式 2024-01-08 00:00:00")
    private LocalDateTime executeTimeStart;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "执行时间 开始，格式 2024-01-08 23:59:59")
    private LocalDateTime executeTimeEnd;

    @ApiModelProperty(value = "最后操作人")
    private String lastOperateUserName;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "执行时间 开始，格式 2024-01-08 00:00:00")
    private LocalDateTime lastOperateTimeStart;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "执行时间 开始，格式 2024-01-08 23:59:59")
    private LocalDateTime lastOperateTimeEnd;

    @ApiModelProperty("首次完成时间 开始，格式 2024-01-08")
    private String firstCompleteTimeStart;

    @ApiModelProperty("首次完成时间 结束，格式 2024-01-08")
    private String firstCompleteTimeEnd;

    @ApiModelProperty("纸质，0-无，1-有")
    private Integer mediumPaper;

    @ApiModelProperty("电子，0-无，1-有")
    private Integer mediumElectric;

    @ApiModelProperty("银企，0-无，1-有")
    private Integer mediumBank;

    @ApiModelProperty("业务公司id")
    private Long periodBusinessDeptId;

    @ApiModelProperty(value = "派单状态：1-待派单，2-已派单")
    private Integer dispatchStatus;

    @ApiModelProperty("批量查询批次号")
    private String batchNo;

    @ApiModelProperty("银行名称搜索")
    private String bankName;

    @ApiModelProperty("银行账号搜索")
    private String bankAccountNumber;

    @ApiModelProperty("是否有事项备注，1-有，0-无")
    private Integer hasMattersNotes;

    @ApiModelProperty("是否有银行流水，1-有，0-无")
    private Integer hasBankPayment;

    @ApiModelProperty(hidden = true)
    private Long deptId;

    @ApiModelProperty(hidden = true)
    private Long userId;
}