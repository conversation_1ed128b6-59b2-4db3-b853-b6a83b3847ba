package com.bxm.customer.domain.vo.newCustomer;

import com.bxm.common.core.web.domain.CommonFileVO;
import com.bxm.customer.domain.dto.TagDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class NewCustomerCreateBaseInfoVO {

    @ApiModelProperty("客户企业名")
    @NotEmpty(message = "客户企业名不能为空")
    private String customerName;

    @ApiModelProperty("信用代码")
    @NotEmpty(message = "信用代码不能为空")
    private String creditCode;

    @ApiModelProperty("税号")
    @NotEmpty(message = "税号不能为空")
    private String taxNumber;

    @ApiModelProperty("注册时间")
    @NotEmpty(message = "注册时间不能为空")
    private String registrationDate;

    private String registerLocation;

    @ApiModelProperty(value = "省份编码")
    @NotEmpty(message = "注册区域不能为空")
    private String provinceCode;

    @ApiModelProperty(value = "省份名称")
    @NotEmpty(message = "注册区域不能为空")
    private String provinceName;

    @ApiModelProperty(value = "城市编码")
    @NotEmpty(message = "注册区域不能为空")
    private String cityCode;

    @ApiModelProperty(value = "城市名称")
    @NotEmpty(message = "注册区域不能为空")
    private String cityName;

    @ApiModelProperty(value = "区域编码")
//    @NotEmpty(message = "注册区域不能为空")
    private String areaCode;

    @ApiModelProperty(value = "区域名称")
//    @NotEmpty(message = "注册区域不能为空")
    private String areaName;

    @ApiModelProperty("所属行业")
    @NotEmpty(message = "所属行业不能为空")
    private String industry;

    @ApiModelProperty("纳税人性质, 1-小规模, 2-一般纳税人")
    @NotNull(message = "纳税人性质不能为空")
    private Integer taxType;

    @ApiModelProperty("标签,只传选中的标签")
    private List<TagDTO> tags;

    @ApiModelProperty("业务公司ID")
    private Long businessDeptId;

    @ApiModelProperty("顾问组别ID")
    @NotNull(message = "顾问小组不能为空")
    private Long advisorDeptId;

    @ApiModelProperty("首个账期,yyyy-MM")
    @NotEmpty(message = "首个账期不能为空")
    private String firstAccountPeriod;

    @ApiModelProperty("首个记账账期,yyyy-MM")
    @NotEmpty(message = "首个记账账期不能为空")
    private String firstAccountingPeriod;

    @ApiModelProperty("是否新客户, 0-否, 1-是")
    @NotNull(message = "请选择是否新户")
    private Boolean isNewCustomer;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("附件")
    private List<CommonFileVO> files;
}
