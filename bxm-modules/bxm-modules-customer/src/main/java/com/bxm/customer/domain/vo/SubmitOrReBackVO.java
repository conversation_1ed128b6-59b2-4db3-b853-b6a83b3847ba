package com.bxm.customer.domain.vo;

import com.bxm.common.core.web.domain.CommonFileVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SubmitOrReBackVO {

    @ApiModelProperty("选中的交付单id列表（批量操作的时候传）")
    private List<Long> ids;

    @ApiModelProperty("交付单id（单个操作的时候传）")
    private Long id;

    @ApiModelProperty("前置状态")
    private Integer preStatus;

    @ApiModelProperty("退回原因")
    private String remark;

    @ApiModelProperty("退回附件")
    private List<CommonFileVO> files;
}
