package com.bxm.customer.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bxm.common.core.annotation.Excel;
import com.bxm.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 客户服务变更记录对象 c_customer_service_change_record
 * 
 * <AUTHOR>
 * @date 2024-05-11
 */
@Data
@ApiModel("客户服务变更记录对象")
@Accessors(chain = true)
@TableName("c_customer_service_change_record")
public class CCustomerServiceChangeRecord extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    @TableId(value = "id", type = IdType.AUTO)
    @Excel(name = "id")
    @ApiModelProperty(value = "id")
    private Long id;

    /** 客户服务id */
    @Excel(name = "客户服务id")
    @TableField("customer_service_id")
    @ApiModelProperty(value = "客户服务id")
    private Long customerServiceId;

    /** 生效账期 */
    @Excel(name = "生效账期")
    @TableField("valid_period")
    @ApiModelProperty(value = "生效账期")
    private Integer validPeriod;

    /** 变更类型，1-结束服务，2-冻结，3-解冻，4-变更业务公司，5-信息变更 */
    @Excel(name = "变更类型，1-结束服务，2-冻结，3-解冻，4-变更业务公司，5-信息变更")
    @TableField("change_type")
    @ApiModelProperty(value = "变更类型，1-结束服务，2-冻结，3-解冻，4-变更业务公司，5-信息变更")
    private Integer changeType;

    /** 变更明细 */
    @Excel(name = "变更明细")
    @TableField("change_content")
    @ApiModelProperty(value = "变更明细")
    private String changeContent;

    /** 是否有效，0-否，1-是 */
    @Excel(name = "是否有效，0-否，1-是")
    @TableField("is_valid")
    @ApiModelProperty(value = "是否有效，0-否，1-是")
    private Boolean isValid;

    /** 是否执行，0-否，1-是 */
    @Excel(name = "是否执行，0-否，1-是")
    @TableField("is_done")
    @ApiModelProperty(value = "是否执行，0-否，1-是")
    private Boolean isDone;

}
