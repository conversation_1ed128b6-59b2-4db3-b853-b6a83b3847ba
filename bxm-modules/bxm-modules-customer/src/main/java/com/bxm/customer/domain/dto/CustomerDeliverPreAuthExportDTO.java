package com.bxm.customer.domain.dto;

import com.bxm.common.core.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CustomerDeliverPreAuthExportDTO {

    @ApiModelProperty("客户名")
    @Excel(name = "客户名")
    private String customerName;

    @ApiModelProperty("信用代码")
    @Excel(name = "信用代码")
    private String creditCode;

    @ApiModelProperty("账期")
    private Integer period;

    @Excel(name = "账期")
    @ApiModelProperty("账期")
    private String periodStr;

    @Excel(name = "ddl")
    private String ddlStr;

    /** 提交人名称 */
    @Excel(name = "提交人")
    @ApiModelProperty(value = "提交人名称")
    private String employeeName;

    private Long advisorDeptId;

    private Long accountingDeptId;

    private String advisorDeptName;

    private String accountingDeptName;

    @ApiModelProperty("账期业务公司")
    @Excel(name = "账期归属业务公司")
    private String businessDeptName;

    @Excel(name = "顾问")
    @ApiModelProperty("顾问信息")
    private String advisorDeptInfo;

    @Excel(name = "会计")
    @ApiModelProperty("会计信息")
    private String accountingDeptInfo;

    @Excel(name = "提交时间", dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("提交时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @Excel(name = "无票收入（顾问提交）")
    @ApiModelProperty(value = "无票收入")
    private String preAuthNoTicketIncomeStr;

    @Excel(name = "事项备忘")
    @ApiModelProperty(value = "事项备忘")
    private String mattersNotesDetail;

    @Excel(name = "交付要求")
    private String createRemark;

    @Excel(name = "交付单附件")
    private Long createFileCount;

    @ApiModelProperty("状态")
    @Excel(name = "状态")
    private String statusStr;

//    @ApiModelProperty("是否辅导期")
//    @Excel(name = "是否辅导期")
//    private String isTutor;

    @ApiModelProperty("上期留抵税额")
    @Excel(name = "上期留抵税额")
    private String lastPeriodCarriedForwardTax;

    @ApiModelProperty("本期已认证进项金额")
    @Excel(name = "本期已认证进项金额")
    private String currentCertifiedInputAmount;

    @ApiModelProperty("本期待确认进项金额")
    @Excel(name = "本期待确认进项金额")
    private String currentPendingInputAmount;

    @ApiModelProperty("本月(季)销售金额")
    @Excel(name = "本月(季)销售金额")
    private String currentMonthOrQuarterSalesAmount;

    @ApiModelProperty("无票收入")
    @Excel(name = "无票收入")
    private String unInvoicedIncome;

    @ApiModelProperty("本期销项税额")
    @Excel(name = "本期销项税额")
    private String currentOutputTax;

    @ApiModelProperty("本期简易征收税额")
    @Excel(name = "本期简易征收税额")
    private String currentSimpleTaxCollection;

    @ApiModelProperty("本期已认证进项税额")
    @Excel(name = "本期已认证进项税额")
    private String currentCertifiedInputTax;

    @ApiModelProperty("本期待确认进项税额")
    @Excel(name = "本期待确认进项税额")
    private String currentPendingInputTax;

    @ApiModelProperty("本期已勾选进项税额")
    @Excel(name = "本期已勾选进项税额")
    private String currentSelectedInputTax;

    @ApiModelProperty("用于计税的待确认进项范围")
    @Excel(name = "用于计税的待确认进项范围")
    private String pendingInputTaxForCalculation;

    @ApiModelProperty("本期已勾选并确认的进项税额")
    @Excel(name = "本期已勾选并确认的进项税额")
    private String currentSelectedAndCertifiedInputTax;

    @ApiModelProperty("本期红字进项应转出税额")
    @Excel(name = "本期红字进项应转出税额")
    private String currentRedLetterInputTaxShouldRevert;

    @ApiModelProperty("本期预计应缴增值税")
    @Excel(name = "本期预计应缴增值税")
    private String currentExpectedPayableVat;

    @ApiModelProperty("本期已缴增值税")
    @Excel(name = "本期已缴增值税")
    private String currentPaidVat;

    @ApiModelProperty("本期实际申报认证进项税额")
    @Excel(name = "本期实际申报认证进项税额")
    private String currentActualReportedInputTax;

    @ApiModelProperty("本期预计结余可用进项税额")
    @Excel(name = "本期预计结余可用进项税额")
    private String currentExpectedRemainingInputTax;

    @ApiModelProperty("本期开票张数")
    @Excel(name = "本期开票张数")
    private String currentInvoiceCount;

    @ApiModelProperty("上年增值税税负率")
    @Excel(name = "上年增值税税负率")
    private String lastYearVatBurdenRate;

    @ApiModelProperty("截止上期本年增值税税负率")
    @Excel(name = "截止上期本年增值税税负率")
    private String cumulativeVatBurdenRateToLastPeriod;

    @ApiModelProperty("截止上期本年累计缴纳增值税")
    @Excel(name = "截止上期本年累计缴纳增值税")
    private String cumulativeVatPaidToLastPeriod;

    @ApiModelProperty("截止上月本年累计加速加计扣除进项税额")
    @Excel(name = "截止上月本年累计加速加计扣除进项税额")
    private String cumulativeAcceleratedAdditionalDeductedInputTaxToLastMonth;

    @ApiModelProperty("截止上期本年累计营业额")
    @Excel(name = "截止上期本年累计营业额")
    private String cumulativeTurnoverToLastPeriod;

    @ApiModelProperty("截止本月本年累计营业额")
    @Excel(name = "截止本月本年累计营业额")
    private String cumulativeTurnoverToCurrentMonth;

    @ApiModelProperty("截止本月本年累计销项税额")
    @Excel(name = "截止本月本年累计销项税额")
    private String cumulativeOutputTaxToCurrentMonth;

    @ApiModelProperty("截止本月本年累计加速加计扣除进项税额")
    @Excel(name = "截止本月本年累计加速加计扣除进项税额")
    private String cumulativeAcceleratedAdditionalDeductedInputTaxToCurrentMonth;

    @ApiModelProperty("截止本月本年累计预计缴纳增值税额")
    @Excel(name = "截止本月本年累计预计缴纳增值税额")
    private String cumulativeExpectedVatToCurrentMonth;

    @ApiModelProperty("截止本月预计增值税税负率")
    @Excel(name = "截止本月预计增值税税负率")
    private String estimatedVatBurdenRateToCurrentMonth;

    @ApiModelProperty("截止本月本年累计实际抵扣进项税额")
    @Excel(name = "截止本月本年累计实际抵扣进项税额")
    private String cumulativeActualDeductedInputTaxToCurrentMonth;

    @ApiModelProperty("截止本月本年累计进项发票金额（不含税）")
    @Excel(name = "截止本月本年累计进项发票金额（不含税）")
    private String cumulativeInputInvoiceAmountExcludingTaxToCurrentMonth;

    @ApiModelProperty("截止本月本年累计进项发票金额（含税）")
    @Excel(name = "截止本月本年累计进项发票金额（含税）")
    private String cumulativeInputInvoiceAmountIncludingTaxToCurrentMonth;

    @ApiModelProperty("近12个月累计开票金额")
    @Excel(name = "近12个月累计开票金额")
    private String cumulativeInvoicedAmountLast12Months;

//    @ApiModelProperty("预提报附件数量")
//    @Excel(name = "预提报附件数量")
//    private Long createFileCount;

    @ApiModelProperty("认证附件数量")
    @Excel(name = "认证附件数量")
    private Long authFileCount;

    @Excel(name = "最后操作")
    private String lastOperType;

    @Excel(name = "最后操作时间", dateFormat = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastOperTime;

    @Excel(name = "最后操作人")
    private String lastOperName;

    @Excel(name = "最后操作备注")
    private String lastOperRemark;
}
