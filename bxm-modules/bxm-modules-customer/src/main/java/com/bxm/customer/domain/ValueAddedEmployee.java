package com.bxm.customer.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bxm.common.core.annotation.Excel;
import com.bxm.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 增值员工信息对象 c_value_added_employee
 *
 * <AUTHOR>
 * @date 2025-01-27
 */
@Data
@ApiModel("增值员工信息对象")
@Accessors(chain = true)
@TableName("c_value_added_employee")
public class ValueAddedEmployee extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @TableId(value = "id", type = IdType.AUTO)
    @Excel(name = "主键ID")
    @ApiModelProperty(value = "主键ID")
    private Long id;

    /** 增值交付单编号 */
    @Excel(name = "增值交付单编号")
    @TableField("delivery_order_no")
    @ApiModelProperty(value = "增值交付单编号")
    private String deliveryOrderNo;

    /** 业务类型，1-社医保，2-个税明细，3-国税账号，4-个税账号 */
    @Excel(name = "业务类型")
    @TableField("biz_type")
    @ApiModelProperty(value = "业务类型，1-社医保，2-个税明细，3-国税账号，4-个税账号")
    private Integer bizType;

    /** 录入方式，1-批量新增，2-单个新增 */
    @Excel(name = "录入方式")
    @TableField("entry_type")
    @ApiModelProperty(value = "录入方式，1-批量新增，2-单个新增")
    private Integer entryType;

    /** 操作方式，社医保/个税明细：1-提醒，2-更正，3-减员；国税账号：1-会计实名，2-异地实名；个税账号：1-个税账号添加 */
    @Excel(name = "操作方式")
    @TableField("operation_type")
    @ApiModelProperty(value = "操作方式，社医保/个税明细：1-提醒，2-更正，3-减员；国税账号：1-会计实名，2-异地实名；个税账号：1-个税账号添加")
    private Integer operationType;

    /** 员工姓名 */
    @Excel(name = "员工姓名")
    @TableField("employee_name")
    @ApiModelProperty(value = "员工姓名")
    private String employeeName;

    /** 身份证号 */
    @Excel(name = "身份证号")
    @TableField("id_number")
    @ApiModelProperty(value = "身份证号")
    private String idNumber;

    /** 手机号 */
    @Excel(name = "手机号")
    @TableField("mobile")
    @ApiModelProperty(value = "手机号")
    private String mobile;

    /** 应发工资 */
    @Excel(name = "应发工资")
    @TableField("gross_salary")
    @ApiModelProperty(value = "应发工资")
    private BigDecimal grossSalary;

    /** 公积金（个人） */
    @Excel(name = "公积金（个人）")
    @TableField("provident_fund_personal")
    @ApiModelProperty(value = "公积金（个人）")
    private BigDecimal providentFundPersonal;

    /** 税号（国税账号业务专用，可同信用代码） */
    @Excel(name = "税号")
    @TableField("tax_number")
    @ApiModelProperty(value = "税号（国税账号业务专用，可同信用代码）")
    private String taxNumber;

    /** 登录密码（国税账号/个税账号业务专用） */
    @Excel(name = "登录密码")
    @TableField("query_password")
    @ApiModelProperty(value = "登录密码（国税账号/个税账号业务专用）")
    private String queryPassword;

    /** 登录方式（个税账号业务专用） */
    @Excel(name = "登录方式")
    @TableField("login_method")
    @ApiModelProperty(value = "登录方式（个税账号业务专用）")
    private String loginMethod;

    /** 实名经办人（个税账号业务专用） */
    @Excel(name = "实名经办人")
    @TableField("real_name_agent")
    @ApiModelProperty(value = "实名经办人（个税账号业务专用）")
    private String realNameAgent;

    /** 社保套餐信息，JSON格式：{"yang_lao":true,"shi_ye":true,"gong_shang":true,"yi_liao":true,"sheng_yu":true,"qi_ta":true} */
    @Excel(name = "社保套餐信息")
    @TableField("social_insurance_package")
    @ApiModelProperty(value = "社保套餐信息，JSON格式")
    private String socialInsurancePackage;

    /** 扩展信息，JSON格式存储其他业务特定字段 */
    @Excel(name = "扩展信息")
    @TableField("extend_info")
    @ApiModelProperty(value = "扩展信息，JSON格式存储其他业务特定字段")
    private String extendInfo;

    /** 备注 */
    @Excel(name = "备注")
    @TableField("remark")
    @ApiModelProperty(value = "备注")
    private String remark;

    /** 状态，1-待处理，2-已处理，3-已完成 */
    @Excel(name = "状态")
    @TableField("status")
    @ApiModelProperty(value = "状态，1-待处理，2-已处理，3-已完成")
    private Integer status;
}
