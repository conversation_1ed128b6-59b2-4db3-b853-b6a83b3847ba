package com.bxm.customer.domain.dto.settlementOrder;

import com.bxm.common.core.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Getter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SettlementOrderCustomerUploadDTO {

    @Excel(name = "客户名")
    private String customerName;

    @Excel(name = "信用代码")
    private String creditCode;

    @Excel(name = "异常信息")
    private String checkError;

    private Long customerServiceId;

    private Long customerServicePeriodMonthId;

    public void setCustomerServiceId(Long customerServiceId) {
        this.customerServiceId = customerServiceId;
    }

    public void setCustomerServicePeriodMonthId(Long customerServicePeriodMonthId) {
        this.customerServicePeriodMonthId = customerServicePeriodMonthId;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public void setCreditCode(String creditCode) {
        this.creditCode = creditCode;
    }


    public void setCheckError(String checkError) {
        if (this.checkError == null || this.checkError.isEmpty()) {
            this.checkError = checkError;
        } else {
            this.checkError += "; " + checkError;
        }
    }

}
