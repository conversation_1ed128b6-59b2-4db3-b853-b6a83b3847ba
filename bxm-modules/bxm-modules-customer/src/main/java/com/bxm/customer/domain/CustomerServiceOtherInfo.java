package com.bxm.customer.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bxm.common.core.annotation.Excel;
import com.bxm.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 客户服务其他信息对象 c_customer_service_other_info
 * 
 * <AUTHOR>
 * @date 2024-07-01
 */
@Data
@ApiModel("客户服务其他信息对象")
@Accessors(chain = true)
@TableName("c_customer_service_other_info")
public class CustomerServiceOtherInfo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @TableId(value = "id", type = IdType.AUTO)
    @Excel(name = "主键ID")
    @ApiModelProperty(value = "主键ID")
    private Long id;

    /** 客户服务ID */
    @Excel(name = "客户服务ID")
    @TableField("customer_service_id")
    @ApiModelProperty(value = "客户服务ID")
    private Long customerServiceId;

    /** 汇算清缴状态, 1-已申报已扣款, 2-已申报未扣款 */
    @Excel(name = "汇算清缴状态, 1-已申报已扣款, 2-已申报未扣款")
    @TableField("tax_submission_status")
    @ApiModelProperty(value = "汇算清缴状态, 1-已申报已扣款, 2-已申报未扣款")
    private Integer taxSubmissionStatus;

    /** 所得税交税金额 */
    @Excel(name = "所得税交税金额")
    @TableField("pre_tax_profit")
    @ApiModelProperty(value = "所得税交税金额")
    private BigDecimal preTaxProfit;

    /** 下一年可弥补金额 */
    @Excel(name = "下一年可弥补金额")
    @TableField("next_year_supplement")
    @ApiModelProperty(value = "下一年可弥补金额")
    private BigDecimal nextYearSupplement;

    /** 备注 */
    @Excel(name = "备注")
    @TableField("notes")
    @ApiModelProperty(value = "备注")
    private String notes;



}
