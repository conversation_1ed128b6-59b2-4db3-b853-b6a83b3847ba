package com.bxm.customer.domain.dto.workBench;

import com.bxm.common.core.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class QualityExceptionMiniListDTO {

    @ApiModelProperty("质检结果id")
    private Long qualityCheckingResultId;

    @ApiModelProperty("客户id")
    private Long customerServiceId;

    @ApiModelProperty("账期id")
    private Long customerServicePeriodMonthId;

    @ApiModelProperty("客户名称")
    @Excel(name = "客户名")
    private String customerName;

    @ApiModelProperty("质检事项id")
    private Long qualityCheckingItemId;

    @ApiModelProperty("质检事项名称")
    @Excel(name = "质检事项")
    private String qualityCheckingItemName;

    @ApiModelProperty("账期，yyyyMM")
    private Integer period;

    @ApiModelProperty("账期，yyyy-MM")
    @Excel(name = "账期")
    private String periodStr;

    @ApiModelProperty("顾问小组id")
    private Long advisorDeptId;

    @ApiModelProperty("账期顾问")
    @Excel(name = "账期顾问")
    private String advisorInfo;

    @ApiModelProperty("会计小组id")
    private Long accountingDeptId;

    @ApiModelProperty("账期会计")
    @Excel(name = "账期会计")
    private String accountingInfo;
}
