package com.bxm.customer.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 文件状态信息DTO
 * 用于封装文件处理状态相关信息，便于使用BeanUtils进行对象复制
 *
 * <AUTHOR>
 * @date 2025-01-30
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("文件状态信息DTO")
public class FileStatusInfo {

    /** 处理状态 */
    @ApiModelProperty(value = "处理状态，0-处理中，1-处理完成，2-处理失败")
    private Integer status;

    /** 状态消息 */
    @ApiModelProperty(value = "状态消息")
    private String message;

    /** 总数量 */
    @ApiModelProperty(value = "总数量")
    private Integer totalCount;

    /** 成功数量 */
    @ApiModelProperty(value = "成功数量")
    private Integer successCount;

    /** 失败数量 */
    @ApiModelProperty(value = "失败数量")
    private Integer failCount;

    /** 是否有错误文件 */
    @ApiModelProperty(value = "是否有错误文件")
    private Boolean hasErrorFile;

    /** 更新时间戳 */
    @ApiModelProperty(value = "更新时间戳")
    private Long updateTime;
}
