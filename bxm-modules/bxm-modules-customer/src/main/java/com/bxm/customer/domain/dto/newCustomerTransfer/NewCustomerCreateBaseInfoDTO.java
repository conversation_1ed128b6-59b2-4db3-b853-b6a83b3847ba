package com.bxm.customer.domain.dto.newCustomerTransfer;

import com.bxm.common.core.web.domain.CommonFileVO;
import com.bxm.customer.domain.dto.TagDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class NewCustomerCreateBaseInfoDTO {

    @ApiModelProperty(value = "新户流转ID")
    private Long newCustomerTransferId;

    @ApiModelProperty("客户企业名")
    private String customerName;

    @ApiModelProperty("信用代码")
    private String creditCode;

    @ApiModelProperty("税号")
    private String taxNumber;

    @ApiModelProperty("注册时间")
    private String registrationDate;

    @ApiModelProperty(value = "省份编码")
    private String provinceCode;

    @ApiModelProperty(value = "省份名称")
    private String provinceName;

    @ApiModelProperty(value = "城市编码")
    private String cityCode;

    @ApiModelProperty(value = "城市名称")
    private String cityName;

    @ApiModelProperty(value = "区域编码")
    private String areaCode;

    @ApiModelProperty(value = "区域名称")
    private String areaName;

    @ApiModelProperty("所属行业")
    private String industry;

    @ApiModelProperty("纳税人性质, 1-小规模, 2-一般纳税人")
    private Integer taxType;

    @ApiModelProperty("纳税人性质")
    private String taxTypeStr;

    @ApiModelProperty("标签")
    private List<TagDTO> tags;

    @ApiModelProperty("业务公司ID")
    private Long businessDeptId;

    @ApiModelProperty("业务公司名称")
    private String businessDeptName;

    @ApiModelProperty("顾问组别ID")
    private Long advisorDeptId;

    @ApiModelProperty("顾问组别ID路径")
    private List<Long> advisorDeptIdPath;

    @ApiModelProperty("顾问组别名称")
    private String advisorDeptName;

    @ApiModelProperty("顾问组别信息(带员工)")
    private String advisorDeptInfo;

    @ApiModelProperty("首个账期,yyyy-MM")
    private String firstAccountPeriod;

    @ApiModelProperty("首个记账账期,yyyy-MM")
    private String firstAccountingPeriod;

    @ApiModelProperty("是否新客户, 0-否, 1-是")
    private Integer isNewCustomer;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("附件")
    private List<CommonFileVO> files;
}
