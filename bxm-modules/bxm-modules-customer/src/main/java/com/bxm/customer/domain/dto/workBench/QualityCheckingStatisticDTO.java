package com.bxm.customer.domain.dto.workBench;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
@AllArgsConstructor
public class QualityCheckingStatisticDTO {

    @ApiModelProperty("异常（账务问题）数量")
    private Long exceptionAccountingCount;

    @ApiModelProperty("异常（风险提示）数量")
    private Long exceptionRiskCount;

    public QualityCheckingStatisticDTO() {
        this.exceptionAccountingCount = 0L;
        this.exceptionRiskCount = 0L;
    }
}
