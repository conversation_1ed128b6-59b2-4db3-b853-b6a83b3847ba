package com.bxm.customer.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bxm.common.core.annotation.Excel;
import com.bxm.common.core.web.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDate;

/**
 * 结算单关联数据临时对象 c_settlement_order_data_temp
 * 
 * <AUTHOR>
 * @date 2024-09-15
 */
@Data
@ApiModel("结算单关联数据来临时对象")
@Accessors(chain = true)
@TableName("c_settlement_order_data_temp")
public class SettlementOrderDataTemp extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 账单id */
    @TableId(value = "id", type = IdType.AUTO)
    @Excel(name = "账单id")
    @ApiModelProperty(value = "账单id")
    private Long id;

    /** 结算单批次号 */
    @Excel(name = "结算单批次号")
    @TableField("settlement_order_batch_no")
    @ApiModelProperty(value = "结算单批次号")
    private String settlementOrderBatchNo;

    /** 业务id，服务id或账期id */
    @Excel(name = "业务id，服务id或账期id")
    @TableField("business_id")
    @ApiModelProperty(value = "业务id，服务id或账期id")
    private Long businessId;

    /** 业务类型，1-服务，2-账期 */
    @Excel(name = "业务类型，1-服务，2-账期")
    @TableField("business_type")
    @ApiModelProperty(value = "业务类型，1-服务，2-账期")
    private Integer businessType;

    @Excel(name = "业务公司id")
    @TableField("business_dept_id")
    @ApiModelProperty(value = "业务公司id")
    private Long businessDeptId;

    /** 客户服务id */
    @Excel(name = "客户服务id")
    @TableField("customer_service_id")
    @ApiModelProperty(value = "客户服务id")
    private Long customerServiceId;

    /** 服务纳税人性质 */
    @Excel(name = "服务纳税人性质")
    @TableField("customer_service_tax_type")
    @ApiModelProperty(value = "服务纳税人性质")
    private Integer customerServiceTaxType;

    /** 服务顾问小组id */
    @Excel(name = "服务顾问小组id")
    @TableField("customer_service_advisor_dept_id")
    @ApiModelProperty(value = "服务顾问小组id")
    private Long customerServiceAdvisorDeptId;

    /** 服务顾问小组名称 */
    @Excel(name = "服务顾问小组名称")
    @TableField("customer_service_advisor_dept_name")
    @ApiModelProperty(value = "服务顾问小组名称")
    private String customerServiceAdvisorDeptName;

    /** 服务顾问人员名称 */
    @Excel(name = "服务顾问人员名称")
    @TableField("customer_service_advisor_employee_name")
    @ApiModelProperty(value = "服务顾问人员名称")
    private String customerServiceAdvisorEmployeeName;

    /** 服务会计小组id */
    @Excel(name = "服务会计小组id")
    @TableField("customer_service_accounting_dept_id")
    @ApiModelProperty(value = "服务会计小组id")
    private Long customerServiceAccountingDeptId;

    /** 服务会计小组名称 */
    @Excel(name = "服务会计小组名称")
    @TableField("customer_service_accounting_dept_name")
    @ApiModelProperty(value = "服务会计小组名称")
    private String customerServiceAccountingDeptName;

    /** 服务会计人员名称 */
    @Excel(name = "服务会计人员名称")
    @TableField("customer_service_accounting_employee_name")
    @ApiModelProperty(value = "服务会计人员名称")
    private String customerServiceAccountingEmployeeName;

    /** 服务首个账期 */
    @Excel(name = "服务首个账期")
    @TableField("customer_service_first_account_period")
    @ApiModelProperty(value = "服务首个账期")
    private Integer customerServiceFirstAccountPeriod;

    /** 服务会计备注 */
    @Excel(name = "服务会计备注")
    @TableField("customer_service_accounting_remark")
    @ApiModelProperty(value = "服务会计备注")
    private String customerServiceAccountingRemark;

    /** 账期 */
    @Excel(name = "账期")
    @TableField("period")
    @ApiModelProperty(value = "账期")
    private Integer period;

    /** 账期类型，1-代账，2-补账 */
    @Excel(name = "账期类型，1-代账，2-补账")
    @TableField("period_service_type")
    @ApiModelProperty(value = "账期类型，1-代账，2-补账")
    private Integer periodServiceType;

    /** 账期纳税人性质 */
    @Excel(name = "账期纳税人性质")
    @TableField("period_tax_type")
    @ApiModelProperty(value = "账期纳税人性质")
    private Integer periodTaxType;

    /** 账期顾问小组id */
    @Excel(name = "账期顾问小组id")
    @TableField("period_advisor_dept_id")
    @ApiModelProperty(value = "账期顾问小组id")
    private Long periodAdvisorDeptId;

    /** 账期顾问小组名称 */
    @Excel(name = "账期顾问小组名称")
    @TableField("period_advisor_dept_name")
    @ApiModelProperty(value = "账期顾问小组名称")
    private String periodAdvisorDeptName;

    /** 账期顾问人员名称 */
    @Excel(name = "账期顾问人员名称")
    @TableField("period_advisor_employee_name")
    @ApiModelProperty(value = "账期顾问人员名称")
    private String periodAdvisorEmployeeName;

    /** 账期会计小组id */
    @Excel(name = "账期会计小组id")
    @TableField("period_accounting_dept_id")
    @ApiModelProperty(value = "账期会计小组id")
    private Long periodAccountingDeptId;

    /** 账期会计小组名称 */
    @Excel(name = "账期会计小组名称")
    @TableField("period_accounting_dept_name")
    @ApiModelProperty(value = "账期会计小组名称")
    private String periodAccountingDeptName;

    /** 账期会计人员名称 */
    @Excel(name = "账期会计人员名称")
    @TableField("period_accounting_employee_name")
    @ApiModelProperty(value = "账期会计人员名称")
    private String periodAccountingEmployeeName;

    /** 账务状态，1-正常，2-无需做账 */
    @Excel(name = "账务状态，1-正常，2-无需做账")
    @TableField("period_accounting_status")
    @ApiModelProperty(value = "账务状态，1-正常，2-无需做账")
    private Integer periodAccountingStatus;

    /** 交付结果：1-正常、2-无需交付、3-异常 */
    @Excel(name = "交付结果：1-正常、2-无需交付、3-异常")
    @TableField("period_in_account_deliver_result")
    @ApiModelProperty(value = "交付结果：1-正常、2-无需交付、3-异常")
    private Integer periodInAccountDeliverResult;

    @TableField(value = "period_bank_payment_input_result")
    @ApiModelProperty(value = "银行流水录入结果，1-正常2-异常3-无需交付4-已开户无流水5-未开户6-银行部分缺")
    private Integer periodBankPaymentInputResult;

    @TableField(value = "period_in_account_status")
    @ApiModelProperty(value = "入账交付状态，1-待交付，2-交付完成，3-交付异常，4-待重提")
    private Integer periodInAccountStatus;

    @TableField(value = "period_in_account_result")
    @ApiModelProperty(value = "入账结果，1-正常2-异常3-无需交付4-无账务")
    private Integer periodInAccountResult;

    /** 银行流水录入日期 */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @Excel(name = "银行流水录入日期", width = 30, dateFormat = "yyyy-MM-dd")
    @TableField("period_in_account_bank_payment_input_time")
    @ApiModelProperty(value = "银行流水录入日期")
    private LocalDate periodInAccountBankPaymentInputTime;

    /** 入账时间 */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @Excel(name = "入账时间", width = 30, dateFormat = "yyyy-MM-dd")
    @TableField("period_in_account_in_time")
    @ApiModelProperty(value = "入账时间")
    private LocalDate periodInAccountInTime;

    /** 结账时间 */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @Excel(name = "结账时间", width = 30, dateFormat = "yyyy-MM-dd")
    @TableField("period_in_account_end_time")
    @ApiModelProperty(value = "结账时间")
    private LocalDate periodInAccountEndTime;

    /** 入账rpa备注 */
    @Excel(name = "入账rpa备注")
    @TableField("period_in_account_rpa_remark")
    @ApiModelProperty(value = "入账rpa备注")
    private String periodInAccountRpaRemark;

    /** 创建小组id */
    @Excel(name = "创建小组id")
    @TableField("create_dept_id")
    @ApiModelProperty(value = "创建小组id")
    private Long createDeptId;

    /** 创建小组名称 */
    @Excel(name = "创建小组名称")
    @TableField("create_dept_name")
    @ApiModelProperty(value = "创建小组名称")
    private String createDeptName;

}
