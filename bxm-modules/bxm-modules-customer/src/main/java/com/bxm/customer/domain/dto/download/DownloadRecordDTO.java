package com.bxm.customer.domain.dto.download;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DownloadRecordDTO {

    @ApiModelProperty("导出记录id")
    private Long id;

    @ApiModelProperty("导出记录标题")
    private String title;

    @ApiModelProperty("导出记录数据量")
    private Long dataCount;

    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @ApiModelProperty("导出状态,0-未完成，1-导出成功，2-导出失败")
    private Integer status;

//    @ApiModelProperty("下载地址")
//    private String downloadUrl;

    @ApiModelProperty("文件类型，0-excel，1-zip")
    private Integer fileType;
}
