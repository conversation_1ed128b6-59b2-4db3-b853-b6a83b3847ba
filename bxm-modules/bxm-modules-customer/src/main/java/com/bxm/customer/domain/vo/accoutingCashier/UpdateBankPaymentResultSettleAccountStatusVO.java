package com.bxm.customer.domain.vo.accoutingCashier;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UpdateBankPaymentResultSettleAccountStatusVO {

    private List<Long> customerServicePeriodMonthIds;

    private Long deptId;

    private Long userId;

    private String operName;

    private String operType;
}
