package com.bxm.customer.domain;

import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bxm.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.bxm.common.core.web.domain.BaseEntity;
import lombok.experimental.Accessors;

/**
 * 质检结果对象 c_quality_checking_result
 * 
 * <AUTHOR>
 * @date 2025-06-12
 */
@Data
@ApiModel("质检结果对象")
@Accessors(chain = true)
@TableName("c_quality_checking_result")
public class QualityCheckingResult extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键id */
    @TableId(value = "id", type = IdType.AUTO)
    @Excel(name = "主键id")
    @ApiModelProperty(value = "主键id")
    private Long id;

    /** 客户id */
    @Excel(name = "客户id")
    @TableField("customer_service_id")
    @ApiModelProperty(value = "客户id")
    private Long customerServiceId;

    /** 账期id */
    @Excel(name = "账期id")
    @TableField("customer_service_period_month_id")
    @ApiModelProperty(value = "账期id")
    private Long customerServicePeriodMonthId;

    /** 账期 */
    @Excel(name = "账期")
    @TableField("period")
    @ApiModelProperty(value = "账期")
    private Integer period;

    /** 质检事项id */
    @Excel(name = "质检事项id")
    @TableField("quality_checking_item_id")
    @ApiModelProperty(value = "质检事项id")
    private Long qualityCheckingItemId;

    /** 质检类型，1-账务问题，2-风险提示 */
    @Excel(name = "质检类型，1-账务问题，2-风险提示")
    @TableField("quality_checking_type")
    @ApiModelProperty(value = "质检类型，1-账务问题，2-风险提示")
    private Integer qualityCheckingType;

    /** 质检周期，1-单期，2-累计 */
    @Excel(name = "质检周期，1-单期，2-累计")
    @TableField("quality_checking_cycle")
    @ApiModelProperty(value = "质检周期，1-单期，2-累计")
    private Integer qualityCheckingCycle;

    /** 状态，0-未执行，1-执行中，2-已执行 */
    @Excel(name = "状态，0-未执行，1-执行中，2-已执行")
    @TableField("status")
    @ApiModelProperty(value = "状态，0-未执行，1-执行中，2-已执行")
    private Integer status;

    /** 质检结果，1-正常，2-异常 */
    @Excel(name = "质检结果，1-正常，2-异常")
    @TableField("checking_result")
    @ApiModelProperty(value = "质检结果，1-正常，2-异常")
    private Integer checkingResult;

    /** 首次质检时间 */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "首次质检时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @TableField("first_checking_time")
    @ApiModelProperty(value = "首次质检时间")
    private LocalDateTime firstCheckingTime;

    /** 最后质检时间 */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "最后质检时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @TableField("last_checking_time")
    @ApiModelProperty(value = "最后质检时间")
    private LocalDateTime lastCheckingTime;

    /** 备注 */
    @Excel(name = "备注")
    @TableField("remark")
    @ApiModelProperty(value = "备注")
    private String remark;

    /** 是否删除，0-否，1-是 */
    @Excel(name = "是否删除，0-否，1-是")
    @TableField("is_del")
    @ApiModelProperty(value = "是否删除，0-否，1-是")
    private Boolean isDel;
}
