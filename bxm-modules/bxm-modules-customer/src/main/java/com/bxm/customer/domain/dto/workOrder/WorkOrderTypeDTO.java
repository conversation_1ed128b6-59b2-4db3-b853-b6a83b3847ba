package com.bxm.customer.domain.dto.workOrder;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class WorkOrderTypeDTO {

    @ApiModelProperty("工单类型")
    private Integer workOrderType;

    @ApiModelProperty(value = "工单类型名称")
    private String workOrderTypeName;

    @ApiModelProperty(value = "客户是否必填")
    private Boolean isNeedCustomer;

    @ApiModelProperty(value = "账期是否必填")
    private Boolean isNeedPeriod;

    @ApiModelProperty(value = "备注是否必填")
    private Boolean isNeedRemark;

    @ApiModelProperty(value = "默认工单备注")
    private String defaultRemark;
}
