package com.bxm.customer.domain.dto.newCustomerTransfer;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class NewCustomerTransferInsuranceDTO {

    @ApiModelProperty("人数")
    private String peopleCount;

    @ApiModelProperty("人员")
    private String peopleInfo;

    @ApiModelProperty("基数")
    private String base;

    @ApiModelProperty("比例")
    private String rate;

    @ApiModelProperty("工伤金额")
    private String injuryFee;

    @ApiModelProperty("工伤比例")
    private String injuryRate;

    @ApiModelProperty("缴纳金额")
    private String totalContribution;

    @ApiModelProperty("每月状态，编辑时需要用到的，会返回未来月份")
    private List<NewCustomerTransferInsuranceMonthDTO> monthStatusForEdit;

    @ApiModelProperty("每月状态，详情需要用的到，只会返回到当前月，未来月份不会返回")
    private List<NewCustomerTransferInsuranceMonthDTO> monthStatusForDetail;
}
