package com.bxm.customer.domain.dto.settlementOrder;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
public class AddDataResultDTO {

    @ApiModelProperty("总数据")
    private Long totalCount;

    @ApiModelProperty("成功数据")
    private Long successCount;

    @ApiModelProperty("失败数据")
    private Long failCount;

    public AddDataResultDTO() {
        this.totalCount = 0L;
        this.successCount = 0L;
        this.failCount = 0L;
    }
}
