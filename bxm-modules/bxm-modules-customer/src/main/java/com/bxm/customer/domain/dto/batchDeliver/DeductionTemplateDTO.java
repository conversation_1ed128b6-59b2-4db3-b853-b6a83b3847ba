package com.bxm.customer.domain.dto.batchDeliver;

import com.bxm.common.core.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DeductionTemplateDTO {

    @Excel(name = "企业名称")
    private String customerName;

    @Excel(name = "信用代码")
    private String creditCode;

    @Excel(name = "扣款结果：成功/异常")
    private String deductionResult;

    @Excel(name = "扣款备注")
    private String deductionRemark;
}
