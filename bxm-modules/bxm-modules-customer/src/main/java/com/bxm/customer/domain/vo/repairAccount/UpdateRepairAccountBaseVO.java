package com.bxm.customer.domain.vo.repairAccount;

import com.bxm.common.core.web.domain.CommonFileVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/7/21 15:54
 * happy coding!
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UpdateRepairAccountBaseVO {
    @ApiModelProperty(value = "id")
    @NotNull
    private Long id;

    @ApiModelProperty(value = "服务账期 开始")
    @NotNull(message = "请选择服务账期")
    private Integer periodStart;

    @ApiModelProperty(value = "服务账期 结束，这个值不是用户选的，是 /getRepairAccountPeriodEnd 这个获得的")
    @NotNull(message = "服务账期不可为空")
    private Integer periodEnd;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty("附件")
    private List<CommonFileVO> files;
}
