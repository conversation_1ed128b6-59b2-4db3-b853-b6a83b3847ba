package com.bxm.customer.task;

import com.bxm.customer.service.ICCustomerServiceService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class SyncStatusTask {

    @Autowired
    private ICCustomerServiceService customerServiceService;

    // 兜底，每天同步一下账期上的交付单状态和最大入账id
    @XxlJob("SyncStatusTask")
    public ReturnT<String> syncStatusTask(String param) {
        log.info("兜底同步状态开始=============");
        customerServiceService.syncStatusTask();
        log.info("兜底同步状态结束=============");
        return ReturnT.SUCCESS;
    }
}
