package com.bxm.customer.domain.dto.materialDeliver;

import com.bxm.common.core.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MaterialDeliverBankDetailV2DTO {

    @ApiModelProperty("解析结果(文案)")
    @Excel(name = "解析结果")
    private String analysisResultStr;

    @ApiModelProperty("异常原因")
    @Excel(name = "异常原因")
    private String errorMsg;

    @ApiModelProperty("客户名称")
    @Excel(name = "客户名")
    private String customerName;

    @ApiModelProperty("银行信息")
    @Excel(name = "银行")
    private String bankInfo;

    @Excel(name = "交接账期起")
    private String startDate;

    @Excel(name = "交接账期止")
    private String endDate;

    @Excel(name = "对账单文件数量")
    private Long checkFileCount;

    @Excel(name = "回单文件数量")
    private Long receiptFileCount;
}
