package com.bxm.customer.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CustomerSearchResultDTO {

    @ApiModelProperty("是否查询到结果，true-是，false-否")
    private Boolean hasResult;

    @ApiModelProperty("服务id")
    private Long id;

    @ApiModelProperty("业务公司名称")
    private String businessDeptName;

    @ApiModelProperty("会计部门名称")
    private String accountingParentDeptName;

    @ApiModelProperty("会计小组名称")
    private String accountingDeptName;
}
