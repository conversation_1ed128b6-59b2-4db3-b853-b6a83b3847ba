package com.bxm.customer.domain.dto.workBench;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
@AllArgsConstructor
public class DocHandoverWorkBenchDTO {

    @ApiModelProperty("待核验数量")
    private Long waitCheckCount;

    @ApiModelProperty("待完善数量")
    private Long needPerfectCount;

    @ApiModelProperty("待重提数量")
    private Long needReSubmitCount;

    @ApiModelProperty("待提交数量")
    private Long needSubmitCount;

    public DocHandoverWorkBenchDTO() {
        this.waitCheckCount = 0L;
        this.needPerfectCount = 0L;
        this.needReSubmitCount = 0L;
        this.needSubmitCount = 0L;
    }
}
