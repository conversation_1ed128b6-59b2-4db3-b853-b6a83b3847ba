package com.bxm.customer.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bxm.common.core.annotation.Excel;
import com.bxm.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 新户流转五险一金月状态对象 c_new_customer_insurance_fund_status
 * 
 * <AUTHOR>
 * @date 2024-07-01
 */
@Data
@ApiModel("新户流转五险一金月状态对象")
@Accessors(chain = true)
@TableName("c_new_customer_insurance_fund_status")
public class NewCustomerInsuranceFundStatus extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @TableId(value = "id", type = IdType.AUTO)
    @Excel(name = "主键ID")
    @ApiModelProperty(value = "主键ID")
    private Long id;

    /** 新户流转id */
    @Excel(name = "新户流转id")
    @TableField("customer_id")
    @ApiModelProperty(value = "新户流转id")
    private Long customerId;

    /** 类型，1-医保，2-社保，3-公积金 */
    @Excel(name = "类型，1-医保，2-社保，3-公积金")
    @TableField("type")
    @ApiModelProperty(value = "类型，1-医保，2-社保，3-公积金")
    private Integer type;

    /** 月份 */
    @Excel(name = "月份")
    @TableField("month")
    @ApiModelProperty(value = "月份")
    private Integer month;

    /** 状态, 1-待申报，2-待扣款，3-已扣款，4-同上月 */
    @Excel(name = "状态, 1-待申报，2-待扣款，3-已扣款，4-同上月")
    @TableField("status")
    @ApiModelProperty(value = "状态, 1-待申报，2-待扣款，3-已扣款，4-同上月")
    private Integer status;

    /** 状态值 */
    @Excel(name = "状态值")
    @TableField("status_str")
    @ApiModelProperty(value = "状态值")
    private String statusStr;
}
