package com.bxm.customer.domain.dto.inAccount;

import com.bxm.common.core.annotation.Excel;
import com.bxm.common.core.web.domain.CommonFileVO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/19 11:22
 * happy coding!
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class InAccountRpaUpdateShowDTO {
    @ApiModelProperty("入账交付id")
    private Long id;

    @ApiModelProperty(value = "交付类型")
    private String typeStr;

    @ApiModelProperty(value = "客户服务ID")
    private Long customerServiceId;

    @Excel(name = "客户名")
    @ApiModelProperty(value = "客户名")
    private String customerName;

    @ApiModelProperty(value = "月度账期id")
    private Long customerServicePeriodMonthId;

    @ApiModelProperty(value = "账期")
    private Integer period;

    @ApiModelProperty(value = "入账状态，1未入账、2已入账未结账、3已入账已结账")
    private Integer status;

    @ApiModelProperty(value = "入账状态，字符串")
    private String statusStr;

    @ApiModelProperty(value = "RPA执行结果：1-成功、0-失败")
    private Integer rpaExeResult;

    @ApiModelProperty(value = "RPA执行结果 字符串：1-成功、0-失败")
    private String rpaExeResultStr;

    @ApiModelProperty(value = "主营收入累计")
    private BigDecimal majorIncomeTotal;

    @ApiModelProperty(value = "主营成本累计")
    private BigDecimal majorCostTotal;

    @ApiModelProperty(value = "利润总计")
    private BigDecimal profitTotal;

    @ApiModelProperty(value = "个税申报人数")
    private Integer taxReportCount;

    @ApiModelProperty(value = "本年个税申报工资总额")
    private BigDecimal taxReportSalaryTotal;

    @ApiModelProperty(value = "报表状态是否平衡")
    private String tableStatusBalance;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("RPA查询时间")
    private LocalDateTime rpaSearchTime;

    @ApiModelProperty(value = "RPA备注")
    private String rpaRemark;

    @ApiModelProperty("RPA附件")
    private List<CommonFileVO> rpaFiles;
}
