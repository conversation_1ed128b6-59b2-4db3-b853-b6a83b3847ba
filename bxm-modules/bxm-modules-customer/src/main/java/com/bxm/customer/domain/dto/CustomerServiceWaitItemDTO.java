package com.bxm.customer.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CustomerServiceWaitItemDTO {

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("客户服务id")
    private Long customerServiceId;

    @ApiModelProperty("客户企业名称")
    private String customerName;

    @ApiModelProperty("客户名称")
    private String customerCompanyName;

    @ApiModelProperty("客户服务编号")
    private String serviceNumber;

    @ApiModelProperty("纳税人性质，1-小规模，2-一般纳税人")
    private Integer taxType;

    @ApiModelProperty("标签")
    private List<TagDTO> tagList;

    @ApiModelProperty("顾问小组id")
    private Long advisorDeptId;

    @ApiModelProperty("顾问小组名称")
    private String advisorDeptName;

    @ApiModelProperty("顾问员工名称")
    private String advisorEmployeeNames;

    @ApiModelProperty("会计小组id")
    private Long accountingDeptId;

    @ApiModelProperty("会计小组名称")
    private String accountingDeptName;

    @ApiModelProperty("会计员工名称")
    private String accountingEmployeeNames;

    @ApiModelProperty("业务公司名称")
    private String businessDeptName;

    @ApiModelProperty("变更事项内容，待重派列表代表原因，更名待确认列表代表更名后的名称")
    private String itemContent;

    @ApiModelProperty("业务公司id")
    private Long businessDeptId;

    @ApiModelProperty("业务集团id")
    private Long businessTopDeptId;

    @ApiModelProperty("会计集团id")
    private Long accountingLeaderDeptId;

    @ApiModelProperty("会计区域id")
    private Long accountingTopDeptId;

    @ApiModelProperty("会计区域名称")
    private String accountingTopDeptName;
}
