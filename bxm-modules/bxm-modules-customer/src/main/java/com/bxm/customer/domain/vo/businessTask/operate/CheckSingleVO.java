package com.bxm.customer.domain.vo.businessTask.operate;

import com.bxm.common.core.web.domain.CommonFileVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/25 18:13
 * happy coding!
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CheckSingleVO {
    @ApiModelProperty("ids")
    @NotNull
    private Long id;

    @ApiModelProperty(value = "审核结果：1-通过、0-驳回")
    @NotNull
    private Integer checkResult;

    @ApiModelProperty("材料完整度，1-齐，2-缺，3-缺但齐")
    @NotNull(message = "材料完整度不能为空")
    private Integer materialIntegrity;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty("附件")
    private List<CommonFileVO> files;
}
