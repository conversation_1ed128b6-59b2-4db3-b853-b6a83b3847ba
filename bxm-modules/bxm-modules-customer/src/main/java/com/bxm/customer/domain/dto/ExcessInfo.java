package com.bxm.customer.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ExcessInfo {

    @ApiModelProperty("金额")
    private BigDecimal amount;

    @ApiModelProperty("预警类型，0-无预警，1-黄色预警，2-红色预警")
    private Integer warningLevel;
}
