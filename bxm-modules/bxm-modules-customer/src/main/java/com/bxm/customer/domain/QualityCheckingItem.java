package com.bxm.customer.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bxm.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.bxm.common.core.web.domain.BaseEntity;
import lombok.experimental.Accessors;

/**
 * 质检项目配置对象 c_quality_checking_item
 * 
 * <AUTHOR>
 * @date 2025-06-12
 */
@Data
@ApiModel("质检项目配置对象")
@Accessors(chain = true)
@TableName("c_quality_checking_item")
public class QualityCheckingItem extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键id */
    @TableId(value = "id", type = IdType.AUTO)
    @Excel(name = "主键id")
    @ApiModelProperty(value = "主键id")
    private Long id;

    /** 质检类型，1-账务问题，2-风险提示 */
    @Excel(name = "质检类型，1-账务问题，2-风险提示")
    @TableField("quality_checking_type")
    @ApiModelProperty(value = "质检类型，1-账务问题，2-风险提示")
    private Integer qualityCheckingType;

    /** 质检周期，1-单期，2-累计 */
    @Excel(name = "质检周期，1-单期，2-累计")
    @TableField("quality_checking_cycle")
    @ApiModelProperty(value = "质检周期，1-单期，2-累计")
    private Integer qualityCheckingCycle;

    /** 质检项目名称 */
    @Excel(name = "质检项目名称")
    @TableField("item_name")
    @ApiModelProperty(value = "质检项目名称")
    private String itemName;

    /** 检验标准 */
    @Excel(name = "检验标准")
    @TableField("checking_standard")
    @ApiModelProperty(value = "检验标准")
    private String checkingStandard;

    /** 事项说明 */
    @Excel(name = "事项说明")
    @TableField("item_remark")
    @ApiModelProperty(value = "事项说明")
    private String itemRemark;

    /** 风险级别 */
    @Excel(name = "风险级别")
    @TableField("risk_level")
    @ApiModelProperty(value = "风险级别")
    private String riskLevel;

    /** 启用状态，0-未启用，1-启用 */
    @Excel(name = "启用状态，0-未启用，1-启用")
    @TableField("status")
    @ApiModelProperty(value = "启用状态，0-未启用，1-启用")
    private Integer status;

    /** 是否rpa自动执行，0-否，1-是 */
    @Excel(name = "是否rpa自动执行，0-否，1-是")
    @TableField("is_rpa")
    @ApiModelProperty(value = "是否rpa自动执行，0-否，1-是")
    private Boolean isRpa;

}
