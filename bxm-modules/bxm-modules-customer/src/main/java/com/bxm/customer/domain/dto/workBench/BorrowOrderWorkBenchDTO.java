package com.bxm.customer.domain.dto.workBench;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
public class BorrowOrderWorkBenchDTO {

    @ApiModelProperty("待出站数量")
    private Long waitOutStationCount;

    @ApiModelProperty("待归还数量")
    private Long waitReturnCount;

    @ApiModelProperty("待验收数量")
    private Long waitCheckCount;

    public BorrowOrderWorkBenchDTO() {
        this.waitCheckCount = 0L;
        this.waitOutStationCount = 0L;
        this.waitReturnCount = 0L;
    }
}
