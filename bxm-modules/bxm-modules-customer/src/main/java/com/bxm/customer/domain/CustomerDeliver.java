package com.bxm.customer.domain;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bxm.common.core.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.bxm.common.core.web.domain.BaseEntity;
import lombok.experimental.Accessors;

/**
 * 交付对象 c_customer_deliver
 * 
 * <AUTHOR>
 * @date 2024-05-31
 */
@Data
@ApiModel("交付对象")
@Accessors(chain = true)
@TableName("c_customer_deliver")
public class CustomerDeliver extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** ID */
    @TableId(value = "id", type = IdType.AUTO)
    @Excel(name = "ID")
    @ApiModelProperty(value = "ID")
    private Long id;

    /** 服务id */
    @Excel(name = "服务id")
    @TableField("customer_service_id")
    @ApiModelProperty(value = "服务id")
    private Long customerServiceId;

    @ApiModelProperty("月度账期id")
    @TableField("customer_service_period_month_id")
    private Long customerServicePeriodMonthId;

    /** 交付单标题 */
    @Excel(name = "交付单标题")
    @TableField("title")
    @ApiModelProperty(value = "交付单标题")
    private String title;

    /** 批次号 */
    @Excel(name = "批次号")
    @TableField("batch_no")
    @ApiModelProperty(value = "批次号")
    private String batchNo;

    /** 提交员工id */
    @Excel(name = "提交员工id")
    @TableField("employee_id")
    @ApiModelProperty(value = "提交员工id")
    private Long employeeId;

    /** 提交人名称 */
    @Excel(name = "提交人名称")
    @TableField("employee_name")
    @ApiModelProperty(value = "提交人名称")
    private String employeeName;

    /** 交付类型，1-医保，2-社保，3-个税（工资薪金），4-国税，5-预认证 */
    @Excel(name = "交付类型，1-医保，2-社保，3-个税（工资薪金），4-国税，5-预认证")
    @TableField("deliver_type")
    @ApiModelProperty(value = "交付类型，1-医保，2-社保，3-个税（工资薪金），4-国税，5-预认证")
    private Integer deliverType;

    /** 交付账期 */
    @Excel(name = "交付账期")
    @TableField("period")
    @ApiModelProperty(value = "交付账期")
    private Integer period;

    /** 状态，0-已提交待申报，1-已保存待提交，2-已申报待确认，3-已确认待扣款，4-扣款已保存待提交，5-已扣款，6-待重提，7-申报异常，8-暂不申报，9-扣款异常，10-暂不扣款，101-已提交待补充，102-已补充待认证，103-已认证待确认，104-已确认，105-待重提，106-认证异常，107-暂不认证 */
    @Excel(name = "状态，0-已提交待申报，1-已保存待提交，2-已申报待确认，3-已确认待扣款，4-扣款已保存待提交，5-已扣款，6-待重提，7-申报异常，8-暂不申报，9-扣款异常，10-暂不扣款，101-已提交待补充，102-已补充待认证，103-已认证待确认，104-已确认，105-待重提，106-认证异常，107-暂不认证")
    @TableField("status")
    @ApiModelProperty(value = "状态，0-已提交待申报，1-已保存待提交，2-已申报待确认，3-已确认待扣款，4-扣款已保存待提交，5-已扣款，6-待重提，7-申报异常，8-暂不申报，9-扣款异常，10-暂不扣款，101-已提交待补充，102-已补充待认证，103-已认证待确认，104-已确认，105-待重提，106-认证异常，107-暂不认证")
    private Integer status;

    @TableField("is_tutor")
    @ApiModelProperty("是否辅导期，0-否，1-是，预认证类型才会有值")
    private Boolean isTutor;

    /** 是否有人员变动，0-否，1-是 */
    @Excel(name = "是否有人员变动，0-否，1-是")
    @TableField("has_person_change")
    @ApiModelProperty(value = "是否有人员变动，0-否，1-是")
    private Boolean hasPersonChange;

    /** 人员变动信息 */
    @Excel(name = "人员变动信息")
    @TableField("person_change_info")
    @ApiModelProperty(value = "人员变动信息")
    private String personChangeInfo;

    /** 普票金额 */
    @Excel(name = "普票金额")
    @TableField("normal_ticket_amount")
    @ApiModelProperty(value = "普票金额")
    private BigDecimal normalTicketAmount;

    /** 普票税额 */
    @Excel(name = "普票税额")
    @TableField("normal_ticket_tax_amount")
    @ApiModelProperty(value = "普票税额")
    private BigDecimal normalTicketTaxAmount;

    /** 专票金额 */
    @Excel(name = "专票金额")
    @TableField("special_ticket_amount")
    @ApiModelProperty(value = "专票金额")
    private BigDecimal specialTicketAmount;

    /** 专票税额 */
    @Excel(name = "专票税额")
    @TableField("special_ticket_tax_amount")
    @ApiModelProperty(value = "专票税额")
    private BigDecimal specialTicketTaxAmount;

    /** 无票收入 */
    @Excel(name = "无票收入")
    @TableField("no_ticket_amount")
    @ApiModelProperty(value = "无票收入")
    private BigDecimal noTicketAmount;

    /** 无票税额 */
    @Excel(name = "无票税额")
    @TableField("no_ticket_tax_amount")
    @ApiModelProperty(value = "无票税额")
    private BigDecimal noTicketTaxAmount;

    /** 简易收入 */
    @Excel(name = "简易收入")
    @TableField("simple_amount")
    @ApiModelProperty(value = "简易收入")
    private BigDecimal simpleAmount;

    /** 简易税额 */
    @Excel(name = "简易税额")
    @TableField("simple_tax_amount")
    @ApiModelProperty(value = "简易税额")
    private BigDecimal simpleTaxAmount;

    /** 进项税额 */
    @Excel(name = "进项税额")
    @TableField("income_tax_amount")
    @ApiModelProperty(value = "进项税额")
    private BigDecimal incomeTaxAmount;

    /** 上期留抵税额 */
    @Excel(name = "上期留抵税额")
    @TableField("last_month_purpose_tax_amount")
    @ApiModelProperty(value = "上期留抵税额")
    private BigDecimal lastMonthPurposeTaxAmount;

    /** 销项金额合计 */
    @Excel(name = "销项金额合计")
    @TableField("output_amount")
    @ApiModelProperty(value = "销项金额合计")
    private BigDecimal outputAmount;

    /** 销项税额合计 */
    @Excel(name = "销项税额合计")
    @TableField("output_tax_amount")
    @ApiModelProperty(value = "销项税额合计")
    private BigDecimal outputTaxAmount;

    /** 可抵扣税额 */
    @Excel(name = "可抵扣税额")
    @TableField("purpose_tax_amount")
    @ApiModelProperty(value = "可抵扣税额")
    private BigDecimal purposeTaxAmount;

    /** 本月应交税金 */
    @Excel(name = "本月应交税金")
    @TableField("this_month_tax_amount")
    @ApiModelProperty(value = "本月应交税金")
    private BigDecimal thisMonthTaxAmount;

    /** 本月留抵税额 */
    @Excel(name = "本月留抵税额")
    @TableField("this_month_purpose_tax_amount")
    @ApiModelProperty(value = "本月留抵税额")
    private BigDecimal thisMonthPurposeTaxAmount;

    /** 本月税负标准 */
    @Excel(name = "本月税负标准")
    @TableField("this_month_tax_burden")
    @ApiModelProperty(value = "本月税负标准")
    private BigDecimal thisMonthTaxBurden;

    @ApiModelProperty(value = "本月税负税额")
    @TableField("this_month_tax_burden_tax_amount")
    private BigDecimal thisMonthTaxBurdenTaxAmount;

    @ApiModelProperty(value = "本月税额差")
    @TableField("this_month_tax_burden_tax_amount_sub")
    private BigDecimal thisMonthTaxBurdenTaxAmountSub;

    /** 上年房租总额 */
    @Excel(name = "上年房租总额")
    @TableField("last_year_house_amount")
    @ApiModelProperty(value = "上年房租总额")
    private BigDecimal lastYearHouseAmount;

    /** 增值税 */
    @Excel(name = "增值税")
    @TableField("value_add_tax_amount")
    @ApiModelProperty(value = "增值税")
    private BigDecimal valueAddTaxAmount;

    /** 附加税 */
    @Excel(name = "附加税")
    @TableField("additional_tax_amount")
    @ApiModelProperty(value = "附加税")
    private BigDecimal additionalTaxAmount;

    /** 印花税 */
    @Excel(name = "印花税")
    @TableField("stamp_duty_tax_amount")
    @ApiModelProperty(value = "印花税")
    private BigDecimal stampDutyTaxAmount;

    /** 其他税 */
    @Excel(name = "其他税")
    @TableField("other_tax_amount")
    @ApiModelProperty(value = "其他税")
    private BigDecimal otherTaxAmount;

    /** 税款总额 */
    @Excel(name = "税款总额")
    @TableField("total_tax_amount")
    @ApiModelProperty(value = "税款总额")
    private BigDecimal totalTaxAmount;

    /** 申报结果，1-正常，2-异常 */
    @Excel(name = "申报结果，1-正常，2-异常")
    @TableField("report_status")
    @ApiModelProperty(value = "申报结果，1-正常，2-异常")
    private Integer reportStatus;

    /** 申报金额 */
    @Excel(name = "申报金额")
    @TableField("report_amount")
    @ApiModelProperty(value = "申报金额")
    private BigDecimal reportAmount;

    /** 本期金额 */
    @Excel(name = "本期金额")
    @TableField("current_period_amount")
    @ApiModelProperty(value = "本期金额")
    private BigDecimal currentPeriodAmount;

    /** 逾期金额 */
    @Excel(name = "逾期金额")
    @TableField("overdue_amount")
    @ApiModelProperty(value = "逾期金额")
    private BigDecimal overdueAmount;

    /** 补缴金额 */
    @Excel(name = "补缴金额")
    @TableField("supplement_amount")
    @ApiModelProperty(value = "补缴金额")
    private BigDecimal supplementAmount;

    /** 申报备注 */
    @Excel(name = "申报备注")
    @TableField("report_remark")
    @ApiModelProperty(value = "申报备注")
    private String reportRemark;

    /** 扣款结果，1-正常，2-异常 */
    @Excel(name = "扣款结果，1-正常，2-异常")
    @TableField("deduction_status")
    @ApiModelProperty(value = "扣款结果，1-正常，2-异常")
    private Integer deductionStatus;

    /** 扣款备注 */
    @Excel(name = "扣款备注")
    @TableField("deduction_remark")
    @ApiModelProperty(value = "扣款备注")
    private String deductionRemark;

    /** 异常处理结果，1-解除异常，2-关闭交付 */
    @Excel(name = "异常处理结果，1-解除异常，2-关闭交付")
    @TableField("exception_status")
    @ApiModelProperty(value = "异常处理结果，1-解除异常，2-关闭交付")
    private Integer exceptionStatus;

    /** 异常处理备注 */
    @Excel(name = "异常处理备注")
    @TableField("exception_remark")
    @ApiModelProperty(value = "异常处理备注")
    private String exceptionRemark;

    /** 新建备注 */
    @Excel(name = "新建备注")
    @TableField("create_remark")
    @ApiModelProperty(value = "新建备注")
    private String createRemark;

    /** 预认证结果，1-正常，2-异常 */
    @Excel(name = "预认证结果，1-正常，2-异常")
    @TableField("auth_status")
    @ApiModelProperty(value = "预认证结果，1-正常，2-异常")
    private Integer authStatus;

    /** 预认证备注 */
    @Excel(name = "预认证备注")
    @TableField("auth_remark")
    @ApiModelProperty(value = "预认证备注")
    private String authRemark;

    @TableField("pre_auth_info")
    @ApiModelProperty(value = "预认证数据")
    private String preAuthInfo;

    @ApiModelProperty(value = "预认证确认备注")
    @TableField("pre_auth_confirm_remark")
    private String preAuthConfirmRemark;

    @TableField("pre_auth_confirm_result")
    @ApiModelProperty(value = "预认证确认结果，1-确认，2-驳回")
    private Integer preAuthConfirmResult;

    @ApiModelProperty(value = "认证提醒")
    @TableField("pre_auth_remind")
    private String preAuthRemind;

    /** 无票收入 */
    @Excel(name = "无票收入")
    @TableField("pre_auth_no_ticket_income")
    @ApiModelProperty(value = "无票收入")
    private BigDecimal preAuthNoTicketIncome;

    /** 进项计税范围：1-全部待抵进项用于计税、2-仅已勾选进项用于计税 */
    @Excel(name = "进项计税范围：1-全部待抵进项用于计税、2-仅已勾选进项用于计税")
    @TableField("pre_auth_input_tax")
    @ApiModelProperty(value = "进项计税范围：1-全部待抵进项用于计税、2-仅已勾选进项用于计税")
    private Integer preAuthInputTax;

    @ApiModelProperty("是否删除，0-否，1-是")
    @TableField("is_del")
    private Boolean isDel;

    @ApiModelProperty("是否有交付变更，0-否，1-是")
    @TableField("has_changed")
    private Boolean hasChanged;

    @ApiModelProperty("是否需要重新确认，0-否，1-是")
    @TableField("is_repeat_confirm")
    private Boolean isRepeatConfirm;

    @ApiModelProperty("最后操作类型")
    @TableField("last_oper_type")
    private String lastOperType;

    @ApiModelProperty("最后操作人")
    @TableField("last_oper_name")
    private String lastOperName;

    @ApiModelProperty("最后操作时间")
    @TableField("last_oper_time")
    private LocalDateTime lastOperTime;

    @ApiModelProperty("最后操作备注")
    @TableField("last_oper_remark")
    private String lastOperRemark;

    @ApiModelProperty("联络员手机号")
    @TableField("liaison_phone")
    private String liaisonPhone;

    @ApiModelProperty("联络员证件号")
    @TableField("liaison_certificate_number")
    private String liaisonCertificateNumber;

    @ApiModelProperty("是否同步联络，0-否，1-是")
    @TableField("is_sync_liaison")
    private Integer isSyncLiaison;

    @ApiModelProperty("个税申报总额")
    @TableField("tax_report_total_amount")
    private BigDecimal taxReportTotalAmount;

    @ApiModelProperty("次报税种类型")
    @TableField("tax_check_type")
    private String taxCheckType;

    @ApiModelProperty("ddl")
    @TableField("ddl")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate ddl;

    @ApiModelProperty("是否关闭交付，0-否，1-是")
    @TableField("is_close")
    private Boolean isClose;
}
