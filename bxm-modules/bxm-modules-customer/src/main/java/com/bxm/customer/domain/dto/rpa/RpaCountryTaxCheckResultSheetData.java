package com.bxm.customer.domain.dto.rpa;

import com.bxm.common.core.annotation.Excel;

import java.util.List;

public class RpaCountryTaxCheckResultSheetData implements RpaEnterpriseData {

    @Excel(name = "序号")
    private String number;

    @Excel(name = "公司名称")
    private String enterpriseName;

    @Excel(name = "纳税识别号")
    private String creditCode;

    @Excel(name = "登入密码")
    private String password;

    @Excel(name = "实名人")
    private String realName;

    @Excel(name = "缴款金额")
    private String reportAmount;

    @Excel(name = "增值税")
    private String valueAddTaxAmount;

    @Excel(name = "印花税")
    private String stampTaxAmount;

    @Excel(name = "其他收入")
    private String otherIncome;

    @Excel(name = "文化事业建设税")
    private String cultureTaxAmount;

    @Excel(name = "企业所得税")
    private String incomeTaxAmount;

    @Excel(name = "非税收入通用申报")
    private String resourceTaxAmount;

    @Excel(name = "房产税")
    private String houseTaxAmount;

    @Excel(name = "财报")
    private String moneyReportAmount;

    @Excel(name = "环境税")
    private String landTaxAmount;

    @Excel(name = "资源税")
    private String waterTaxAmount;

    @Excel(name = "城镇土地使用税")
    private String unionFundsAmount;

    @Excel(name = "消费税")
    private String consumptionTaxAmount;

    @Excel(name = "残保金")
    private String canbaoAmount;

    @Excel(name = "年度财报")
    private String annualReport;

    @Excel(name = "所得税年报")
    private String incomeTaxReport;

    @Excel(name = "执行情况")
    private String reportResult;

    @Excel(name = "涉税机构纳税识别号")
    private String taxAgencyCreditCode;

    @Excel(name = "手机号")
    private String phone;

    @Excel(name = "报税截止日期")
    private String dealTime;

    @Excel(name = "异常信息")
    private String checkError;

    private List<AliFileDTO> files;

    private Long customerServiceId;

    private Long periodId;

    private Long deliverId;

    private String sheetIndex;

    @Override
    public String getSheetIndex() {
        return sheetIndex;
    }

    public void setSheetIndex(String sheetIndex) {
        this.sheetIndex = sheetIndex;
    }

    @Override
    public boolean hasErrors() {
        return checkError != null && !checkError.isEmpty();
    }

    @Override
    public void setCheckError(String checkError) {
        this.checkError = checkError;
    }

    @Override
    public void addCheckError(String checkError) {
        if (this.checkError == null || this.checkError.isEmpty()) {
            this.checkError = checkError;
        } else {
            this.checkError += "; " + checkError;
        }
    }

    @Override
    public List<AliFileDTO> getFiles() {
        return files;
    }

    public String getNumber() {
        return number;
    }

    public void setNumber(String number) {
        this.number = number;
    }

    public String getEnterpriseName() {
        return enterpriseName;
    }

    public void setEnterpriseName(String enterpriseName) {
        this.enterpriseName = enterpriseName;
    }

    public String getCreditCode() {
        return creditCode;
    }

    public void setCreditCode(String creditCode) {
        this.creditCode = creditCode;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getRealName() {
        return realName;
    }

    public void setRealName(String realName) {
        this.realName = realName;
    }

    public String getReportAmount() {
        return reportAmount;
    }

    public void setReportAmount(String reportAmount) {
        this.reportAmount = reportAmount;
    }

    public String getValueAddTaxAmount() {
        return valueAddTaxAmount;
    }

    public void setValueAddTaxAmount(String valueAddTaxAmount) {
        this.valueAddTaxAmount = valueAddTaxAmount;
    }

    public String getStampTaxAmount() {
        return stampTaxAmount;
    }

    public void setStampTaxAmount(String stampTaxAmount) {
        this.stampTaxAmount = stampTaxAmount;
    }

    public String getOtherIncome() {
        return otherIncome;
    }

    public void setOtherIncome(String otherIncome) {
        this.otherIncome = otherIncome;
    }

    public String getCultureTaxAmount() {
        return cultureTaxAmount;
    }

    public void setCultureTaxAmount(String cultureTaxAmount) {
        this.cultureTaxAmount = cultureTaxAmount;
    }

    public String getIncomeTaxAmount() {
        return incomeTaxAmount;
    }

    public void setIncomeTaxAmount(String incomeTaxAmount) {
        this.incomeTaxAmount = incomeTaxAmount;
    }

    public String getResourceTaxAmount() {
        return resourceTaxAmount;
    }

    public void setResourceTaxAmount(String resourceTaxAmount) {
        this.resourceTaxAmount = resourceTaxAmount;
    }

    public String getHouseTaxAmount() {
        return houseTaxAmount;
    }

    public void setHouseTaxAmount(String houseTaxAmount) {
        this.houseTaxAmount = houseTaxAmount;
    }

    public String getMoneyReportAmount() {
        return moneyReportAmount;
    }

    public void setMoneyReportAmount(String moneyReportAmount) {
        this.moneyReportAmount = moneyReportAmount;
    }

    public String getLandTaxAmount() {
        return landTaxAmount;
    }

    public void setLandTaxAmount(String landTaxAmount) {
        this.landTaxAmount = landTaxAmount;
    }

    public String getWaterTaxAmount() {
        return waterTaxAmount;
    }

    public void setWaterTaxAmount(String waterTaxAmount) {
        this.waterTaxAmount = waterTaxAmount;
    }

    public String getUnionFundsAmount() {
        return unionFundsAmount;
    }

    public void setUnionFundsAmount(String unionFundsAmount) {
        this.unionFundsAmount = unionFundsAmount;
    }

    public String getConsumptionTaxAmount() {
        return consumptionTaxAmount;
    }

    public void setConsumptionTaxAmount(String consumptionTaxAmount) {
        this.consumptionTaxAmount = consumptionTaxAmount;
    }

    public String getCanbaoAmount() {
        return canbaoAmount;
    }

    public void setCanbaoAmount(String canbaoAmount) {
        this.canbaoAmount = canbaoAmount;
    }

    public String getAnnualReport() {
        return annualReport;
    }

    public void setAnnualReport(String annualReport) {
        this.annualReport = annualReport;
    }

    public String getIncomeTaxReport() {
        return incomeTaxReport;
    }

    public void setIncomeTaxReport(String incomeTaxReport) {
        this.incomeTaxReport = incomeTaxReport;
    }

    public String getReportResult() {
        return reportResult;
    }

    public void setReportResult(String reportResult) {
        this.reportResult = reportResult;
    }

    @Override
    public String getCheckError() {
        return checkError;
    }

    public void setFiles(List<AliFileDTO> files) {
        this.files = files;
    }

    public Long getCustomerServiceId() {
        return customerServiceId;
    }

    public void setCustomerServiceId(Long customerServiceId) {
        this.customerServiceId = customerServiceId;
    }

    public Long getPeriodId() {
        return periodId;
    }

    public void setPeriodId(Long periodId) {
        this.periodId = periodId;
    }

    public Long getDeliverId() {
        return deliverId;
    }

    public void setDeliverId(Long deliverId) {
        this.deliverId = deliverId;
    }

    public String getTaxAgencyCreditCode() {
        return taxAgencyCreditCode;
    }

    public void setTaxAgencyCreditCode(String taxAgencyCreditCode) {
        this.taxAgencyCreditCode = taxAgencyCreditCode;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getDealTime() {
        return dealTime;
    }

    public void setDealTime(String dealTime) {
        this.dealTime = dealTime;
    }
}
