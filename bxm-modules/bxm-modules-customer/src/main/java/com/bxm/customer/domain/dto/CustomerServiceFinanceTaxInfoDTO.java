package com.bxm.customer.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CustomerServiceFinanceTaxInfoDTO
{

    @ApiModelProperty(value = "客户服务ID")
    private Long customerServiceId;

    /** 个税申报方式, 1-易捷账, 2-扣缴端 */
    @ApiModelProperty(value = "个税申报方式, 1-易捷账, 2-扣缴端")
    private Integer taxMethod;

    /** 易捷账个税账号, 0-未维护, 1-已维护 */
    @ApiModelProperty(value = "易捷账个税账号, false-未维护, true-已维护")
    private Boolean ezTaxAccount;

    /** 税盘, 1-黑盘, 2-白盘, 3-U-key, 4-无 */
    @ApiModelProperty(value = "税盘, 1-黑盘, 2-白盘, 3-U-key, 4-无")
    private Integer taxDisk;

    /** 税盘密码 */
    @ApiModelProperty(value = "税盘密码")
    private String taxPassword;


}
