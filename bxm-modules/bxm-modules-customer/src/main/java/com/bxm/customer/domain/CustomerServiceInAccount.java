package com.bxm.customer.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bxm.common.core.annotation.Excel;
import com.bxm.common.core.web.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 入账、入账交付对象 c_customer_service_in_account
 *
 * <AUTHOR>
 * @date 2024-07-11
 */
@Data
@ApiModel("入账、入账交付对象")
@Accessors(chain = true)
@TableName("c_customer_service_in_account")
public class CustomerServiceInAccount extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @TableId(value = "id", type = IdType.AUTO)
    @Excel(name = "主键ID")
    @ApiModelProperty(value = "主键ID")
    private Long id;

    /** 是否删除，0-否，1-是 */
    @Excel(name = "是否删除，0-否，1-是")
    @TableField("is_del")
    @ApiModelProperty(value = "是否删除，0-否，1-是")
    private Boolean isDel;

    /** 月度账期id */
    @Excel(name = "月度账期id")
    @TableField("customer_service_period_month_id")
    @ApiModelProperty(value = "月度账期id")
    private Long customerServicePeriodMonthId;

    /** 账期 */
    @Excel(name = "账期")
    @TableField("period")
    @ApiModelProperty(value = "账期")
    private Integer period;

    /** 客户服务ID */
    @Excel(name = "客户服务ID")
    @TableField("customer_service_id")
    @ApiModelProperty(value = "客户服务ID")
    private Long customerServiceId;

    /** 服务类型 */
    @Excel(name = "服务类型")
    @TableField("service_type")
    @ApiModelProperty(value = "服务类型")
    private Integer serviceType;

    /** 客户名 */
    @Excel(name = "客户名")
    @TableField("customer_name")
    @ApiModelProperty(value = "客户名")
    private String customerName;

    /** 信用代码 */
    @Excel(name = "信用代码")
    @TableField("credit_code")
    @ApiModelProperty(value = "信用代码")
    private String creditCode;

    /** 服务编号、档案编码、客户编码 */
    @Excel(name = "服务编号、档案编码、客户编码")
    @TableField("service_number")
    @ApiModelProperty(value = "服务编号、档案编码、客户编码")
    private String serviceNumber;

    /** 纳税人性质，1-小规模，2-一般纳税人 */
    @Excel(name = "纳税人性质，1-小规模，2-一般纳税人")
    @TableField("tax_type")
    @ApiModelProperty(value = "纳税人性质，1-小规模，2-一般纳税人")
    private Integer taxType;

    /** 标题 */
    @Excel(name = "标题")
    @TableField("title")
    @ApiModelProperty(value = "标题")
    private String title;

    /** 批次序号 */
    @Excel(name = "批次序号")
    @TableField("batch_num")
    @ApiModelProperty(value = "批次序号")
    private Integer batchNum;

    /** 状态：1未入账、2已入账未结账、3已入账已结账 */
    @Excel(name = "状态：1未入账、2已入账未结账、3已入账已结账")
    @TableField("status")
    @ApiModelProperty(value = "状态：1未入账、2已入账未结账、3已入账已结账")
    private Integer status;

    /** 交付结果：1-正常、2-无需交付 */
    @Excel(name = "交付结果：1-正常、2-无需交付")
    @TableField("deliver_result")
    @ApiModelProperty(value = "交付结果：1-正常、2-无需交付")
    private Integer deliverResult;

    /** 主营收入累计 */
    @Excel(name = "主营收入累计")
    @TableField("major_income_total")
    @ApiModelProperty(value = "主营收入累计")
    private BigDecimal majorIncomeTotal;

    /** 主营成本累计 */
    @Excel(name = "主营成本累计")
    @TableField("major_cost_total")
    @ApiModelProperty(value = "主营成本累计")
    private BigDecimal majorCostTotal;

    /** 利润总计 */
    @Excel(name = "利润总计")
    @TableField("profit_total")
    @ApiModelProperty(value = "利润总计")
    private BigDecimal profitTotal;

    /** 个税申报人数 */
    @Excel(name = "个税申报人数")
    @TableField("tax_report_count")
    @ApiModelProperty(value = "个税申报人数")
    private Integer taxReportCount;

    /** 本年个税申报工资总额 */
    @Excel(name = "本年个税申报工资总额")
    @TableField("tax_report_salary_total")
    @ApiModelProperty(value = "本年个税申报工资总额")
    private BigDecimal taxReportSalaryTotal;

    /** 备注 */
    @Excel(name = "备注")
    @TableField("remark")
    @ApiModelProperty(value = "备注")
    private String remark;

    /** 银行流水录入日期 */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @Excel(name = "银行流水录入日期", width = 30, dateFormat = "yyyy-MM-dd")
    @TableField("bank_payment_input_time")
    @ApiModelProperty(value = "银行流水录入日期")
    private LocalDate bankPaymentInputTime;

    @TableField(value = "bank_payment_input_result")
    @ApiModelProperty(value = "银行流水录入结果，1-正常2-异常3-无需交付4-已开户无流水5-未开户6-银行部分缺")
    private Integer bankPaymentInputResult;

    @TableField(value = "in_account_result")
    @ApiModelProperty(value = "入账结果，1-正常2-异常3-无需交付4-无账务")
    private Integer inAccountResult;

    /** 入账时间 */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @Excel(name = "入账时间", width = 30, dateFormat = "yyyy-MM-dd")
    @TableField("in_time")
    @ApiModelProperty(value = "入账时间")
    private LocalDate inTime;

    /** 入账员工部门ID */
    @Excel(name = "入账员工部门ID")
    @TableField("in_employee_dept_id")
    @ApiModelProperty(value = "入账员工部门ID")
    private Long inEmployeeDeptId;

    /** 入账员工部门名称 */
    @Excel(name = "入账员工部门名称")
    @TableField("in_employee_dept_name")
    @ApiModelProperty(value = "入账员工部门名称")
    private String inEmployeeDeptName;

    /** 入账员工id */
    @Excel(name = "入账员工id")
    @TableField("in_employee_id")
    @ApiModelProperty(value = "入账员工id")
    private Long inEmployeeId;

    /** 入账员工名称 */
    @Excel(name = "入账员工名称")
    @TableField("in_employee_name")
    @ApiModelProperty(value = "入账员工名称")
    private String inEmployeeName;

    /** 结账时间 */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @Excel(name = "结账时间", width = 30, dateFormat = "yyyy-MM-dd")
    @TableField("end_time")
    @ApiModelProperty(value = "结账时间")
    private LocalDate endTime;

    /** 结账员工部门ID */
    @Excel(name = "结账员工部门ID")
    @TableField("end_employee_dept_id")
    @ApiModelProperty(value = "结账员工部门ID")
    private Long endEmployeeDeptId;

    /** 结账员工部门名称 */
    @Excel(name = "结账员工部门名称")
    @TableField("end_employee_dept_name")
    @ApiModelProperty(value = "结账员工部门名称")
    private String endEmployeeDeptName;

    /** 结账员工id */
    @Excel(name = "结账员工id")
    @TableField("end_employee_id")
    @ApiModelProperty(value = "结账员工id")
    private Long endEmployeeId;

    /** 结账员工名称 */
    @Excel(name = "结账员工名称")
    @TableField("end_employee_name")
    @ApiModelProperty(value = "结账员工名称")
    private String endEmployeeName;

    /** RPA执行结果：1-成功、0-失败 */
    @Excel(name = "RPA执行结果：1-成功、0-失败")
    @TableField("rpa_exe_result")
    @ApiModelProperty(value = "RPA执行结果：1-成功、0-失败")
    private Integer rpaExeResult;

    /** 报表状态是否平衡 */
    @Excel(name = "报表状态是否平衡")
    @TableField("table_status_balance")
    @ApiModelProperty(value = "报表状态是否平衡")
    private String tableStatusBalance;

    /** RPA查询时间 */
    @Excel(name = "RPA查询时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("rpa_search_time")
    @ApiModelProperty("RPA查询时间")
    private LocalDateTime rpaSearchTime;

    /** RPA备注 */
    @Excel(name = "RPA备注")
    @TableField("rpa_remark")
    @ApiModelProperty(value = "RPA备注")
    private String rpaRemark;

    @TableField(value = "first_bank_payment_input_time")
    @ApiModelProperty(value = "首次银行流水交付时间")
    private LocalDate firstBankPaymentInputTime;

    @TableField(value = "last_bank_payment_input_time")
    @ApiModelProperty(value = "末次银行流水交付时间")
    private LocalDate lastBankPaymentInputTime;

    @TableField(value = "first_bank_payment_input_result")
    @ApiModelProperty(value = "首次银行流水交付结果")
    private Integer firstBankPaymentInputResult;

    @TableField(value = "last_bank_payment_input_result")
    @ApiModelProperty(value = "末次银行流水交付结果")
    private Integer lastBankPaymentInputResult;

    @TableField(value = "first_in_account_time")
    @ApiModelProperty(value = "首次入账交付时间")
    private LocalDate firstInAccountTime;

    @TableField(value = "last_in_account_time")
    @ApiModelProperty(value = "末次入账交付时间")
    private LocalDate lastInAccountTime;

    @TableField(value = "first_in_account_result")
    @ApiModelProperty(value = "首次入账交付结果")
    private Integer firstInAccountResult;

    @TableField(value = "last_in_account_result")
    @ApiModelProperty(value = "末次入账交付结果")
    private Integer lastInAccountResult;
}
