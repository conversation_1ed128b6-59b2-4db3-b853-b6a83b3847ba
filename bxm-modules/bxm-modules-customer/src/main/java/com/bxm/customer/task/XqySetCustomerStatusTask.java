package com.bxm.customer.task;

import com.bxm.common.core.utils.DateUtils;
import com.bxm.common.core.utils.StringUtils;
import com.bxm.customer.service.ICCustomerServiceService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

@Component
@Slf4j
public class XqySetCustomerStatusTask {

    @Autowired
    private ICCustomerServiceService customerServiceService;

    @XxlJob("xqySetCustomerStatusTask")
    public ReturnT<String> xqySetCustomerStatusTask() {
        log.info("每月鑫启易推送客户状态任务开始=============");
        String jobParam = XxlJobHelper.getJobParam();
        LocalDate date = StringUtils.isEmpty(jobParam) ? LocalDate.now().minusMonths(2) : LocalDate.parse(jobParam + "-01", DateTimeFormatter.ofPattern(DateUtils.YYYY_MM_DD));
        customerServiceService.xqySetCustomerStatus(date);
        log.info("每月鑫启易推送客户状态任务结束=============");
        return ReturnT.SUCCESS;
    }
}
