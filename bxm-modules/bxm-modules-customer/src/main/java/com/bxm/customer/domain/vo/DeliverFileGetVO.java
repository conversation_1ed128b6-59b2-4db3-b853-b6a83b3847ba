package com.bxm.customer.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DeliverFileGetVO {

    @ApiModelProperty("交付单id")
    private Long deliverId;

    @ApiModelProperty("附件类型，可多选，不传代表所有类型，1-人员变动附件，2-申报附件，3-扣款附件，4-异常处理附件，5-新建预认证附件，6-预认证认证附件，7-新建国税附件")
    private List<Integer> fileTypes;
}
