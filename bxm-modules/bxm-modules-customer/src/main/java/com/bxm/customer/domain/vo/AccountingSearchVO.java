package com.bxm.customer.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/7/5 16:58
 * happy coding!
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AccountingSearchVO {
    @ApiModelProperty("是否需要搜索")
    private Boolean needSearch;

    @ApiModelProperty("是否直接返回")
    private Boolean fail;

    @ApiModelProperty("搜索后对应的主体ID列表")
    private List<Long> ids;
}
