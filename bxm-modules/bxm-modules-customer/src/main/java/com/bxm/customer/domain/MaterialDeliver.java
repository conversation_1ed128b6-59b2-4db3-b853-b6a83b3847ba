package com.bxm.customer.domain;

import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bxm.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.bxm.common.core.web.domain.BaseEntity;
import lombok.experimental.Accessors;

/**
 * 材料交接单对象 c_material_deliver
 * 
 * <AUTHOR>
 * @date 2024-12-08
 */
@Data
@ApiModel("材料交接单对象")
@Accessors(chain = true)
@TableName("c_material_deliver")
public class MaterialDeliver extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键id */
    @TableId(value = "id", type = IdType.AUTO)
    @Excel(name = "主键id")
    @ApiModelProperty(value = "主键id")
    private Long id;

    /** 标题 */
    @Excel(name = "标题")
    @TableField("title")
    @ApiModelProperty(value = "标题")
    private String title;

    /** 交接单编号 */
    @Excel(name = "交接单编号")
    @TableField("material_deliver_number")
    @ApiModelProperty(value = "交接单编号")
    private String materialDeliverNumber;

    /** 交接单类型，1-银行流水，2-普通入账，3-凭票入账 */
    @Excel(name = "交接单类型，1-银行流水，2-普通入账，3-凭票入账")
    @TableField("material_deliver_type")
    @ApiModelProperty(value = "交接单类型，1-银行流水，2-普通入账，3-凭票入账")
    private Integer materialDeliverType;

    /** 解析状态，0-队列中，1-解析中，2-解析完成，3-解析失败，4-解析中止 */
    @Excel(name = "解析状态，0-队列中，1-解析中，2-解析完成，3-解析失败，4-解析中止")
    @TableField("analysis_status")
    @ApiModelProperty(value = "解析状态，0-队列中，1-解析中，2-解析完成，3-解析失败，4-解析中止")
    private Integer analysisStatus;

    /** 解析结果，1-正常，2-异常 */
    @Excel(name = "解析结果，1-正常，2-异常")
    @TableField("analysis_result")
    @ApiModelProperty(value = "解析结果，1-正常，2-异常")
    private Integer analysisResult;

    @ApiModelProperty(value = "解析任务id")
    @TableField("analysis_task_id")
    @Excel(name = "解析任务id")
    private String analysisTaskId;

    /** 推送状态，1-待推送，2-已推送 */
    @Excel(name = "推送状态，1-待推送，2-已推送")
    @TableField("push_status")
    @ApiModelProperty(value = "推送状态，1-待推送，2-已推送")
    private Integer pushStatus;

    /** 提交小组id */
    @Excel(name = "提交小组id")
    @TableField("commit_dept_id")
    @ApiModelProperty(value = "提交小组id")
    private Long commitDeptId;

    /** 提交用户id */
    @Excel(name = "提交用户id")
    @TableField("commit_user_id")
    @ApiModelProperty(value = "提交用户id")
    private Long commitUserId;

    /** 提交用户昵称 */
    @Excel(name = "提交用户昵称")
    @TableField("commit_user_nick_name")
    @ApiModelProperty(value = "提交用户昵称")
    private String commitUserNickName;

    /** 最后一次操作记录类型 */
    @Excel(name = "最后一次操作记录类型")
    @TableField("last_oper_type")
    @ApiModelProperty(value = "最后一次操作记录类型")
    private String lastOperType;

    /** 最后一次操作人员 */
    @Excel(name = "最后一次操作人员")
    @TableField("last_oper_name")
    @ApiModelProperty(value = "最后一次操作人员")
    private String lastOperName;

    /** 最后一次操作时间 */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "最后一次操作时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @TableField("last_oper_time")
    @ApiModelProperty(value = "最后一次操作时间")
    private LocalDateTime lastOperTime;

    @ApiModelProperty(value = "是否删除，0-否，1-是")
    @TableField("is_del")
    private Boolean isDel;
}
