package com.bxm.customer.domain.dto.workOrder;

import com.bxm.customer.domain.dto.BusinessDeptDTO;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
public class InitiateUndertakeDeptDTO {

    private List<BusinessDeptDTO> initiateDeptList;

    private List<BusinessDeptDTO> undertakeDeptList;

    public InitiateUndertakeDeptDTO() {
        this.initiateDeptList = Lists.newArrayList();
        this.undertakeDeptList = Lists.newArrayList();
    }
}
