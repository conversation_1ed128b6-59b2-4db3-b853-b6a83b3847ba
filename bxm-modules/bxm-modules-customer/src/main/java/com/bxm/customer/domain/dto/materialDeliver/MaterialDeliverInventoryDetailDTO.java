package com.bxm.customer.domain.dto.materialDeliver;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MaterialDeliverInventoryDetailDTO {

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("文件名称")
    private String fileName;

    @ApiModelProperty("文件短链接")
    private String fileUrl;

    @ApiModelProperty("文件大小")
    private Long fileSize;

    @ApiModelProperty("解析结果，1-正常，2-异常")
    private Integer analysisResult;

    @ApiModelProperty("解析结果(文案)")
    private String analysisResultStr;

    @ApiModelProperty("异常原因")
    private String errorMsg;

    @ApiModelProperty("是否疑似重复，0-否，1-是")
    private Integer isRepeat;

    @ApiModelProperty("是否客户名不一致，0-否，1-是")
    private Integer isCustomerNameNotSame;

    @ApiModelProperty("归属客户")
    private String customerName;

    @ApiModelProperty("客户名称")
    private String customerServiceName;

    @ApiModelProperty("客户id")
    private Long customerServiceId;

    @ApiModelProperty("银行名称")
    private String bankName;

    @ApiModelProperty("银行账号")
    private String bankAccountNumber;

    @ApiModelProperty("银行信息")
    private String bankInfo;

    @ApiModelProperty("文件备注")
    private String fileRemark;

    @ApiModelProperty("开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate startDate;

    @ApiModelProperty("结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate endDate;

    @ApiModelProperty("文件序号")
    private Integer fileNumber;

    @ApiModelProperty("账期")
    private Integer period;
}
