package com.bxm.customer.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
public class CustomerServiceDeliverCountDTO {

    @ApiModelProperty("应报户数")
    private Long shouldCreateCount;

    @ApiModelProperty("待创建数量")
    private Long waitCreateCount;

    @ApiModelProperty("待申报数量")
    private Long waitReportCount;

    @ApiModelProperty("申报待提交数量")
    private Long waitReportSubmitCount;

    @ApiModelProperty("待确认数量")
    private Long waitConfirmCount;

    @ApiModelProperty("待扣款数量")
    private Long waitDeductionCount;

    @ApiModelProperty("扣款待提交数量")
    private Long waitDeductionSubmitCount;

    @ApiModelProperty("待重提数量")
    private Long waitRepeatSubmitCount;

    @ApiModelProperty("申报异常数量")
    private Long reportExceptionCount;

    @ApiModelProperty("扣款异常数量")
    private Long deductionExceptionCount;

    @ApiModelProperty("认证异常数量")
    private Long authExceptionCount;

    @ApiModelProperty("冻结待交付数量")
    private Long waitFrozeDeliverCount;

    @ApiModelProperty("待反馈数量")
    private Long waitFeedBackCount;

    @ApiModelProperty("待认证数量")
    private Long waitAuthCount;

    @ApiModelProperty("认证待提交数量")
    private Long waitAuthSubmitCount;

    @ApiModelProperty("待完结数量")
    private Long waitOverCount;

    @ApiModelProperty("完结异常数量")
    private Long overExceptionCount;

    @ApiModelProperty("交付待变更数量")
    private Long hasChangedCount;

    @ApiModelProperty("往期未完成数量")
    private Long overdueNotCompleteCount;

    @ApiModelProperty("往期待交付数量")
    private Long overdueWaitDeliverCount;

    @ApiModelProperty("待申报（按税种）数量")
    private Long waitReportDeliverCount;

    @ApiModelProperty("待扣款（按税种）数量")
    private Long waitDeductionDeliverCount;

    public CustomerServiceDeliverCountDTO() {
        this.waitCreateCount = 0L;
        this.waitReportCount = 0L;
        this.waitReportSubmitCount = 0L;
        this.waitConfirmCount = 0L;
        this.waitDeductionCount = 0L;
        this.waitDeductionSubmitCount = 0L;
        this.waitRepeatSubmitCount = 0L;
        this.reportExceptionCount = 0L;
        this.deductionExceptionCount = 0L;
        this.waitFrozeDeliverCount = 0L;
        this.waitFeedBackCount = 0L;
        this.waitAuthCount = 0L;
        this.authExceptionCount = 0L;
        this.hasChangedCount = 0L;
        this.waitOverCount = 0L;
        this.overExceptionCount = 0L;
        this.overdueNotCompleteCount = 0L;
        this.overdueWaitDeliverCount = 0L;
        this.waitAuthSubmitCount = 0L;
        this.waitReportDeliverCount = 0L;
        this.waitDeductionDeliverCount = 0L;
    }
}
