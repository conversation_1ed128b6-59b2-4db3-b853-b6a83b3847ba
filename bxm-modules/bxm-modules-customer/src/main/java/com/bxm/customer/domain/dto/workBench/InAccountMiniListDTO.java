package com.bxm.customer.domain.dto.workBench;

import com.bxm.common.core.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class InAccountMiniListDTO {

    @ApiModelProperty("客户id")
    private Long customerServiceId;

    @ApiModelProperty("账期id")
    private Long customerServicePeriodMonthId;

    @ApiModelProperty("客户名")
    @Excel(name = "客户名")
    private String customerName;

    @ApiModelProperty("账期")
    @Excel(name = "账期")
    private Integer period;

    @ApiModelProperty("银行流水结果，0-未开户，1-待创建，2-银行部分缺，3-异常，4-交付中，5-无需交付，6-无流水，7-正常完成")
    private Integer bankPaymentResult;

    @ApiModelProperty("银行流水结果")
    @Excel(name = "银行流水事项结果")
    private String bankPaymentResultStr;

    @ApiModelProperty("入账交付状态，0-待创建，1-待交付，2-交付完成，3-交付异常，4-待重提")
    private Integer inAccountResult;

    @ApiModelProperty("入账交付状态")
    @Excel(name = "入账事项结果")
    private String inAccountResultStr;

    @ApiModelProperty("结账事项结果，1未入账、2已入账未结账、3已入账已结账")
    private Integer settleAccountResult;

    @ApiModelProperty("结账事项结果")
    @Excel(name = "结账事项结果")
    private String settleAccountResultStr;
}
