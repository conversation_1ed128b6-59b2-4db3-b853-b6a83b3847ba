//package com.bxm.customer.config;
//
//import com.alibaba.otter.canal.client.CanalConnector;
//import com.alibaba.otter.canal.client.CanalConnectors;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//
//import java.net.InetSocketAddress;
//
//@Configuration
//public class CanalConfig {
//
//    @Bean
//    public CanalConnector canalConnector() {
//        // 创建 Canal 连接器，连接到 Canal Server
//        return CanalConnectors.newSingleConnector(
//                new InetSocketAddress("*************", 11111), // Canal Server 地址和端口
//                "example", // Canal instance 名称
//                "", // 用户名（如果有）
//                ""  // 密码（如果有）
//        );
//    }
//}
