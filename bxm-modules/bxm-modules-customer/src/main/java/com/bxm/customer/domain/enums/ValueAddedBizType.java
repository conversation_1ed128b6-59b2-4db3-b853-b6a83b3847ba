package com.bxm.customer.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 增值员工业务类型枚举
 *
 * <AUTHOR>
 * @date 2025-01-27
 */
@Getter
@AllArgsConstructor
public enum ValueAddedBizType {

    /**
     * 社医保
     */
    SOCIAL_INSURANCE(1, "社医保"),

    /**
     * 个税明细
     */
    PERSONAL_TAX(2, "个税明细"),

    /**
     * 国税账号
     */
    NATIONAL_TAX_ACCOUNT(3, "国税账号");

    private final Integer code;
    private final String name;

    /**
     * 根据代码获取枚举
     *
     * @param code 业务类型代码
     * @return 对应的枚举值
     */
    public static ValueAddedBizType getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (ValueAddedBizType bizType : values()) {
            if (bizType.getCode().equals(code)) {
                return bizType;
            }
        }
        return null;
    }

    /**
     * 验证业务类型代码是否有效
     *
     * @param code 业务类型代码
     * @return 是否有效
     */
    public static boolean isValid(Integer code) {
        return getByCode(code) != null;
    }
}
