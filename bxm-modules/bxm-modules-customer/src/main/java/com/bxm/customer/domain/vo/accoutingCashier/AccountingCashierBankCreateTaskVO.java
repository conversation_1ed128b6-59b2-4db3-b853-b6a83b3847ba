package com.bxm.customer.domain.vo.accoutingCashier;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AccountingCashierBankCreateTaskVO {

    @ApiModelProperty("选中的银行流水交付单id列表，批量操作")
    private List<Long> ids;

    @ApiModelProperty("银行流水交付单id，单个操作")
    private Long id;

    @ApiModelProperty("DDL， yyyy-MM")
    private String ddl;

    @ApiModelProperty("监管人用户id")
    private Long adminUserId;

    @ApiModelProperty(hidden = true)
    private Long userId;

    @ApiModelProperty(hidden = true)
    private Long deptId;

    @ApiModelProperty(hidden = true)
    private String operName;
}
