package com.bxm.customer.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CustomerServiceStatisticDTO {

    @ApiModelProperty("部门名称")
    private String deptName;

    @ApiModelProperty("有效服务数量")
    private Long validServiceCount;

    // 冻结数量
    @ApiModelProperty("冻结服务数量")
    private Long frozenServiceCount;

    @ApiModelProperty("本月结束服务数量")
    private Long thisMonthEndServiceCount;

    // 待重派数量
    @ApiModelProperty("待重派服务数量")
    private Long toBeReassignedServiceCount;

    @ApiModelProperty("更名待确认数量")
    private Long waitChangeNameServiceCount;
}
