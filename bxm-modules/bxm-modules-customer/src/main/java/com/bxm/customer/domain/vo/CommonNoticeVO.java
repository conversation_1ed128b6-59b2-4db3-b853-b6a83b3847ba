package com.bxm.customer.domain.vo;

import com.bxm.customer.domain.vo.xqy.XqyFileVO;
import com.bxm.thirdpart.api.domain.ReportTableDownloadResultDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CommonNoticeVO {

    private String taxNumber;

    private String customerName;

    private String creditCode;

    private String operator;

    private Integer period;

    private String noticeCode;

    private Map<String, Object> noticeParameter;

    private String sourceName;

    private Integer source;

    private String platType;

    private String groupId;

    private String groupName;

    private Integer operType;

    private Long customerServiceId;

    private Long deliverId;

    private Long businessTaskId;

    private Long userId;

    private Long deptId;

    private String uuid;

    private Boolean success;

    private String message;

    private Integer count;

    private Boolean statementFileExist;

    private Integer bankReceiptFileExist;

    private Integer bankReceiptFileCount;

    private Integer bankReceiptSuccessCount;

    private Integer bankReceiptFailCount;

    private Integer bankReceiptFileCheckStatus;

    private Integer checkType;

    private Long taskId;

    private String bankNumber;

    private Boolean incomeResult;

    private Boolean outputResult;

    private String incomeMessage;

    private String outputMessage;

    private Integer successCount;

    private Integer failCount;

    private String outputFileLink;

    private String incomeFileLink;

    private Long customerServicePeriodMonthId;

    private Integer type;

    private String declareStatus;

    private String payStatus;

    private String checkStatus;

    private String income;

    private Integer declareEmployeeCount;

    private String taxOwnedOrOverpaid;

    private String note;

    private List<CommonNoticeFileVO> payImgs;

    private List<CommonNoticeFileVO> checkImgs;

    private List<CommonNoticeFileVO> declareImgs;

    private String fileId;

    private String fileName;

    private String officalFilename;

    private String declareTotalIncome;

    private String downloadId;

    private List<ReportTableDownloadResultDTO> list;

    private String rpaTaskId;

    private Integer voucherGenerationStatus;

    private String accountantId;

    private String clientVersion;

    private String customerUuid;

    private ReportDeductionResultVO params;

    private String requestId;

    private String taskType;

    private String tfn;

    private Long syncRecordId;

    private Long qualityCheckingResultId;

    private Long qualityCheckingRecordId;
}
