package com.bxm.customer.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.bxm.common.core.web.domain.CommonFileVO;
import com.bxm.customer.domain.dto.PreAuthInfoDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CustomerDeliverVO {

    @ApiModelProperty("交付单id，编辑时传入")
    private Long id;

    @ApiModelProperty("服务id")
    private Long customerServiceId;

    @ApiModelProperty("交付类型，2-医社保，3-个税（工资薪金），4-国税，5-预认证，6-个税（经营所得）")
    private Integer deliverType;

    @ApiModelProperty("子交付类型，1-医保，2-社保")
    private List<Integer> subDeliverTypes;

    @ApiModelProperty("账期id")
    private Long customerServicePeriodMonthId;

    @ApiModelProperty("是否有人员变动，0-否，1-是（编辑时传的字段）")
    private Integer hasPersonChange;

    @ApiModelProperty("个税（工资薪金）/个税（经营所得）是否有人员变动，0-否，1-是")
    private Integer personTaxHasPersonChange;

    @ApiModelProperty("医保是否有人员变动，0-否，1-是")
    private Integer medicalHasPersonChange;

    @ApiModelProperty("社保是否有人员变动，0-否，1-是")
    private Integer socialSecurityHasPersonChange;

    @ApiModelProperty("医保/个税（工资薪金）/个税（经营所得）人员变动信息,新增时需要区分，编辑时统一传这个字段")
    private String personChangeInfo;

    @ApiModelProperty("医保/个税（工资薪金）/个税（经营所得）人员变动相关附件,新增时需要区分，编辑时统一传这个字段")
    private List<CommonFileVO> personChangeFiles;

    @ApiModelProperty("社保人员变动信息")
    private String socialPersonChangeInfo;

    @ApiModelProperty("社保人员变动相关附件")
    private List<CommonFileVO> socialPersonChangeFiles;

    @ApiModelProperty("是否辅导期，0-否，1-是")
    private Integer isTutor;

    /** 上年房租总额 */
    @ApiModelProperty(value = "上年房租总额")
    private BigDecimal lastYearHouseAmount;

    /** 普票金额 */
//    @ApiModelProperty(value = "普票金额")
//    private BigDecimal normalTicketAmount;
//
//    /** 普票税额 */
//    @ApiModelProperty(value = "普票税额")
//    private BigDecimal normalTicketTaxAmount;
//
//    /** 专票金额 */
//    @ApiModelProperty(value = "专票金额")
//    private BigDecimal specialTicketAmount;
//
//    /** 专票税额 */
//    @ApiModelProperty(value = "专票税额")
//    private BigDecimal specialTicketTaxAmount;
//
//    /** 无票收入 */
//    @ApiModelProperty(value = "无票收入")
//    private BigDecimal noTicketAmount;
//
//    /** 无票税额 */
//    @ApiModelProperty(value = "无票税额")
//    private BigDecimal noTicketTaxAmount;
//
//    /** 简易收入 */
//    @ApiModelProperty(value = "简易收入")
//    private BigDecimal simpleAmount;
//
//    /** 简易税额 */
//    @ApiModelProperty(value = "简易税额")
//    private BigDecimal simpleTaxAmount;
//
//    /** 进项税额 */
//    @ApiModelProperty(value = "进项税额")
//    private BigDecimal incomeTaxAmount;
//
//    /** 上期留抵税额 */
//    @ApiModelProperty(value = "上期留抵税额")
//    private BigDecimal lastMonthPurposeTaxAmount;
//
//    /** 本月税负标准 */
//    @ApiModelProperty(value = "本月税负标准")
//    private BigDecimal thisMonthTaxBurden;

    @ApiModelProperty("新建预认证/国税备注")
    private String createRemark;

    @ApiModelProperty("新建预认证相关附件")
    private List<CommonFileVO> createFiles;

    @ApiModelProperty("联络员手机号")
    private String liaisonPhone;

    @ApiModelProperty("联络员证件号")
    private String liaisonCertificateNumber;

    @ApiModelProperty("是否同步联络，0-否，1-是")
    private Integer isSyncLiaison;

    @ApiModelProperty("无票收入")
    private BigDecimal preAuthNoTicketIncome;

    @ApiModelProperty("国税无票收入")
    private BigDecimal noTicketIncome;

    @ApiModelProperty("税种")
    private String taxCheckType;

    @ApiModelProperty("ddl，yyyy-MM-dd")
    private String ddl;

    @ApiModelProperty("是否关闭交付，0-否，1-是")
    private Integer isClose;
}
