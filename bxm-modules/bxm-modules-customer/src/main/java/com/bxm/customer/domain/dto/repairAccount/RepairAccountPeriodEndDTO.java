package com.bxm.customer.domain.dto.repairAccount;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/7/21 15:44
 * happy coding!
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RepairAccountPeriodEndDTO {
    @ApiModelProperty(value = "客户服务ID")
    private Long customerServiceId;

    @ApiModelProperty(value = "客户名")
    private String customerName;

    @ApiModelProperty(value = "补账的结束账期。PRD：账期必须是和记账服务连续的，即只允许选择开始账期，结束账期只能在补账服务关联的服务的首个账期-1月")
    private Integer periodEnd;

    @ApiModelProperty(value = "结束账期 文案")
    private String periodEndStr;
}
