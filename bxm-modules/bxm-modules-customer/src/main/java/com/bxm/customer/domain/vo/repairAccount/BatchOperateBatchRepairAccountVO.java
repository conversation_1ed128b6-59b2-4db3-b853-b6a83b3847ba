package com.bxm.customer.domain.vo.repairAccount;

import com.bxm.common.core.web.domain.CommonFileVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/7/27 20:13
 * happy coding!
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BatchOperateBatchRepairAccountVO {
    @ApiModelProperty("补账id")
    @NotEmpty
    private List<Long> ids;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty("核验附件")
    private List<CommonFileVO> files;
}
