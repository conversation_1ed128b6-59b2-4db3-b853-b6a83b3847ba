package com.bxm.customer.domain.dto;

import com.bxm.common.core.web.domain.CommonFileVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/6/17 21:37
 * happy coding!
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CustomerServiceMonthPeriodDetailDeliverDTO {
    @ApiModelProperty("交付ID")
    private Long id;

    @ApiModelProperty("交付事项值")
    private Integer code;

    @ApiModelProperty("交付事项名称")
    private String name;

    @ApiModelProperty("交付事项的总金额")
    private BigDecimal amount;

    @ApiModelProperty("状态值")
    private Integer status;

    @ApiModelProperty("状态")
    private String statusStr;

    @ApiModelProperty("文件列表")
    private List<CommonFileVO> files;
}
