package com.bxm.customer.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bxm.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.bxm.common.core.web.domain.BaseEntity;
import lombok.experimental.Accessors;

/**
 * 第三方申报同步对象 c_open_api_sync_record
 * 
 * <AUTHOR>
 * @date 2024-08-23
 */
@Data
@ApiModel("第三方申报同步对象")
@Accessors(chain = true)
@TableName("c_open_api_sync_record")
public class OpenApiSyncRecord extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @TableId(value = "id", type = IdType.AUTO)
    @Excel(name = "主键ID")
    @ApiModelProperty(value = "主键ID")
    private Long id;

    /** 来源类型，1-鑫启易 */
    @Excel(name = "来源类型，1-鑫启易")
    @TableField("source_type")
    @ApiModelProperty(value = "来源类型，1-鑫启易")
    private Integer sourceType;

    /** 批次号 */
    @Excel(name = "批次号")
    @TableField("batch_no")
    @ApiModelProperty(value = "批次号")
    private String batchNo;

    /** 账期 */
    @Excel(name = "账期")
    @TableField("period")
    @ApiModelProperty(value = "账期")
    private Integer period;

    /** 总数据量 */
    @Excel(name = "总数据量")
    @TableField("total_data_count")
    @ApiModelProperty(value = "总数据量")
    private Long totalDataCount;

    /** 成功数据量 */
    @Excel(name = "成功数据量")
    @TableField("success_data_count")
    @ApiModelProperty(value = "成功数据量")
    private Long successDataCount;

    /** 异常原因 */
    @Excel(name = "异常原因")
    @TableField("error_reason")
    @ApiModelProperty(value = "异常原因")
    private String errorReason;

    @TableField("type")
    @ApiModelProperty(value = "类型，1-税种核定，2-申报")
    private Integer type;

    @TableField("deliver_type")
    @ApiModelProperty(value = "交付类型，1-医保，2-社保，3-个税，4-国税")
    private String deliverType;

    @TableField("purchase_invoice_scope")
    private String purchaseInvoiceScope;
}
