package com.bxm.customer.domain.dto.workBench;

import com.bxm.common.core.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CustomerServicePeriodMonthNoAccountingDTO {

    @ApiModelProperty("客户名")
    @Excel(name = "客户名")
    private String customerName;

    @ApiModelProperty("信用代码")
    @Excel(name = "信用代码")
    private String creditCode;

    @ApiModelProperty("账期")
    @Excel(name = "账期")
    private String periodStr;

    @ApiModelProperty("服务状态名称")
    @Excel(name = "服务状态")
    private String serviceStatusStr;

    @ApiModelProperty("服务会计")
    @Excel(name = "服务会计")
    private String customerServiceAccountingDeptInfo;
}
