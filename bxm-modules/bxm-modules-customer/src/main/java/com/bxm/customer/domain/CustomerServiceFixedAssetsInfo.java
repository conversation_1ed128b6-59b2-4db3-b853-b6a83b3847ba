package com.bxm.customer.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bxm.common.core.annotation.Excel;
import com.bxm.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 客户服务固定资产信息对象 c_customer_service_fixed_assets_info
 * 
 * <AUTHOR>
 * @date 2024-07-01
 */
@Data
@ApiModel("客户服务固定资产信息对象")
@Accessors(chain = true)
@TableName("c_customer_service_fixed_assets_info")
public class CustomerServiceFixedAssetsInfo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @TableId(value = "id", type = IdType.AUTO)
    @Excel(name = "主键ID")
    @ApiModelProperty(value = "主键ID")
    private Long id;

    /** 客户服务ID */
    @Excel(name = "客户服务ID")
    @TableField("customer_service_id")
    @ApiModelProperty(value = "客户服务ID")
    private Long customerServiceId;

    /** 一次性税前扣除固定资产 */
    @Excel(name = "一次性税前扣除固定资产")
    @TableField("asset_name")
    @ApiModelProperty(value = "一次性税前扣除固定资产")
    private String assetName;

    /** 发生年份 */
    @Excel(name = "发生年份")
    @TableField("occurrence_year")
    @ApiModelProperty(value = "发生年份")
    private Integer occurrenceYear;


}
