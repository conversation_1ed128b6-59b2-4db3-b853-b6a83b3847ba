package com.bxm.customer.task;

import com.bxm.customer.service.ICustomerDeliverService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class PreAuthDeliverConfirmTask {

    @Autowired
    private ICustomerDeliverService customerDeliverService;

    @XxlJob("preAuthDeliverConfirmTask")
    public ReturnT<String> preAuthDeliverConfirmTask(String param) {
        log.info("每月自动确认预认证交付单任务开始=============");
        customerDeliverService.preAuthDeliverConfirmTask();
        log.info("每月自动确认预认证交付单任务结束=============");
        return ReturnT.SUCCESS;
    }
}
