package com.bxm.customer.domain.dto.settlementOrder;

import com.bxm.common.core.web.domain.CommonFileVO;
import com.bxm.customer.domain.SettlementOrderCondition;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SettlementOrderDetailDTO {

    @ApiModelProperty("结算单id")
    private Long id;

    @ApiModelProperty("账单编号")
    private String billNo;

    @ApiModelProperty("账单标题")
    private String billTitle;

    @ApiModelProperty("结算单编号")
    private String settlementOrderNo;

    @ApiModelProperty("结算单批次号")
    private String settlementBatchNo;

    @ApiModelProperty("结算单标题")
    private String settlementTitle;

    @ApiModelProperty("业务集团id")
    private Long businessTopDeptId;

    @ApiModelProperty("业务集团名称")
    private String businessTopDeptName;

    @ApiModelProperty("业务公司id")
    private Long businessDeptId;

    @ApiModelProperty("业务公司名称")
    private String businessDeptName;

    @ApiModelProperty("结算类型，1-入账结算，2-新户预收")
    private Integer settlementType;

    @ApiModelProperty("结算类型名称")
    private String settlementTypeName;

    @ApiModelProperty("单价")
    private BigDecimal price;

    @ApiModelProperty("单位")
    private String unit;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("附件")
    private List<CommonFileVO> files;

    @ApiModelProperty("结算单条件")
    private List<SettlementOrderCondition> settlementConditions;

    @ApiModelProperty("总数居")
    private Long dataCount;

    @ApiModelProperty("总金额")
    private BigDecimal totalPrice;

    @ApiModelProperty("优惠金额")
    private BigDecimal discountPrice;

    @ApiModelProperty("结算金额")
    private BigDecimal settlementPrice;

    @ApiModelProperty("状态,1-已创建待推送，2-结算中，3-已驳回，4-已撤回，5-已确认")
    private Integer status;

    @ApiModelProperty("状态名称")
    private String statusName;

    @ApiModelProperty("是否补差，0-否，1-是")
    private Boolean isSupplement;

    @ApiModelProperty("是否待修改，0-否，1-是")
    private Boolean isWaitForEdit;
}
