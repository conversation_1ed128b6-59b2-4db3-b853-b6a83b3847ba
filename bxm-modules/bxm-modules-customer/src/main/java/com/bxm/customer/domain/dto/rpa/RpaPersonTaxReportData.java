package com.bxm.customer.domain.dto.rpa;

import com.bxm.common.core.annotation.Excel;

import java.util.Date;
import java.util.List;

public class RpaPersonTaxReportData implements RpaEnterpriseData {

    @Excel(name = "序号")
    private String number;

    @Excel(name = "公司名称")
    private String enterpriseName;

    @Excel(name = "企业税号")
    private String creditCode;

    @Excel(name = "申报提交结果")
    private String reportResult;

    @Excel(name = "申报查询结果")
    private String reportSearchResult;

    @Excel(name = "申报表下载结果")
    private String reportDownloadResult;

    @Excel(name = "缴款金额")
    private String reportAmount;

    @Excel(name = "申报所属期")
    private String reportPeriod;

    @Excel(name = "开始执行时间", dateFormat = "yyyy/M/d HH:mm:ss")
    private Date startTime;

    @Excel(name = "结束执行时间", dateFormat = "yyyy/M/d HH:mm:ss")
    private Date endTime;

    @Excel(name = "异常信息")
    private String checkError;

    private List<AliFileDTO> files;

    private Long customerServiceId;

    private Long periodId;

    private Long deliverId;

    public Long getCustomerServiceId() {
        return customerServiceId;
    }

    public void setCustomerServiceId(Long customerServiceId) {
        this.customerServiceId = customerServiceId;
    }

    public Long getPeriodId() {
        return periodId;
    }

    public void setPeriodId(Long periodId) {
        this.periodId = periodId;
    }

    public Long getDeliverId() {
        return deliverId;
    }

    public void setDeliverId(Long deliverId) {
        this.deliverId = deliverId;
    }

    @Override
    public boolean hasErrors() {
        return checkError != null && !checkError.isEmpty();
    }

    @Override
    public void setCheckError(String checkError) {
        this.checkError = checkError;
    }

    @Override
    public void addCheckError(String checkError) {
        if (this.checkError == null || this.checkError.isEmpty()) {
            this.checkError = checkError;
        } else {
            this.checkError += "; " + checkError;
        }
    }

    public String getNumber() {
        return number;
    }

    public void setNumber(String number) {
        this.number = number;
    }

    public String getEnterpriseName() {
        return enterpriseName;
    }

    public void setEnterpriseName(String enterpriseName) {
        this.enterpriseName = enterpriseName;
    }

    public String getCreditCode() {
        return creditCode;
    }

    public void setCreditCode(String creditCode) {
        this.creditCode = creditCode;
    }

    public String getReportResult() {
        return reportResult;
    }

    public void setReportResult(String reportResult) {
        this.reportResult = reportResult;
    }

    public String getReportSearchResult() {
        return reportSearchResult;
    }

    public void setReportSearchResult(String reportSearchResult) {
        this.reportSearchResult = reportSearchResult;
    }

    public String getReportDownloadResult() {
        return reportDownloadResult;
    }

    public void setReportDownloadResult(String reportDownloadResult) {
        this.reportDownloadResult = reportDownloadResult;
    }

    public String getReportAmount() {
        return reportAmount;
    }

    public void setReportAmount(String reportAmount) {
        this.reportAmount = reportAmount;
    }

    public String getReportPeriod() {
        return reportPeriod;
    }

    public void setReportPeriod(String reportPeriod) {
        this.reportPeriod = reportPeriod;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    @Override
    public String getCheckError() {
        return checkError;
    }

    @Override
    public List<AliFileDTO> getFiles() {
        return files;
    }

    @Override
    public String getSheetIndex() {
        return "";
    }

    public void setFiles(List<AliFileDTO> files) {
        this.files = files;
    }
}
