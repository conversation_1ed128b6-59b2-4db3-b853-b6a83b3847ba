package com.bxm.customer.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CustomerServiceWorkbenchDTO {

    @ApiModelProperty("待分派数量")
    private Long waitDispatchCount;

    @ApiModelProperty("待重派数量")
    private Long waitReDispatchCount;

    @ApiModelProperty("顾问待分派数量")
    private Long waitDispatchAdvisorCount;

    @ApiModelProperty("补账待分派数量")
    private Long repairAccountWaitDispatchCount;
}
