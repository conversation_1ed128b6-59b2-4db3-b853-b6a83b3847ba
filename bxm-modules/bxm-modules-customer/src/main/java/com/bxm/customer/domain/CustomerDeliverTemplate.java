package com.bxm.customer.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bxm.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.bxm.common.core.web.domain.BaseEntity;
import lombok.experimental.Accessors;

/**
 * 交付模板对象 c_customer_deliver_template
 * 
 * <AUTHOR>
 * @date 2024-06-17
 */
@Data
@ApiModel("交付模板对象")
@Accessors(chain = true)
@TableName("c_customer_deliver_template")
public class CustomerDeliverTemplate extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** ID */
    @TableId(value = "id", type = IdType.AUTO)
    @Excel(name = "ID")
    @ApiModelProperty(value = "ID")
    private Long id;

    /** 交付类型，1-医保，2-社保，3-个税（工资薪金），4-国税，5-预认证 */
    @Excel(name = "交付类型，1-医保，2-社保，3-个税（工资薪金），4-国税，5-预认证")
    @TableField("deliver_type")
    @ApiModelProperty(value = "交付类型，1-医保，2-社保，3-个税（工资薪金），4-国税，5-预认证")
    private Integer deliverType;

    /** 操作类型，1-新建，2-申报，3-解除申报异常，4-扣款，5-解除扣款异常，6-补充，7-认证，8-解除认证异常 */
    @Excel(name = "操作类型，1-新建，2-申报，3-解除申报异常，4-扣款，5-解除扣款异常，6-补充，7-认证，8-解除认证异常")
    @TableField("oper_type")
    @ApiModelProperty(value = "操作类型，1-新建，2-申报，3-解除申报异常，4-扣款，5-解除扣款异常，6-补充，7-认证，8-解除认证异常")
    private Integer operType;

    /** 文件地址 */
    @Excel(name = "文件地址")
    @TableField("file_url")
    @ApiModelProperty(value = "文件地址")
    private String fileUrl;

}
