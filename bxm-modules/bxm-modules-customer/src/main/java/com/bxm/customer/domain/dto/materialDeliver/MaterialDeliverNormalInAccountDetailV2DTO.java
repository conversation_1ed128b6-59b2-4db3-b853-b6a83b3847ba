package com.bxm.customer.domain.dto.materialDeliver;

import com.bxm.common.core.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MaterialDeliverNormalInAccountDetailV2DTO {

    @ApiModelProperty("解析结果(文案)")
    @Excel(name = "解析结果")
    private String analysisResultStr;

    @ApiModelProperty("异常原因")
    @Excel(name = "异常原因")
    private String errorMsg;

    @ApiModelProperty("客户名称")
    @Excel(name = "客户名")
    private String customerName;

    @Excel(name = "入账材料数量")
    private Long checkFileCount;
}
