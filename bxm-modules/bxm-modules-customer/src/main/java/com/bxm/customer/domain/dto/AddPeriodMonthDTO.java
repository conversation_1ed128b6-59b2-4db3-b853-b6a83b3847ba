package com.bxm.customer.domain.dto;

import com.bxm.common.core.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AddPeriodMonthDTO {

    @Excel(name = "客户id")
    private Long customerServiceId;

    @Excel(name = "补账开始账期")
    private Integer periodStart;

    @Excel(name = "补账结束账期")
    private Integer periodEnd;

    @Excel(name = "甲方区域")
    private String businessDeptName;

    @Excel(name = "甲方服务组别")
    private String advisorDeptName;

    @Excel(name = "服务会计组别")
    private String accountingDeptName;

    @Excel(name = "服务会计")
    private String accountingEmployeeName;

    private String error;
}
