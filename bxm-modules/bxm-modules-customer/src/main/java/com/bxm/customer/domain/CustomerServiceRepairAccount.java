package com.bxm.customer.domain;

import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bxm.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.bxm.common.core.web.domain.BaseEntity;
import lombok.experimental.Accessors;

/**
 * 补账对象 c_customer_service_repair_account
 * 
 * <AUTHOR>
 * @date 2024-07-11
 */
@Data
@ApiModel("补账对象")
@Accessors(chain = true)
@TableName("c_customer_service_repair_account")
public class CustomerServiceRepairAccount extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @TableId(value = "id", type = IdType.AUTO)
    @Excel(name = "主键ID")
    @ApiModelProperty(value = "主键ID")
    private Long id;

    /** 是否删除，0-否，1-是 */
    @Excel(name = "是否删除，0-否，1-是")
    @TableField("is_del")
    @ApiModelProperty(value = "是否删除，0-否，1-是")
    private Boolean isDel;

    /** 客户服务ID */
    @Excel(name = "客户服务ID")
    @TableField("customer_service_id")
    @ApiModelProperty(value = "客户服务ID")
    private Long customerServiceId;

    /** 客户名 */
    @Excel(name = "客户名")
    @TableField("customer_name")
    @ApiModelProperty(value = "客户名")
    private String customerName;

    /** 信用代码 */
    @Excel(name = "信用代码")
    @TableField("credit_code")
    @ApiModelProperty(value = "信用代码")
    private String creditCode;

    /** 税号 */
    @Excel(name = "税号")
    @TableField("tax_number")
    @ApiModelProperty(value = "税号")
    private String taxNumber;

    /** 服务编号、档案编码、客户编码 */
    @Excel(name = "服务编号、档案编码、客户编码")
    @TableField("service_number")
    @ApiModelProperty(value = "服务编号、档案编码、客户编码")
    private String serviceNumber;

    /** 纳税人性质，1-小规模，2-一般纳税人 */
    @Excel(name = "纳税人性质，1-小规模，2-一般纳税人")
    @TableField("tax_type")
    @ApiModelProperty(value = "纳税人性质，1-小规模，2-一般纳税人")
    private Integer taxType;

    /** 业务部门id */
    @Excel(name = "业务部门id")
    @TableField("business_dept_id")
    @ApiModelProperty(value = "业务部门id")
    private Long businessDeptId;

    /** 业务部门名称 */
    @Excel(name = "业务部门名称")
    @TableField("business_dept_name")
    @ApiModelProperty(value = "业务部门名称")
    private String businessDeptName;

    /** 顶级业务部门id */
    @Excel(name = "顶级业务部门id")
    @TableField("business_top_dept_id")
    @ApiModelProperty(value = "顶级业务部门id")
    private Long businessTopDeptId;

    /** 顾问部门id */
    @Excel(name = "顾问部门id")
    @TableField("advisor_dept_id")
    @ApiModelProperty(value = "顾问部门id")
    private Long advisorDeptId;

    /** 顾问部门名称 */
    @Excel(name = "顾问部门名称")
    @TableField("advisor_dept_name")
    @ApiModelProperty(value = "顾问部门名称")
    private String advisorDeptName;

    /** 顾问顶级部门id */
    @Excel(name = "顾问顶级部门id")
    @TableField("advisor_top_dept_id")
    @ApiModelProperty(value = "顾问顶级部门id")
    private Long advisorTopDeptId;

    /** 会计部门id */
    @Excel(name = "会计部门id")
    @TableField("accounting_dept_id")
    @ApiModelProperty(value = "会计部门id")
    private Long accountingDeptId;

    /** 会计部门名称 */
    @Excel(name = "会计部门名称")
    @TableField("accounting_dept_name")
    @ApiModelProperty(value = "会计部门名称")
    private String accountingDeptName;

    /** 会计顶级部门id */
    @Excel(name = "会计顶级部门id")
    @TableField("accounting_top_dept_id")
    @ApiModelProperty(value = "会计顶级部门id")
    private Long accountingTopDeptId;

    /** 承验人类型：1总部，-1会计区域 */
    @Excel(name = "承验人类型：1总部，-1会计区域")
    @TableField("verification_employee_type")
    @ApiModelProperty(value = "承验人类型：1总部，-1会计区域")
    private Integer verificationEmployeeType;

    /** 状态： */
    @Excel(name = "状态：")
    @TableField("status")
    @ApiModelProperty(value = "状态：")
    private Integer status;

    /** 开始账期 */
    @Excel(name = "开始账期")
    @TableField("start_period")
    @ApiModelProperty(value = "开始账期")
    private Integer startPeriod;

    /** 结束账期 */
    @Excel(name = "结束账期")
    @TableField("end_period")
    @ApiModelProperty(value = "结束账期")
    private Integer endPeriod;

    /** 备注 */
    @Excel(name = "备注")
    @TableField("remark")
    @ApiModelProperty(value = "备注")
    private String remark;

    /** 提交时间 */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "提交时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @TableField("submit_time")
    @ApiModelProperty(value = "提交时间")
    private LocalDateTime submitTime;

    /** 提交员工部门ID */
    @Excel(name = "提交员工部门ID")
    @TableField("submit_employee_dept_id")
    @ApiModelProperty(value = "提交员工部门ID")
    private Long submitEmployeeDeptId;

    /** 提交员工部门名称 */
    @Excel(name = "提交员工部门名称")
    @TableField("submit_employee_dept_name")
    @ApiModelProperty(value = "提交员工部门名称")
    private String submitEmployeeDeptName;

    /** 提交员工id */
    @Excel(name = "提交员工id")
    @TableField("submit_employee_id")
    @ApiModelProperty(value = "提交员工id")
    private Long submitEmployeeId;

    /** 提交员工名称 */
    @Excel(name = "提交员工名称")
    @TableField("submit_employee_name")
    @ApiModelProperty(value = "提交员工名称")
    private String submitEmployeeName;

    /** 分派时间 */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "分派时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @TableField("assign_time")
    @ApiModelProperty(value = "分派时间")
    private LocalDateTime assignTime;

    /** 分派员工部门ID */
    @Excel(name = "分派员工部门ID")
    @TableField("assign_employee_dept_id")
    @ApiModelProperty(value = "分派员工部门ID")
    private Long assignEmployeeDeptId;

    /** 分派员工部门名称 */
    @Excel(name = "分派员工部门名称")
    @TableField("assign_employee_dept_name")
    @ApiModelProperty(value = "分派员工部门名称")
    private String assignEmployeeDeptName;

    /** 分派员工id */
    @Excel(name = "分派员工id")
    @TableField("assign_employee_id")
    @ApiModelProperty(value = "分派员工id")
    private Long assignEmployeeId;

    /** 分派员工名称 */
    @Excel(name = "分派员工名称")
    @TableField("assign_employee_name")
    @ApiModelProperty(value = "分派员工名称")
    private String assignEmployeeName;

    /** 分派说明 */
    @Excel(name = "分派说明")
    @TableField("assign_remark")
    @ApiModelProperty(value = "分派说明")
    private String assignRemark;

    /** 分派到哪个会计小组 */
    @Excel(name = "分派到哪个会计小组")
    @TableField("assign_accounting_dept_id")
    @ApiModelProperty(value = "分派到哪个会计小组")
    private Long assignAccountingDeptId;

    /** 退回时间 */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "退回时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @TableField("back_time")
    @ApiModelProperty(value = "退回时间")
    private LocalDateTime backTime;

    /** 退回员工部门ID */
    @Excel(name = "退回员工部门ID")
    @TableField("back_employee_dept_id")
    @ApiModelProperty(value = "退回员工部门ID")
    private Long backEmployeeDeptId;

    /** 退回员工部门名称 */
    @Excel(name = "退回员工部门名称")
    @TableField("back_employee_dept_name")
    @ApiModelProperty(value = "退回员工部门名称")
    private String backEmployeeDeptName;

    /** 退回员工id */
    @Excel(name = "退回员工id")
    @TableField("back_employee_id")
    @ApiModelProperty(value = "退回员工id")
    private Long backEmployeeId;

    /** 退回员工名称 */
    @Excel(name = "退回员工名称")
    @TableField("back_employee_name")
    @ApiModelProperty(value = "退回员工名称")
    private String backEmployeeName;

    /** 退回说明 */
    @Excel(name = "退回说明")
    @TableField("back_remark")
    @ApiModelProperty(value = "退回说明")
    private String backRemark;

    /** 交付状态，实际是个逻辑值，但不好搜索，就直接存库了：1-待交付、2-交付中、3已完成 */
    @Excel(name = "交付状态，实际是个逻辑值，但不好搜索，就直接存库了：1-待交付、2-交付中、3已完成")
    @TableField("deliver_status")
    @ApiModelProperty(value = "交付状态，实际是个逻辑值，但不好搜索，就直接存库了：1-待交付、2-交付中、3已完成")
    private Integer deliverStatus;

}
