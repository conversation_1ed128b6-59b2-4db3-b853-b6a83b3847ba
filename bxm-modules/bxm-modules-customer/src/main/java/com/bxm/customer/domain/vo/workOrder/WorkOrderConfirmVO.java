package com.bxm.customer.domain.vo.workOrder;

import com.bxm.common.core.web.domain.CommonFileVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class WorkOrderConfirmVO {

    @ApiModelProperty("工单id")
    private Long id;

    @ApiModelProperty("是否完结，0-否，1-是")
    private Integer isFinish;

    @ApiModelProperty("ddl")
    private String ddl;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("附件")
    private List<CommonFileVO> files;

    @ApiModelProperty(hidden = true)
    private Long userId;
}
