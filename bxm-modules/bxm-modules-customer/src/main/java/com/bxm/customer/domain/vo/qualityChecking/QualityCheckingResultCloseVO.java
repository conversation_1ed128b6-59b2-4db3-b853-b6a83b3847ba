package com.bxm.customer.domain.vo.qualityChecking;

import com.bxm.common.core.web.domain.CommonFileVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class QualityCheckingResultCloseVO {

    @ApiModelProperty("质检记录id，单个关闭")
    private Long qualityCheckingRecordId;

    @ApiModelProperty("质检记录id列表，批量关闭")
    private List<Long> qualityCheckingRecordIds;
}
