package com.bxm.customer.domain.dto.newCustomerTransfer;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class NewCustomerTransferInfoDTO {

    @ApiModelProperty("创建")
    private NewCustomerTransferStatusDTO create;

    @ApiModelProperty("提交")
    private NewCustomerTransferStatusDTO submit;

    @ApiModelProperty("流转")
    private NewCustomerTransferStatusDTO transfer;

    @ApiModelProperty("显示位置，1-最上方，2-最下方")
    private Integer showLocation;

    @ApiModelProperty("新户流转id")
    private Long customerTransferId;
}
