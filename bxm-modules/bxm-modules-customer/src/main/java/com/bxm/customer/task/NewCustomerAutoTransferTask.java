package com.bxm.customer.task;

import com.bxm.customer.service.ICustomerDeliverService;
import com.bxm.customer.service.INewCustomerInfoService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class NewCustomerAutoTransferTask {

    @Autowired
    private INewCustomerInfoService newCustomerInfoService;

    @XxlJob("newCustomerAutoTransferTask")
    public ReturnT<String> newCustomerAutoTransferTask(String param) {
        log.info("自动流转任务开始=============");
        newCustomerInfoService.newCustomerAutoTransferTask();
        log.info("自动流转任务结束=============");
        return ReturnT.SUCCESS;
    }
}
