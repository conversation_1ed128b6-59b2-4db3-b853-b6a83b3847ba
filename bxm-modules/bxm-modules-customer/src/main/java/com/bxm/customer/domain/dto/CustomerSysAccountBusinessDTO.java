package com.bxm.customer.domain.dto;

import com.bxm.common.core.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CustomerSysAccountBusinessDTO {

    @Excel(name = "客户名")
    private String customerName;

    @Excel(name = "信用代码")
    private String creditCode;

    @Excel(name = "业务归属集团")
    private String businessTopDeptName;

    @Excel(name = "业务归属公司")
    private String businessDeptName;

    @Excel(name = "顾问小组")
    private String advisorDeptName;

    @Excel(name = "顾问名")
    private String advisorEmployeeName;

    @Excel(name = "系统")
    private String sysTypeName;

    @Excel(name = "账号")
    private String account;

    @Excel(name = "密码")
    private String password;

    @Excel(name = "登陆方式")
    private String loginType;

    @Excel(name = "实名人员/经办人")
    private String contact;

    @Excel(name = "手机号")
    private String contactMobile;

    @Excel(name = "身份证号")
    private String idNumber;

    @Excel(name = "备注说明")
    private String remark;
}
