package com.bxm.customer.domain.dto.businessTask;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/10/8 16:30
 * happy coding!
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SelectDTO {
    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("名称")
    private String name;
}
