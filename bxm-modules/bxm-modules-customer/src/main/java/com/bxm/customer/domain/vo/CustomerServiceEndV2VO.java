package com.bxm.customer.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/7/13 21:21
 * happy coding!
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CustomerServiceEndV2VO {
    @ApiModelProperty("选择的服务id列表")
    private List<Long> ids;

    @ApiModelProperty("1-上月结束，2-本月结束")
    private Integer endPeriod;

    @ApiModelProperty(value = "最后记账账期")
    private Integer lastAccountPeriod;

    private Boolean isTask;
}
