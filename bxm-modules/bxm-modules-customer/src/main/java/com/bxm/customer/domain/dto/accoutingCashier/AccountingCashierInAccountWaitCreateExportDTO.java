package com.bxm.customer.domain.dto.accoutingCashier;

import com.bxm.common.core.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AccountingCashierInAccountWaitCreateExportDTO {

    @ApiModelProperty("客户名称")
    @Excel(name = "客户名")
    private String customerName;

    @ApiModelProperty("信用代码")
    @Excel(name = "信用代码")
    private String creditCode;

    @ApiModelProperty("账期业务公司")
    @Excel(name = "账期业务公司")
    private String businessDeptName;

    @ApiModelProperty("账期")
    @Excel(name = "账期")
    private Integer period;

    @ApiModelProperty("账期顾问信息")
    @Excel(name = "账期顾问")
    private String periodAdvisorInfo;

    @ApiModelProperty("账期会计信息")
    @Excel(name = "账期会计")
    private String periodAccountingInfo;

    @ApiModelProperty("事项结果")
    @Excel(name = "事项结果")
    private String itemResult;
}
