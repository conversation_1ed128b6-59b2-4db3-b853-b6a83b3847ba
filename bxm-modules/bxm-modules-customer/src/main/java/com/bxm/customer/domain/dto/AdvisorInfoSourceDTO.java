package com.bxm.customer.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/7/24 22:13
 * happy coding!
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AdvisorInfoSourceDTO {
    @ApiModelProperty(value = "月度账期id")
    private Long customerServicePeriodMonthId;

    @ApiModelProperty(value = "顾问部门id")
    private Long advisorDeptId;

    @ApiModelProperty(value = "顾问部门名称")
    private String advisorDeptName;

    @ApiModelProperty("顾问id")
    private String advisorEmployeeId;

    @ApiModelProperty("顾问")
    private String advisorEmployeeName;
}
