package com.bxm.customer.task;

import com.bxm.customer.service.ICCustomerServiceService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class CreatePeriodTask {

    @Autowired
    private ICCustomerServiceService customerServiceService;

    @XxlJob("createPeriodTask")
    public ReturnT<String> createPeriod(String param) {
        log.info("每月生成账期任务开始=============");
        String jobParam = XxlJobHelper.getJobParam();
        customerServiceService.createPeriod(jobParam);
        log.info("每月生成账期任务结束=============");
        return ReturnT.SUCCESS;
    }
}
