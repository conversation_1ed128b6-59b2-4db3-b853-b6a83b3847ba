package com.bxm.customer.task;

import com.bxm.customer.service.IOpenApiNoticeRecordService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class RpaTaskStatusSearchTask {

    @Autowired
    private IOpenApiNoticeRecordService openApiNoticeRecordService;

    @XxlJob("rpaTaskStatusSearchTask")
    public ReturnT<String> rpaTaskStatusSearchTask(String param) {
        log.info("主动查询任务状态轮询开始=============");
        openApiNoticeRecordService.rpaTaskStatusSearchTask();
        log.info("主动查询任务状态轮询结束=============");
        return ReturnT.SUCCESS;
    }
}
