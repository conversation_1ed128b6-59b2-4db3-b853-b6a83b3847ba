package com.bxm.customer.domain.dto;

import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
public class BusinessDeptSelectDTO {

    @ApiModelProperty("顾问下拉框数据")
    private List<BusinessDeptDTO> advisorDeptList;

    @ApiModelProperty("会计下拉框数据")
    private List<BusinessDeptDTO> accountantDeptList;

    @ApiModelProperty("业务公司下拉框数据")
    private List<BusinessDeptDTO> businessDeptList;

    public BusinessDeptSelectDTO() {
        this.advisorDeptList = Lists.newArrayList();
        this.accountantDeptList = Lists.newArrayList();
        this.businessDeptList = Lists.newArrayList();
    }
}
