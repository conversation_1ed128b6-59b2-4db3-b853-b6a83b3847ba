package com.bxm.customer.domain.vo.workOrder;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class WorkOrderModifyDdlVO {

    @ApiModelProperty("选中的工单id列表")
    private List<Long> ids;

    @ApiModelProperty("ddl，yyyy-MM-dd")
    private String ddl;
}
