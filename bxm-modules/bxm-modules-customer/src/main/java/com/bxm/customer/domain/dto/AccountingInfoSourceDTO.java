package com.bxm.customer.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/7/5 23:01
 * happy coding!
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AccountingInfoSourceDTO {
    @ApiModelProperty(value = "月度账期id")
    private Long customerServicePeriodMonthId;

    @ApiModelProperty(value = "会计部门id")
    private Long accountingDeptId;

    @ApiModelProperty(value = "会计部门名称")
    private String accountingDeptName;

    @ApiModelProperty("会计id")
    private String accountingEmployeeId;

    @ApiModelProperty("会计")
    private String accountingEmployeeName;
}
