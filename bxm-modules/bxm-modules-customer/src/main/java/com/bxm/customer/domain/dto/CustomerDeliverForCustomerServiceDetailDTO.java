package com.bxm.customer.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/6/6 13:50
 * happy coding!
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CustomerDeliverForCustomerServiceDetailDTO {
    /**
     * ID
     */
    @ApiModelProperty(value = "ID")
    private Long id;

    @ApiModelProperty("账期")
    private Integer period;

    @ApiModelProperty(value = "交付类型，1-医保，2-社保，3-个税（工资薪金），4-国税，5-预认证")
    private Integer deliverType;

    @ApiModelProperty("交付类型名称")
    private String deliverTypeName;

    /**
     * 国税的金额=增值税+附加税+印花税+其他
     * 预认证的等阿苏确认
     * 其他的=report_amount
     * <p>
     * 预认证的那些都有落库
     */
    @ApiModelProperty(value = "金额/个税（工资薪金）款")
    private BigDecimal amount;

    /**
     * 状态，0-已提交待申报，1-已保存待提交，2-已申报待确认，3-已确认待扣款，4-扣款已保存待提交，5-已扣款，6-待重提，7-申报异常，8-暂不申报，9-扣款异常，10-暂不扣款，101-已提交待补充，102-已补充待认证，103-已认证待确认，104-已确认，105-待重提，106-认证异常，107-暂不认证
     */
    @ApiModelProperty(value = "状态，0-已提交待申报，1-已保存待提交，2-已申报待确认，3-已确认待扣款，4-扣款已保存待提交，5-已扣款，6-待重提，7-申报异常，8-暂不申报，9-扣款异常，10-暂不扣款，101-已提交待补充，102-已补充待认证，103-已认证待确认，104-已确认，105-待重提，106-认证异常，107-暂不认证")
    private Integer status;

    @ApiModelProperty("状态名称")
    private String statusStr;

    /**
     * 好的 @猫玛尼 那这里要根据交付类型区分一下的
     * 交付类型=预认证的，提报=5，结果=6
     * 交付类型=其他类型的，提报=2，结果=3
     */
    @ApiModelProperty("提报，文件数量")
    private Long reportingFileCount;

    @ApiModelProperty("结果，文件数量")
    private Long resultFileCount;
}
