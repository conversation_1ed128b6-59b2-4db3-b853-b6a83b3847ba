package com.bxm.customer.domain.dto.newCustomerTransfer;

import com.bxm.customer.domain.vo.newCustomer.NewCustomerServiceTaxTypeCheckVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class NewCustomerServiceTaxTypeCheckDTO {

    @ApiModelProperty("客户服务id")
    private Long newCustomerTransferId;

//    @ApiModelProperty("税务信息相关字段")
//    private NewCustomerServiceFinanceTaxInfoDTO taxInfo;

    @ApiModelProperty("税种列表")
    private List<NewCustomerServiceTaxTypeCheckVO> taxTypes;
}
