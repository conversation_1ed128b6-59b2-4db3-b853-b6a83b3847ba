package com.bxm.customer.domain.dto;

import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
public class DeliverDeptSelectDTO {

    @ApiModelProperty("服务顾问下拉框数据")
    private List<BusinessDeptDTO> customerServiceAdvisorDeptList;

    @ApiModelProperty("服务会计下拉框数据")
    private List<BusinessDeptDTO> customerServiceAccountingDeptList;

    @ApiModelProperty("账期顾问下拉框数据")
    private List<BusinessDeptDTO> periodAdvisorDeptList;

    @ApiModelProperty("账期会计下拉框数据")
    private List<BusinessDeptDTO> periodAccountingDeptList;

    public DeliverDeptSelectDTO() {
        this.customerServiceAccountingDeptList = Lists.newArrayList();
        this.customerServiceAdvisorDeptList = Lists.newArrayList();
        this.periodAccountingDeptList = Lists.newArrayList();
        this.periodAdvisorDeptList = Lists.newArrayList();
    }
}
