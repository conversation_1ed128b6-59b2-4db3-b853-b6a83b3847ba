package com.bxm.customer.domain.dto.newCustomerTransfer;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class NewCustomerTransferOtherInfoDTO {

    @ApiModelProperty(value = "新户流转ID")
    private Long newCustomerTransferId;

    @ApiModelProperty("收入")
    private List<NewCustomerTransferMonthIncomeDTO> incomeList;

    @ApiModelProperty("汇算清缴")
    private NewCustomerTransferSettlementPaymentInfoDTO settlementPaymentInfo;

    @ApiModelProperty("工商年报")
    private NewCustomerTransferAnnualReportInfoDTO annualReportInfo;
}
