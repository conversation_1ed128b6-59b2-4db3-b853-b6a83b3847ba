package com.bxm.customer.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CustomerDeliverRpaDetailDTO {

    @ApiModelProperty("明细id")
    private Long id;

    @ApiModelProperty("rpa类型，1-医社保，2-个税（工资薪金），3-国税，4-收入，5-预认证")
    private Integer rpaType;

    @ApiModelProperty("操作类型，1-申报，2-扣款，3-其他，4-检查")
    private Integer operType;

    @ApiModelProperty("客户名")
    private String customerName;

    @ApiModelProperty("税号")
    private String creditCode;

    @ApiModelProperty("账期")
    private Integer period;

    @ApiModelProperty("文件数量")
    private Integer fileCount;

    @ApiModelProperty("0-待确认，1-处理中，2-成功，3-失败")
    private Integer status;

    @ApiModelProperty("导入异常原因")
    private String exceptionMsg;

    @ApiModelProperty("写入失败原因")
    private String failReason;

    @ApiModelProperty(value = "rpa执行结果")
    private String rpaResult;

    @ApiModelProperty(value = "申报结果")
    private String reportResult;
}
