package com.bxm.customer.task;

import com.bxm.common.core.utils.DateUtils;
import com.bxm.common.core.utils.StringUtils;
import com.bxm.customer.service.ICCustomerServiceService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

@Component
@Slf4j
public class XqySetCustomerServiceUserTask {

    @Autowired
    private ICCustomerServiceService customerServiceService;

    @XxlJob("xqySetCustomerServiceUserTask")
    public ReturnT<String> xqySetCustomerServiceUserTask() {
        log.info("每月鑫启易推送客户服务人员任务开始=============");
        String jobParam = XxlJobHelper.getJobParam();
        LocalDate date = StringUtils.isEmpty(jobParam) ? LocalDate.now().minusMonths(1) : LocalDate.parse(jobParam + "-01", DateTimeFormatter.ofPattern(DateUtils.YYYY_MM_DD));
        customerServiceService.xqySetCustomerServiceUser(date);
        log.info("每月鑫启易推送客户服务人员任务结束=============");
        return ReturnT.SUCCESS;
    }
}
