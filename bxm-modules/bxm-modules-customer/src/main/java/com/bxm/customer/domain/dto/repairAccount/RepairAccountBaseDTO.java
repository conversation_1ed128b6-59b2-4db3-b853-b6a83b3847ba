package com.bxm.customer.domain.dto.repairAccount;

import com.bxm.common.core.web.domain.CommonFileVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/7/21 15:21
 * happy coding!
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RepairAccountBaseDTO {
    @ApiModelProperty("补账id")
    private Long id;

    @ApiModelProperty(value = "客户企业ID")
    private Long customerServiceId;

    @ApiModelProperty(value = "客户名")
    private String customerName;

    @ApiModelProperty(value = "服务账期 开始")
    private Integer periodStart;

    @ApiModelProperty(value = "服务账期 结束")
    private Integer periodEnd;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty("附件")
    private List<CommonFileVO> files;
}
