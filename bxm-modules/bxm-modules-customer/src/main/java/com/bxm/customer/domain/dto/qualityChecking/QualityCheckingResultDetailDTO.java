package com.bxm.customer.domain.dto.qualityChecking;

import com.bxm.common.core.web.domain.CommonFileVO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class QualityCheckingResultDetailDTO {

    @ApiModelProperty("质检结果id")
    private Long qualityCheckingResultId;

    @ApiModelProperty("账期id")
    private Long customerServicePeriodMonthId;

    @ApiModelProperty("客户id")
    private Long customerServiceId;

    @ApiModelProperty("信用代码")
    private String creditCode;

    @ApiModelProperty("客户名称")
    private String customerName;

    @ApiModelProperty("质检事项id")
    private Long qualityCheckingItemId;

    @ApiModelProperty("质检事项名称")
    private String qualityCheckingItemName;

    @ApiModelProperty("质检类型，1-账务问题,2-风险提示")
    private Integer qualityCheckingType;

    @ApiModelProperty("质检类型名称")
    private String qualityCheckingTypeName;

    @ApiModelProperty("质检周期，1-单期，2-累计")
    private Integer qualityCheckingCycle;

    @ApiModelProperty("质检周期名称")
    private String qualityCheckingCycleName;

    @ApiModelProperty("账期")
    private String periodStr;

    @ApiModelProperty("质检结果，1-正常，2-异常")
    private Integer checkingResult;

    @ApiModelProperty("质检结果名称")
    private String checkingResultName;

    @ApiModelProperty("执行状态，0-未执行，1-已执行")
    private Integer status;

    @ApiModelProperty("执行状态名称")
    private String statusName;

    @ApiModelProperty("检测次数")
    private Integer checkedTimes;

    @ApiModelProperty("首次检测时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime firstCheckTime;

    @ApiModelProperty("末次检测时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime lastCheckTime;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("附件")
    private List<CommonFileVO> files;

    @ApiModelProperty("同账期其他质检事项列表，如果这个字段返回是空的，前端不要展示这一块区域")
    private Map<String, List<QualityCheckingResultDTO>> otherQualityCheckingResultMap;
}
