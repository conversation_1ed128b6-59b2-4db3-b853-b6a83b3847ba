package com.bxm.customer.domain.vo.workOrder;

import com.bxm.common.core.web.domain.BaseVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class QualityExceptionMiniListSearchVO extends BaseVO {

    @ApiModelProperty("质检类型，1-账务问题，2-风险提示")
    private Integer qualityCheckingType;

    @ApiModelProperty("选择的组织id")
    private Long queryDeptId;

    @ApiModelProperty("选择的组织id（多选）")
    private String deptIds;

    @ApiModelProperty("搜索关键字")
    private String keyWord;

    @ApiModelProperty("客户搜索批次号")
    private String batchNo;

    @ApiModelProperty("质检事项id，多个用英文逗号隔开")
    private String qualityCheckingItemIds;

    @ApiModelProperty("账期（开始），yyyyMM")
    private Integer periodStart;

    @ApiModelProperty("账期（结束），yyyyMM")
    private Integer periodEnd;

    @ApiModelProperty("顾问小组id")
    private Long advisorDeptId;

    @ApiModelProperty("会计小组id")
    private Long accountingDeptId;

    @ApiModelProperty(hidden = true)
    private Long userId;

    @ApiModelProperty(hidden = true)
    private Long deptId;
}
