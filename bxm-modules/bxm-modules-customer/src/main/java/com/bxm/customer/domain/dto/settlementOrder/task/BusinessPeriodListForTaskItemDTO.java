package com.bxm.customer.domain.dto.settlementOrder.task;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/10/6 17:37
 * happy coding!
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BusinessPeriodListForTaskItemDTO {
    @ApiModelProperty("账期")
    private Integer period;

    @ApiModelProperty("账期 文案")
    private String periodStr;

    @ApiModelProperty("总数据")
    private Integer dataCount;
}
