package com.bxm.customer.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bxm.common.core.annotation.Excel;
import com.bxm.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 新户流转五险一金信息对象 c_new_customer_insurance_fund_info
 * 
 * <AUTHOR>
 * @date 2024-07-01
 */
@Data
@ApiModel("新户流转五险一金信息对象")
@Accessors(chain = true)
@TableName("c_new_customer_insurance_fund_info")
public class NewCustomerInsuranceFundInfo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @TableId(value = "id", type = IdType.AUTO)
    @Excel(name = "主键ID")
    @ApiModelProperty(value = "主键ID")
    private Long id;

    /** 客户ID */
    @Excel(name = "客户ID")
    @TableField("customer_id")
    @ApiModelProperty(value = "客户ID")
    private Long customerId;

    /** 社保人数 */
    @Excel(name = "社保人数")
    @TableField("social_security_people")
    @ApiModelProperty(value = "社保人数")
    private String socialSecurityPeople;

    /** 社保基数 */
    @Excel(name = "社保基数")
    @TableField("social_security_base")
    @ApiModelProperty(value = "社保基数")
    private String socialSecurityBase;

    /** 社保缴纳金额 */
    @Excel(name = "社保缴纳金额")
    @TableField("total_contribution")
    @ApiModelProperty(value = "社保缴纳金额")
    private String totalContribution;

    /** 工伤金额 */
    @Excel(name = "工伤金额")
    @TableField("injury_fee")
    @ApiModelProperty(value = "工伤金额")
    private String injuryFee;

    /** 工伤比例 */
    @Excel(name = "工伤比例")
    @TableField("injury_rate")
    @ApiModelProperty(value = "工伤比例")
    private String injuryRate;

    /** 医保人数 */
    @Excel(name = "医保人数")
    @TableField("medical_people")
    @ApiModelProperty(value = "医保人数")
    private String medicalPeople;

    /** 医保基数 */
    @Excel(name = "医保基数")
    @TableField("medical_base")
    @ApiModelProperty(value = "医保基数")
    private String medicalBase;

    /** 医保缴纳金额 */
    @Excel(name = "医保缴纳金额")
    @TableField("medical_fee")
    @ApiModelProperty(value = "医保缴纳金额")
    private String medicalFee;

    /** 医保比例 */
    @Excel(name = "医保比例")
    @TableField("medical_rate")
    @ApiModelProperty(value = "医保比例")
    private String medicalRate;

    /** 公积金人数 */
    @Excel(name = "公积金人数")
    @TableField("fund_people")
    @ApiModelProperty(value = "公积金人数")
    private String fundPeople;

    /** 公积金基数 */
    @Excel(name = "公积金基数")
    @TableField("fund_base")
    @ApiModelProperty(value = "公积金基数")
    private String fundBase;

    /** 公积金缴纳金额 */
    @Excel(name = "公积金缴纳金额")
    @TableField("fund_fee")
    @ApiModelProperty(value = "公积金缴纳金额")
    private String fundFee;

    /** 公积金比例 */
    @Excel(name = "公积金比例")
    @TableField("fund_rate")
    @ApiModelProperty(value = "公积金比例")
    private String fundRate;

    /** 社保人员 */
    @Excel(name = "社保人员")
    @TableField("social_security_contact")
    @ApiModelProperty(value = "社保人员")
    private String socialSecurityContact;

    /** 医保人员 */
    @Excel(name = "医保人员")
    @TableField("medical_contact")
    @ApiModelProperty(value = "医保人员")
    private String medicalContact;

    /** 公积金人员 */
    @Excel(name = "公积金人员")
    @TableField("fund_contact")
    @ApiModelProperty(value = "公积金人员")
    private String fundContact;



}
