package com.bxm.customer.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bxm.common.core.annotation.Excel;
import com.bxm.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@ApiModel("客户操作记录表")
@Accessors(chain = true)
@TableName("c_customer_operator_record")
public class CustomerOperatorRecord extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键id */
    @TableId(value = "id", type = IdType.AUTO)
    @Excel(name = "主键id")
    @ApiModelProperty(value = "主键id")
    private Long id;

    @TableField(value = "customer_service_id")
    @Excel(name = "客户服务id")
    @ApiModelProperty(value = "客户服务id")
    private Long customerServiceId;

    @TableField(value = "operator_type")
    @Excel(name = "操作类型，1-分派会计")
    @ApiModelProperty(value = "操作类型，1-分派会计")
    private Integer operatorType;

    @TableField(value = "operator_month")
    @Excel(name = "操作年月")
    @ApiModelProperty(value = "操作年月")
    private Integer operatorMonth;

    @TableField(value = "is_done")
    @Excel(name = "是否发送同步消息，0-否，1-是")
    @ApiModelProperty(value = "是否发送同步消息，0-否，1-是")
    private Boolean isDone;
}
