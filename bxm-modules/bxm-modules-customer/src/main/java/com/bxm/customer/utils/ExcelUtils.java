package com.bxm.customer.utils;

import com.bxm.common.core.utils.poi.ExcelUtil;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.util.List;
import java.util.Map;

public class ExcelUtils {

    public static <T> List<T> parseExcelFile(MultipartFile file, Class<T> clazz) throws Exception {
        ExcelUtil<T> util = new ExcelUtil<>(clazz);
        return util.importExcel(file.getInputStream());
    }

    public static <T> void exportExcelFile(HttpServletResponse httpServletResponse, List<T> data, Class<T> clazz, String fileName) {
        ExcelUtil<T> util = new ExcelUtil<>(clazz);
        util.exportExcel(httpServletResponse, data, fileName);
    }

    public static void exportExcel(Map<String, List<?>> data, Map<String, Class<?>> sheetClassMap, String fileName, HttpServletResponse response) throws IOException {
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition", "attachment;filename=" + fileName);
        OutputStream out = response.getOutputStream();

        Workbook workbook = new SXSSFWorkbook();
        for (Map.Entry<String, Class<?>> entry : sheetClassMap.entrySet()) {
            String sheetName = entry.getKey();
            Class<?> clazz = entry.getValue();
            List<?> sheetData = data.get(sheetName);
            if (clazz != null && sheetData != null) {
                ExcelUtil.createSheet(sheetName, sheetData, clazz, workbook);
            }
        }

        workbook.write(out);
        out.flush();
        workbook.close();
    }

    public static Workbook exportExcelZip(Map<String, List<?>> data, Map<String, Class<?>> sheetClassMap) throws IOException {
        Workbook workbook = new SXSSFWorkbook();
        for (Map.Entry<String, Class<?>> entry : sheetClassMap.entrySet()) {
            String sheetName = entry.getKey();
            Class<?> clazz = entry.getValue();
            List<?> sheetData = data.get(sheetName);
            if (clazz != null && sheetData != null) {
                ExcelUtil.createSheet(sheetName, sheetData, clazz, workbook);
            }
        }
        return workbook;
    }
}
