package com.bxm.customer.domain.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ReportDeductionResultVO {

    private String startTime;

    private String endTime;

    private List<Map<String, String>> shenBaoList;

    private List<Map<String, String>> kouKuanList;
}
