package com.bxm.customer.task;

import com.bxm.customer.service.IWorkOrderService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class WorkOrderCloseTask {

    @Autowired
    private IWorkOrderService workOrderService;

    @XxlJob("workOrderCloseTask")
    public ReturnT<String> workOrderCloseTask(String param) {
        log.info("工单超时关闭任务开始=============");
        workOrderService.workOrderCloseTask();
        log.info("工单超时关闭任务结束=============");
        return ReturnT.SUCCESS;
    }
}
