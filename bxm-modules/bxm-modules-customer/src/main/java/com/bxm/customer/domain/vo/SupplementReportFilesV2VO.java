package com.bxm.customer.domain.vo;

import com.bxm.common.core.web.domain.CommonFileVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SupplementReportFilesV2VO {

    @ApiModelProperty("交付单id")
    private Long id;

    /** 申报备注 */
    @ApiModelProperty(value = "操作备注")
    private String remark;

    @ApiModelProperty("汇算清缴标准附件")
    private List<CommonFileVO> huisuanqingjiaoFiles;

    @ApiModelProperty("已缴款标准附件")
    private List<CommonFileVO> yijiaokuanFiles;

    @ApiModelProperty("待缴款标准附件")
    private List<CommonFileVO> daijiaokuanFiles;

    @ApiModelProperty("其他交付附件")
    private List<CommonFileVO> otherDeliverFiles;

    @ApiModelProperty(hidden = true)
    private Long deptId;

    @ApiModelProperty(hidden = true)
    private Long userId;

    @ApiModelProperty(hidden = true)
    private String operName;

    @ApiModelProperty(hidden = true)
    private Boolean isCoverFiles;
}
