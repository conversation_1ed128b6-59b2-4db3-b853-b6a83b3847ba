package com.bxm.customer.domain.dto;

import com.bxm.common.core.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CustomerServiceBankBusinessDTO {

    @Excel(name = "客户名")
    private String customerName;

    @Excel(name = "信用代码")
    private String creditCode;

    @Excel(name = "业务归属集团")
    private String businessTopDeptName;

    @Excel(name = "业务归属公司")
    private String businessDeptName;

    @Excel(name = "顾问小组")
    private String advisorDeptName;

    @Excel(name = "顾问名")
    private String advisorEmployeeName;

    @Excel(name = "银行名")
    private String bankName;

    @Excel(name = "银行账号")
    private String bankAccountNumber;

    @Excel(name = "开户行")
    private String depositName;

    @Excel(name = "密码")
    private String password;

    @Excel(name = "手机号")
    private String phoneNumber;

    @Excel(name = "开户时间", width = 30)
    private String accountOpenDate;

    @Excel(name = "销户时间", width = 30)
    private String accountCloseDate;

    @Excel(name = "回单卡账号")
    private String receiptAccountNumber;

    @Excel(name = "回单卡托管")
    private String receiptStatus;

    @Excel(name = "银企直连")
    private String bankDirect;

    @Excel(name = "备注说明")
    private String remarks;

    @Excel(name = "创建时间", width = 30)
    private String createTime;

    @Excel(name = "最近更新时间", width = 30)
    private String updateTime;
}
