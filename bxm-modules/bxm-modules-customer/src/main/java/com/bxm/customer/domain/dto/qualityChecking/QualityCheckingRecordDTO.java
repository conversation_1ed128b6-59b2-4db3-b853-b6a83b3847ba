package com.bxm.customer.domain.dto.qualityChecking;

import com.bxm.common.core.annotation.Excel;
import com.bxm.common.core.web.domain.CommonFileVO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class QualityCheckingRecordDTO {

    @ApiModelProperty("批次号")
    private String batchNo;

    @ApiModelProperty("质检记录id")
    private Long qualityCheckingRecordId;

    @ApiModelProperty("客户id")
    private Long customerServiceId;

    @ApiModelProperty("账期id")
    private Long customerServicePeriodMonthId;

    @ApiModelProperty("客户名称")
    @Excel(name = "客户名")
    private String customerName;

    @ApiModelProperty("信用代码")
    @Excel(name = "信用代码")
    private String creditCode;

    @ApiModelProperty("账期，yyyyMM")
    private Integer period;

    @ApiModelProperty("账期，yyyy-MM")
    @Excel(name = "账期")
    private String periodStr;

    @ApiModelProperty("账期集团id")
    private Long periodBusinessTopDeptId;

    @ApiModelProperty("账期集团名称")
    @Excel(name = "账期集团")
    private String periodBusinessTopDeptName;

    @ApiModelProperty("账期业务公司id")
    private Long periodBusinessDeptId;

    @ApiModelProperty("账期业务公司名称")
    @Excel(name = "账期业务公司")
    private String periodBusinessDeptName;

    @ApiModelProperty("账期顾问小组id")
    private Long periodAdvisorDeptId;

    @ApiModelProperty("账期顾问小组名称")
    @Excel(name = "账期顾问小组")
    private String periodAdvisorDeptName;

    @ApiModelProperty("账期顾问名称")
    @Excel(name = "账期顾问名")
    private String periodAdvisorEmployeeName;

    @ApiModelProperty("账期会计区域id")
    private Long periodAccountingTopDeptId;

    @ApiModelProperty("账期会计区域名称")
    @Excel(name = "账期会计区域")
    private String periodAccountingTopDeptName;

    @ApiModelProperty("账期会计小组id")
    private Long periodAccountingDeptId;

    @ApiModelProperty("账期会计小组名称")
    @Excel(name = "账期会计小组")
    private String periodAccountingDeptName;

    @ApiModelProperty("账期会计名称")
    @Excel(name = "账期会计名")
    private String periodAccountingEmployeeName;

    @ApiModelProperty("账期标签")
    @Excel(name = "账期标签")
    private String periodTags;

    @ApiModelProperty("账期纳税人性质")
    private Integer periodTaxType;

    @ApiModelProperty("账期纳税人性质名称")
    @Excel(name = "账期纳税人性质")
    private String periodTaxTypeName;

    @ApiModelProperty("质检类型，1-账务问题，2-风险提示")
    private Integer qualityCheckingType;

    @ApiModelProperty("质检类型名称")
    @Excel(name = "质检类型")
    private String qualityCheckingTypeName;

    @ApiModelProperty("质检事项id")
    private Long qualityCheckingItemId;

    @ApiModelProperty("质检事项名称")
    @Excel(name = "质检事项")
    private String qualityCheckingItemName;

    @ApiModelProperty("质检周期，1-单期，2-累计")
    private Integer qualityCheckingCycle;

    @ApiModelProperty("质检周期名称")
    @Excel(name = "质检周期")
    private String qualityCheckingCycleName;

    @ApiModelProperty("记录状态，1-进行中，2-已完成，3-超时关闭，4-已关闭，5-失败关闭")
    private Integer status;

    @ApiModelProperty("记录状态名称")
    @Excel(name = "记录状态")
    private String statusName;

    @ApiModelProperty("执行结果，1-正常，2-异常")
    private Integer checkingResult;

    @ApiModelProperty("执行结果名称")
    @Excel(name = "执行结果")
    private String checkingResultName;

    @ApiModelProperty("发起人")
    @Excel(name = "发起人")
    private String createUserName;

    @ApiModelProperty("附件")
    private List<CommonFileVO> files;

    @ApiModelProperty("发起时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Excel(name = "发起时间", dateFormat = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime publishTime;

    @ApiModelProperty("回调时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Excel(name = "回调时间", dateFormat = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime callbackTime;
}
