package com.bxm.customer.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bxm.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.bxm.common.core.web.domain.BaseEntity;

/**
 * 会计对象 c_business_accounting
 * 
 * <AUTHOR>
 * @date 2024-05-08
 */
@Data
@ApiModel("会计对象")
@TableName("c_business_accounting")
public class CBusinessAccounting extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    @TableId(value = "id", type = IdType.AUTO)
    @Excel(name = "id")
    @ApiModelProperty(value = "id")
    private Long id;

    /** 业务类型，1-客户服务 */
    @Excel(name = "业务类型，1-客户服务")
    @TableField("business_type")
    @ApiModelProperty(value = "业务类型，1-客户服务")
    private Integer businessType;

    /** 业务id */
    @Excel(name = "业务id")
    @TableField("business_id")
    @ApiModelProperty(value = "业务id")
    private Long businessId;

    /** 用户id */
    @Excel(name = "用户id")
    @TableField("user_id")
    @ApiModelProperty(value = "用户id")
    private Long userId;

    /** 员工id */
    @Excel(name = "员工id")
    @TableField("employee_id")
    @ApiModelProperty(value = "员工id")
    private Long employeeId;

    /** 部门id */
    @Excel(name = "部门id")
    @TableField("dept_id")
    @ApiModelProperty(value = "部门id")
    private Long deptId;

}
