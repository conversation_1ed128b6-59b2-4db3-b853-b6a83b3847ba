package com.bxm.customer.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CustomerDeliverModifyDdlVO {

    @ApiModelProperty("选中的交付id列表")
    private List<Long> ids;

    @ApiModelProperty("ddl，yyyy-MM-dd")
    private String ddl;
}
