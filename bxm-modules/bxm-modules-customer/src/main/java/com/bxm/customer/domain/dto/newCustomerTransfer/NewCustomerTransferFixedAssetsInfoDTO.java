package com.bxm.customer.domain.dto.newCustomerTransfer;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class NewCustomerTransferFixedAssetsInfoDTO {

    @ApiModelProperty("一次性税前扣除固定资产名称")
    private String assetName;

    @ApiModelProperty("发生年份")
    private String occurrenceYear;
}
