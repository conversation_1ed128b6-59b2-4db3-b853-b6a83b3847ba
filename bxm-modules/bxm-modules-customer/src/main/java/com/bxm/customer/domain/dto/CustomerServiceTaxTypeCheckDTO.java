package com.bxm.customer.domain.dto;

import com.bxm.customer.domain.vo.CustomerServiceTaxTypeCheckVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CustomerServiceTaxTypeCheckDTO {

    @ApiModelProperty("客户服务id")
    private Long customerServiceId;

    @ApiModelProperty("税务信息相关字段")
    private CustomerServiceFinanceTaxInfoDTO taxInfo;

    @ApiModelProperty("税种列表")
    private List<CustomerServiceTaxTypeCheckVO> taxTypes;
}
