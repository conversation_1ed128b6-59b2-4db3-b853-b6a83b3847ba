package com.bxm.customer.task;

import com.bxm.customer.service.ICCustomerServiceService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class RemoveColumnBlankTask {

    @Autowired
    private ICCustomerServiceService customerServiceService;

    @XxlJob("removeColumnBlankTask")
    public ReturnT<String> removeColumnBlankTask(String param) {
        log.info("去除表字段中的空格，回车任务开始=============");
        customerServiceService.removeColumnBlankTask();
        log.info("去除表字段中的空格，回车任务开始=============");
        return ReturnT.SUCCESS;
    }
}
