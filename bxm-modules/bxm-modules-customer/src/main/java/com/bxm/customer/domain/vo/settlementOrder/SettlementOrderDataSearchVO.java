package com.bxm.customer.domain.vo.settlementOrder;

import com.bxm.common.core.web.domain.BaseVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import net.minidev.json.annotate.JsonIgnore;

@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SettlementOrderDataSearchVO extends BaseVO {

    @ApiModelProperty("结算单批次号")
    private String batchNo;

    @ApiModelProperty("结算单id")
    private Long settlementOrderId;

    @ApiModelProperty("结算单标题")
    private String settlementOrderTitle;

    @ApiModelProperty("结算类型，1-入账结算，2-新户预收，3-账期任务")
    private Integer settlementType;

    @ApiModelProperty("客户名")
    private String customerName;

    @ApiModelProperty("服务标签是否包含，0-不包含，1-包含")
    private Integer customerServiceTagIncludeFlag;

    @ApiModelProperty("服务标签关键字")
    private String customerServiceTagName;

    private Integer customerServiceTagType;

    private String customerServiceTagNames;

    private Integer customerTagCount;

    @ApiModelProperty("服务纳税人性质，1-小规模，2-一般纳税人")
    private Integer customerServiceTaxType;

    @ApiModelProperty("服务顾问小组id")
    private Long customerServiceAdvisorDeptId;

    @ApiModelProperty("服务会计小组id")
    private Long customerServiceAccountingDeptId;

    @ApiModelProperty("账期开始，yyyy-MM")
    private String periodStart;

    @ApiModelProperty("账期结束，yyyy-MM")
    private String periodEnd;

    @ApiModelProperty("账期类型，1-代账，2-补账")
    private Integer periodServiceType;

    @ApiModelProperty("账期纳税人性质，1-小规模，2-一般纳税人")
    private Integer periodTaxType;

    @ApiModelProperty("账期标签是否包含，0-不包含，1-包含")
    private Integer periodTagIncludeFlag;

    @ApiModelProperty("账期标签关键字")
    private String periodTagName;

    private Integer periodTagType;

    private String periodTagNames;

    private Integer periodTagCount;

    @ApiModelProperty("账期顾问小组id")
    private Long periodAdvisorDeptId;

    @ApiModelProperty("账期会计小组id")
    private Long periodAccountingDeptId;

    @ApiModelProperty("账务状态，1-正常，2-无需做账")
    private Integer periodAccountingStatus;

    @ApiModelProperty("入账交付结果：1-正常、2-无需交付、3-异常")
    private Integer periodInAccountDeliverResult;

    @ApiModelProperty(value = "入账结果，0-空，1-正常2-异常3-无需交付4-无账务")
    private String periodInAccountResult;

    @ApiModelProperty(value = "银行流水录入结果，0-空，1-正常2-异常3-无需交付4-已开户无流水5-未开户6-银行部分缺")
    private String periodBankPaymentInputResult;

    @ApiModelProperty(value = "入账状态，0-空，1-待交付，2-已完成，3-异常，4-待重提，6-交付待提交")
    private String periodInAccountStatus;

    @ApiModelProperty("银行流水录入日期开始，yyyy-MM-dd")
    private String periodInAccountBankPaymentInputTimeStart;

    @ApiModelProperty("银行流水录入日期结束，yyyy-MM-dd")
    private String periodInAccountBankPaymentInputTimeEnd;

    @ApiModelProperty("入账时间开始，yyyy-MM-dd")
    private String periodInAccountInTimeStart;

    @ApiModelProperty("入账时间结束，yyyy-MM-dd")
    private String periodInAccountInTimeEnd;

    @ApiModelProperty("结账时间开始，yyyy-MM-dd")
    private String periodInAccountEndTimeStart;

    @ApiModelProperty("结账时间结束，yyyy-MM-dd")
    private String periodInAccountEndTimeEnd;

    @ApiModelProperty("业务公司id")
    private Long businessDeptId;

    @ApiModelProperty("服务首个账期开始，yyyy-MM，如2024-09")
    private String customerServiceFirstPeriodStart;

    @ApiModelProperty("服务首个账期结束，yyyy-MM，如2024-09")
    private String customerServiceFirstPeriodEnd;

    @ApiModelProperty("创建人小组id")
    private Long createDeptId;

    @ApiModelProperty("创建时间开始，yyyy-MM-dd，如2024-09-01")
    private String customerServiceCreateTimeStart;

    @ApiModelProperty("创建时间结束，yyyy-MM-dd，如2024-09-01")
    private String customerServiceCreateTimeEnd;

    @ApiModelProperty(value = "账期结算状态，1-不可结算，2-待结算，3-结算中，4-已结算")
    private Integer periodSettlementStatus;

    @ApiModelProperty(value = "账期预收状态，1-未预收，2-预收中，3-已预收")
    private Integer periodPrepayStatus;
}
