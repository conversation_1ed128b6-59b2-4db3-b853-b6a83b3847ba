package com.bxm.customer.domain.vo.docHandover;

import com.bxm.customer.domain.dto.docHandover.DocHandoverInstrumentBankDTO;
import com.bxm.customer.domain.dto.docHandover.DocHandoverInstrumentTaxItemDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/7/7 10:50
 * happy coding!
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DocHandoverInstrumentVO {
    @ApiModelProperty("材料id")
    @NotNull
    private Long id;

    @ApiModelProperty(value = "银行票据")
    private List<DocHandoverInstrumentBankDTO> bankInstruments;

    @ApiModelProperty(value = "有无 税号发票:1-有/0-无")
    @NotNull
    private Integer hasTaxTicket;

    @ApiModelProperty(value = "税号发票")
    private List<DocHandoverInstrumentTaxItemDTO> taxInstruments;

    @ApiModelProperty(value = "有无 其他票据:1-有/0-无")
    @NotNull
    private Integer hasOtherTicket;

    @ApiModelProperty(value = "其他票据")
    private List<DocHandoverInstrumentTaxItemDTO> otherInstruments;
}
