package com.bxm.customer.domain.dto.settlementOrder;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SettlementPushReviewDTO {

    @ApiModelProperty("业务集团id")
    private Long businessTopDeptId;

    @ApiModelProperty("业务集团名称")
    private String businessTopDeptName;

    @ApiModelProperty("业务公司id")
    private Long businessDeptId;

    @ApiModelProperty("业务公司名称")
    private String businessDeptName;

    @ApiModelProperty("结算单数量")
    private Long settlementOrderCount;

    @ApiModelProperty("结算单总金额")
    private BigDecimal totalPrice;

    @ApiModelProperty("结算单优惠金额")
    private BigDecimal discountPrice;

    @ApiModelProperty("预存抵扣金额")
    private BigDecimal deductionPrice;

    @ApiModelProperty("应收金额")
    private BigDecimal oughtPrice;

    @ApiModelProperty("结算单列表")
    private List<SettlementPushReviewOrderDTO> settlementOrderList;
}
