package com.bxm.customer.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDate;

/**
 * 服务银行账号对象 c_customer_service_bank_account
 * 
 * <AUTHOR>
 * @date 2024-07-01
 */
@Data
@Accessors(chain = true)
public class CustomerServiceBankAccountDTO
{
    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "客户服务ID")
    private Long customerServiceId;

    @ApiModelProperty(value = "银行名称")
    private String bankName;

    @ApiModelProperty(value = "银行账号")
    private String bankAccountNumber;

    @ApiModelProperty(value = "密码")
    private String password;

    @ApiModelProperty(value = "手机号")
    private String phoneNumber;

    @ApiModelProperty(value = "开户行")
    private String depositName;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "开户时间,yyyy-MM-dd")
    private LocalDate accountOpenDate;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "销户时间,yyyy-MM-dd")
    private LocalDate accountCloseDate;

    @ApiModelProperty(value = "回单卡账号")
    private String receiptAccountNumber;

    @ApiModelProperty(value = "回单卡状态, 1-已托管, 2-未托管")
    private Integer receiptStatus;

    @ApiModelProperty(value = "银企直连, 1-已开通, 2-未开通")
    private Integer bankDirect;

    @ApiModelProperty(value = "状态, 1-正常, 2-已销户，新增或编辑无需传入")
    private Integer status;

    @ApiModelProperty(value = "备注说明")
    private String remarks;

    @ApiModelProperty("是否税局备案，0-否，1-是")
    private Integer isPutOnTaxRecord;
}
