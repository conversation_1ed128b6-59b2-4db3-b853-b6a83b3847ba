package com.bxm.customer.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bxm.common.core.annotation.Excel;
import com.bxm.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 客户服务收入信息对象 c_customer_service_income_info
 * 
 * <AUTHOR>
 * @date 2024-07-01
 */
@Data
@ApiModel("客户服务收入信息对象")
@Accessors(chain = true)
@TableName("c_customer_service_income_info")
public class CustomerServiceIncomeInfo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @TableId(value = "id", type = IdType.AUTO)
    @Excel(name = "主键ID")
    @ApiModelProperty(value = "主键ID")
    private Long id;

    /** 客户服务ID */
    @Excel(name = "客户服务ID")
    @TableField("customer_service_id")
    @ApiModelProperty(value = "客户服务ID")
    private Long customerServiceId;

    /** 月份 */
    @Excel(name = "月份")
    @TableField("month")
    @ApiModelProperty(value = "月份")
    private Integer month;

    /** 收入金额 */
    @Excel(name = "收入金额")
    @TableField("amount")
    @ApiModelProperty(value = "收入金额")
    private BigDecimal amount;



}
