package com.bxm.customer.domain.dto.inAccount;

import com.baomidou.mybatisplus.annotation.TableField;
import com.bxm.common.core.annotation.Excel;
import com.bxm.common.core.web.domain.CommonFileVO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/7/15 18:50
 * happy coding!
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class InAccountDetailDTO {
    @ApiModelProperty("入账交付id")
    private Long id;

    @ApiModelProperty(value = "客户服务ID")
    private Long customerServiceId;

    @ApiModelProperty(value = "客户名")
    private String customerName;

    @ApiModelProperty(value = "服务类型")
    private Integer serviceType;

    @ApiModelProperty(value = "服务类型 文案")
    private String serviceTypeStr;

    @ApiModelProperty(value = "月度账期id")
    private Long customerServicePeriodMonthId;

    @ApiModelProperty(value = "账期")
    private Integer period;

    @ApiModelProperty(value = "交付结果：1-正常、2-无需交付")
    private Integer deliverResult;

    @Excel(name = "交付结果")
    @ApiModelProperty(value = "交付结果 字符串：1-正常、2-无需交付")
    private String deliverResultStr;

    @ApiModelProperty(value = "入账状态，1未入账、2已入账未结账、3已入账已结账")
    private Integer status;

    @ApiModelProperty(value = "入账状态，字符串")
    private String statusStr;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @Excel(name = "银行流水录入日期", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(value = "银行流水录入日期")
    private LocalDate bankPaymentInputTime;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "入账时间")
    private LocalDate inTime;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "结账时间")
    private LocalDate endTime;

    //****** START 数据
    @ApiModelProperty(value = "本年累计主营收入")
    private BigDecimal majorIncomeTotal;

    @ApiModelProperty(value = "本年累计主营成本")
    private BigDecimal majorCostTotal;

    @ApiModelProperty(value = "本年累计会计利润")
    private BigDecimal profitTotal;

    @ApiModelProperty(value = "个税申报人数")
    private Integer taxReportCount;

    @ApiModelProperty(value = "本年个税申报工资总额")
    private BigDecimal taxReportSalaryTotal;
    //****** END 数据

    @ApiModelProperty(value = "入账备注")
    private String remark;

    @ApiModelProperty("入账附件")
    private List<CommonFileVO> files;

    @Excel(name = "入账附件")
    @ApiModelProperty("入账附件 数量字符串")
    private Integer filesStr;

    @ApiModelProperty(value = "材料缺失：1-已完整、2-缺但齐、3-有缺失待补充、0无材料")
    private Integer wholeLevel;

    @ApiModelProperty(value = "材料缺失 字符串")
    private String wholeLevelStr;

    //****** START RPA数据
    @ApiModelProperty(value = "RPA执行结果：1-成功、0-失败")
    private Integer rpaExeResult;

    @ApiModelProperty(value = "RPA执行结果 字符串：1-成功、0-失败")
    private String rpaExeResultStr;

    @ApiModelProperty(value = "报表状态是否平衡")
    private String tableStatusBalance;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("RPA查询时间")
    private LocalDateTime rpaSearchTime;

    @ApiModelProperty(value = "RPA备注")
    private String rpaRemark;

    @ApiModelProperty("RPA附件")
    private List<CommonFileVO> rpaFiles;

    @ApiModelProperty("RPA附件 数量字符串")
    private Integer rpaFilesStr;

    @ApiModelProperty(value = "入账结果，1-正常2-异常3-无需交付4-无账务")
    private Integer inAccountResult;

    @ApiModelProperty(value = "入账结果 字符串")
    private String inAccountResultStr;

    @ApiModelProperty(value = "银行流水录入结果,1-正常2-异常3-无需交付4-已开户无流水5-未开户6-银行部分缺")
    private Integer bankPaymentInputResult;

    @ApiModelProperty(value = "银行流水录入结果 字符串")
    private String bankPaymentInputResultStr;
    //****** END RPA数据
}
