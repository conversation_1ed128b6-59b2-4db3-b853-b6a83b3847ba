package com.bxm.customer.domain.dto.workBench;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class QualityExceptionStatisticDTO {

    @ApiModelProperty("质检类型")
    private Integer qualityCheckingType;

    @ApiModelProperty("异常数量")
    private Long exceptionCount;
}
