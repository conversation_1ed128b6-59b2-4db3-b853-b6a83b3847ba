package com.bxm.customer.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DeliverBatchDTO {

    @ApiModelProperty("交付类型，1-医保，2-社保，3-个税（工资薪金），4-国税，5-预认证")
    private Integer deliverType;

    @ApiModelProperty("操作类型，1-新建，2-申报，3-解除申报异常，4-扣款，5-解除扣款异常，6-补充，7-认证，8-解除认证异常")
    private Integer operType;

    @ApiModelProperty("账期")
    private Integer period;
}
