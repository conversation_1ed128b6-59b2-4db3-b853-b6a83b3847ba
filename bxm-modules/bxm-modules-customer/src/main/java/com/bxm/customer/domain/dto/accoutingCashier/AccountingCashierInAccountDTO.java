package com.bxm.customer.domain.dto.accoutingCashier;

import com.bxm.common.core.web.domain.CommonFileVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AccountingCashierInAccountDTO {

    @ApiModelProperty(value = "本年累计主营收入")
    private String majorIncomeTotal;

    @ApiModelProperty(value = "本年累计主营成本")
    private String majorCostTotal;

    @ApiModelProperty(value = "本年累计会计利润")
    private String profitTotal;

    /** 本年费用调增 */
    @ApiModelProperty(value = "本年费用调增")
    private String priorYearExpenseIncrease;

    /** 个税申报人数 */
    @ApiModelProperty(value = "个税申报人数")
    private Integer taxReportCount;

    @ApiModelProperty(value = "本年个税申报工资总额")
    private String taxReportSalaryTotal;

    @ApiModelProperty(value = "报表状态是否平衡,0-否，1-是")
    private String tableStatusBalance;

    @ApiModelProperty(value = "报表状态是否平衡")
    private String tableStatusBalanceStr;

    @ApiModelProperty(value = "RPA执行结果，1-成功，0-失败")
    private Integer rpaExeResult;

    @ApiModelProperty(value = "RPA执行结果")
    private String rpaExeResultStr;

    @ApiModelProperty(value = "RPA查询时间")
    private String rpaSearchTimeStr;

    @ApiModelProperty(value = "RPA备注")
    private String rpaRemark;

    @ApiModelProperty(value = "RPA附件")
    private List<CommonFileVO> rpaFiles;
}
