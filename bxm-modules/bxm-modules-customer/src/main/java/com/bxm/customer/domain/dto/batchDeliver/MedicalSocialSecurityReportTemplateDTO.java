package com.bxm.customer.domain.dto.batchDeliver;

import com.bxm.common.core.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MedicalSocialSecurityReportTemplateDTO {

    @Excel(name = "企业名")
    private String customerName;

    @Excel(name = "信用代码")
    private String creditCode;

    @Excel(name = "医保申报结果：成功/异常")
    private String medicalReportResult;

    @Excel(name = "医保本期")
    private String medicalCurrentPeriodAmount;

    @Excel(name = "医保往期")
    private String medicalSupplementAmount;

    @Excel(name = "医保滞纳金")
    private String medicalOverdueAmount;

    @Excel(name = "医保申报备注")
    private String medicalReportRemark;

    @Excel(name = "社保申报结果：成功/异常")
    private String socialReportResult;

    @Excel(name = "社保本期")
    private String socialCurrentPeriodAmount;

    @Excel(name = "社保往期")
    private String socialSupplementAmount;

    @Excel(name = "社保滞纳金")
    private String socialOverdueAmount;

    @Excel(name = "社保申报备注")
    private String socialReportRemark;
}
