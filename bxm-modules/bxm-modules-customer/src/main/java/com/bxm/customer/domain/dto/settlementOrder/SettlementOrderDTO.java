package com.bxm.customer.domain.dto.settlementOrder;

import com.bxm.common.core.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SettlementOrderDTO {

    @ApiModelProperty("结算单id")
    private Long id;

    @ApiModelProperty("账单编号")
    @Excel(name = "账单编号")
    private String billNo;

    @ApiModelProperty("账单标题")
    @Excel(name = "账单标题")
    private String billTitle;

    @ApiModelProperty("结算单标题")
    @Excel(name = "结算单标题")
    private String settlementTitle;

    @ApiModelProperty("业务集团id")
    private Long businessTopDeptId;

    @ApiModelProperty("业务集团名称")
    @Excel(name = "业务集团")
    private String businessTopDeptName;

    @ApiModelProperty("业务公司id")
    private Long businessDeptId;

    @ApiModelProperty("业务公司名称")
    @Excel(name = "业务公司")
    private String businessDeptName;

    @ApiModelProperty("结算类型，1-入账结算，2-新户预收")
    private Integer settlementType;

    @ApiModelProperty("结算类型，列表上直接展示这个字段即可")
    @Excel(name = "结算类型")
    private String settlementTypeName;

    @ApiModelProperty("单位")
    @Excel(name = "数据单位")
    private String unit;

    @ApiModelProperty("总数据")
    @Excel(name = "总数据")
    private Long dataCount;

    @ApiModelProperty("单价")
    private BigDecimal price;

    @ApiModelProperty("单价，去零")
    @Excel(name = "单价")
    private String priceStr;

    @ApiModelProperty("总金额")
    private BigDecimal totalPrice;

    @ApiModelProperty("总金额，去零")
    @Excel(name = "总金额")
    private String totalPriceStr;

    @ApiModelProperty("优惠金额")
    private BigDecimal discountPrice;

    @ApiModelProperty("优惠金额，去零")
    @Excel(name = "优惠金额")
    private String discountPriceStr;

    @ApiModelProperty("结算金额")
    private BigDecimal settlementPrice;

    @ApiModelProperty("结算金额，去零")
    @Excel(name = "结算金额")
    private String settlementPriceStr;

    @ApiModelProperty("创建时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @ApiModelProperty("创建时间，格式化后的结果")
    @Excel(name = "创建时间")
    private String createTimeStr;

    @ApiModelProperty("状态,1-已创建待推送，2-结算中，3-已驳回，4-已撤回，5-已确认")
    private Integer status;

    @ApiModelProperty("状态，列表上直接展示这个字段即可")
    @Excel(name = "状态")
    private String statusName;

    @ApiModelProperty("是否待修改，0-否，1-是")
    private Boolean isWaitForEdit;

    @ApiModelProperty("是否补差，0-否，1-是")
    private Boolean isSupplement;
}
