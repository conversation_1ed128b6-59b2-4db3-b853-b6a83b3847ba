package com.bxm.customer.domain.dto.workBench;

import com.bxm.common.core.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CustomerServicePeriodMonthNoAdvisorDTO {

    @ApiModelProperty("客户名")
    @Excel(name = "客户名")
    private String customerName;

    @ApiModelProperty("信用代码")
    @Excel(name = "信用代码")
    private String creditCode;

    @ApiModelProperty("账期")
    @Excel(name = "账期")
    private String periodStr;

    @ApiModelProperty("服务状态名称")
    @Excel(name = "服务状态")
    private String serviceStatusStr;

    @ApiModelProperty("服务顾问")
    @Excel(name = "服务顾问")
    private String customerServiceAdvisorDeptInfo;
}
