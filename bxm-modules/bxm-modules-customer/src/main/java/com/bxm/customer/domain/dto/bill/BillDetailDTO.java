package com.bxm.customer.domain.dto.bill;

import com.bxm.common.core.web.domain.CommonFileVO;
import com.bxm.customer.domain.dto.settlementOrder.SettlementPushReviewOrderDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BillDetailDTO {

    @ApiModelProperty("账单id")
    private Long id;

    @ApiModelProperty("账单编号")
    private String billNo;

    @ApiModelProperty("账单标题")
    private String billTitle;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("附件列表")
    private List<CommonFileVO> files;

    @ApiModelProperty("业务集团id")
    private Long businessTopDeptId;

    @ApiModelProperty("业务集团名称")
    private String businessTopDeptName;

    @ApiModelProperty("业务公司id")
    private Long businessDeptId;

    @ApiModelProperty("业务公司名称")
    private String businessDeptName;

    @ApiModelProperty("状态,0-已推送待确认，1-已驳回，2-已撤回，3-已确认")
    private Integer status;

    @ApiModelProperty("状态名称")
    private String statusName;

    @ApiModelProperty("总金额")
    private BigDecimal totalPrice;

    @ApiModelProperty("优惠金额")
    private BigDecimal discountPrice;

    @ApiModelProperty("预存抵扣金额")
    private BigDecimal deductionPrice;

    @ApiModelProperty("应支付金额")
    private BigDecimal oughtPrice;

    @ApiModelProperty("结算单列表")
    private List<SettlementPushReviewOrderDTO> settlementOrderList;
}
