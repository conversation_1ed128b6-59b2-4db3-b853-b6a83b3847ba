package com.bxm.customer.domain.vo.businessTask.operate;

import com.bxm.common.core.web.domain.CommonFileVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/25 18:38
 * happy coding!
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class HandleExceptionBatchVO {
    @ApiModelProperty("ids")
    @NotEmpty
    private List<Long> ids;

    @ApiModelProperty(value = "处理结果：1-解除异常、0-关闭任务，2-需甲方处理")
    @NotNull
    private Integer handleResult;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty("附件")
    private List<CommonFileVO> files;
}
