package com.bxm.customer.domain.dto.settlementOrder;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SettlementOrderDeptPriceDTO {

    @ApiModelProperty("批次号，查看明细时需要用到")
    private String batchNo;

    @ApiModelProperty("结算结果")
    private List<SettlementBusinessDeptPriceDTO> businessDeptPriceList;
}
