package com.bxm.customer.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CustomerServiceDeptCapacityDTO {

    @ApiModelProperty("当前组别容量")
    private Long capacity;

    @ApiModelProperty("已派户数")
    private Long dispatchCount;

    @ApiModelProperty("还可派户数")
    private Long freeCount;
}
