package com.bxm.customer.domain.dto.borrow;

import com.bxm.common.core.web.domain.CommonFileVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BorrowOrderDetailDTO {

    @ApiModelProperty("借阅单id")
    private Long id;

    @ApiModelProperty("借阅单标题")
    private String title;

    @ApiModelProperty("状态，0-待出站，1-待归还，2-待确认，3-已确认，4-无需归还，5-待重提")
    private Integer status;

    @ApiModelProperty("状态值")
    private String statusStr;

    @ApiModelProperty("借阅数量")
    private Integer borrowAmount;

    @ApiModelProperty("借阅说明")
    private String remark;

    @ApiModelProperty("借阅附件")
    private List<CommonFileVO> files;

    @ApiModelProperty("客户服务id")
    private Long customerServiceId;

    @ApiModelProperty("客户企业名称")
    private String customerName;
}
