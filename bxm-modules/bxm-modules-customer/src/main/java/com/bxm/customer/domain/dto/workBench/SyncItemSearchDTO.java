package com.bxm.customer.domain.dto.workBench;

import com.bxm.common.core.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SyncItemSearchDTO {

    @ApiModelProperty("客户id")
    private Long customerServiceId;

    @ApiModelProperty("账期id")
    private Long customerServicePeriodMonthId;

    @ApiModelProperty("客户名称")
    @Excel(name = "客户名")
    private String customerName;

    @ApiModelProperty("信用代码")
    @Excel(name = "信用代码")
    private String creditCode;

    @ApiModelProperty("纳税人性质名称")
    @Excel(name = "纳税人性质")
    private String taxTypeStr;

    @ApiModelProperty("标签名称")
    @Excel(name = "标签")
    private String tagNames;

    @ApiModelProperty("客户企业名称")
    private String customerCompanyName;

    @ApiModelProperty(value = "税种大类名", hidden = true)
    private String categoryName;

    @ApiModelProperty(value = "税种子类名称", hidden = true)
    private String itemName;

    @ApiModelProperty("税种名称")
    @Excel(name = "税种名")
    private String categoryItemName;

    @ApiModelProperty(hidden = true)
    private String reportPeriodStart;

    @ApiModelProperty(hidden = true)
    private String reportPeriodEnd;

    @ApiModelProperty("税款起止期")
    @Excel(name = "税款起止期")
    private String reportPeriodStartEnd;

    @ApiModelProperty("缴款金额")
    @Excel(name = "缴款金额")
    private String actualPayTaxAmount;

    @ApiModelProperty("申报周期")
    @Excel(name = "申报周期")
    private String reportType;

    @ApiModelProperty("纳税人性质")
    private Integer taxType;

    @ApiModelProperty("顾问小组id")
    private Long advisorDeptId;

    @ApiModelProperty("账期顾问")
    @Excel(name = "账期顾问")
    private String advisorInfo;

    @ApiModelProperty("会计小组id")
    private Long accountingDeptId;

    @ApiModelProperty("账期会计")
    @Excel(name = "账期会计")
    private String accountingInfo;
}
