package com.bxm.customer.domain.dto;

import com.bxm.common.core.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CustomerTaxTypeCheckBusinessDTO
{
    @Excel(name = "客户名")
    private String customerName;

    @Excel(name = "信用代码")
    private String creditCode;

    @Excel(name = "业务归属集团")
    private String businessTopDeptName;

    @Excel(name = "业务归属公司")
    private String businessDeptName;

    @Excel(name = "顾问小组")
    private String advisorDeptName;

    @Excel(name = "顾问名")
    private String advisorEmployeeName;

    @Excel(name = "月报税种")
    private String monthTaxTypes;

    @Excel(name = "季报税种")
    private String quarterTaxTypes;

    @Excel(name = "半年报税种")
    private String halfYearTaxTypes;

    @Excel(name = "年报税种")
    private String annualTaxTypes;

    @Excel(name = "次报税种")
    private String onceTaxTypes;

    @Excel(name = "更新时间")
    private String updateTime;
}
