package com.bxm.customer.domain.vo.settlementOrder;

import com.bxm.common.core.web.domain.BaseVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SettlementOrderSearchVO extends BaseVO {

    @ApiModelProperty("业务公司id")
    private Long businessDeptId;

    @ApiModelProperty("结算类型，1-入账结算，2-新户预收")
    private Integer settlementType;

    @ApiModelProperty("状态，1-已创建待推送，2-结算中，3-已驳回，4-已撤回，5-已确认")
    private Integer status;

    @ApiModelProperty("是否补差，0-否，1-是")
    private Boolean isSupplement;

    @ApiModelProperty("是否待修改，0-否，1-是")
    private Boolean isWaitForEdit;

    @ApiModelProperty("创建时间开始，yyyy-MM-dd")
    private String createTimeStart;

    @ApiModelProperty("创建时间结束，yyyy-MM-dd")
    private String createTimeEnd;

    @ApiModelProperty("账单信息，模糊查询")
    private String billInfo;
}
