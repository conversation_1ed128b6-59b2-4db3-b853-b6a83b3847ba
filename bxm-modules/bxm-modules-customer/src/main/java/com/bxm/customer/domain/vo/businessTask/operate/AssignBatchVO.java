package com.bxm.customer.domain.vo.businessTask.operate;

import com.bxm.common.core.web.domain.CommonFileVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/25 18:09
 * happy coding!
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AssignBatchVO {
    @ApiModelProperty("ids")
    @NotEmpty
    private List<Long> ids;

    @ApiModelProperty(value = "执行人ID")
    @NotNull
    private Long executeUserId;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty("附件")
    private List<CommonFileVO> files;
}
