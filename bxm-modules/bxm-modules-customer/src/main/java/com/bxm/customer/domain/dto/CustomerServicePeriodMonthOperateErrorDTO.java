package com.bxm.customer.domain.dto;

import com.bxm.common.core.annotation.Excel;
import com.bxm.customer.domain.dto.accoutingCashier.CustomerServicePeriodMonthAccountingCashierDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/6/11 21:41
 * happy coding!
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CustomerServicePeriodMonthOperateErrorDTO {
    @ApiModelProperty("账期id")
    private Long id;

    @ApiModelProperty("客户服务id")
    private Long customerServiceId;

    @ApiModelProperty("客户名称")
    @Excel(name = "客户名称")
    private String customerName;

    @ApiModelProperty(value = "信用代码")
    @Excel(name = "信用代码")
    private String creditCode;

    @ApiModelProperty("税号")
    @Excel(name = "税号")
    private String taxNumber;

    @ApiModelProperty("档案编号")
    @Excel(name = "档案编号")
    private String serviceNumber;

    @ApiModelProperty(value = "月账期")
    @Excel(name = "账期")
    private String periodStr;

    @ApiModelProperty("纳税人性质，1-小规模，2-一般纳税人")
    @Excel(name = "纳税人性质", readConverterExp = "1=小规模,2=一般纳税人")
    private Integer taxType;

    @Excel(name = "账期标签")
    private String tagNames;

    private Long businessTopDeptId;

    @ApiModelProperty("业务公司id")
    private Long businessDeptId;

    @ApiModelProperty("业务公司名称")
    @Excel(name = "业务公司")
    private String businessDeptName;

    private String businessTopDeptName;

    @ApiModelProperty("顾问组别id")
    private Long advisorDeptId;

    @ApiModelProperty("顾问组别名称")
    private String advisorDeptName;

    @ApiModelProperty("顾问员工名称")
    private String advisorEmployeeName;

    @ApiModelProperty("顾问信息总和")
    @Excel(name = "顾问小组")
    private String advisorEmployeeNameFull;

    private Long accountingTopDeptId;

    @ApiModelProperty("会计组别id")
    private Long accountingDeptId;

    private String accountingTopDeptName;

    @ApiModelProperty("会计组别名称")
    private String accountingDeptName;

    @ApiModelProperty("会计员工名称")
    private String accountingEmployeeName;

    @ApiModelProperty("会计信息总和")
    @Excel(name = "会计小组")
    private String accountingEmployeeNameFull;

    @Excel(name = "开票金额")
    private String allTicketAmountStr;

    @Excel(name = "医保")
    private String medicalInsuranceStr;

    @Excel(name = "社保")
    private String socialInsuranceStr;

    @Excel(name = "个税（工资薪金）")
    private String taxStr;

    @Excel(name = "个税（经营所得）")
    private String taxOperatingStr;

    @Excel(name = "国税")
    private String nationalTaxStr;

    @Excel(name = "预认证")
    private String preAuthStr;

    @Excel(name = "银行流水")
    @ApiModelProperty(hidden = true)
    private String bankPaymentResultStr;

    @Excel(name = "入账")
    @ApiModelProperty(hidden = true)
    private String inAccountStatusStr;

    @Excel(name = "结账状态")
    @ApiModelProperty("结账")
    private String settleAccountStatusStr;

    @ApiModelProperty("材料数量")
    @Excel(name = "材料")
    private Long materialCount;

    @Excel(name = "预收状态")
    private String prepayStatusStr;

    @ApiModelProperty("结算状态")
    @Excel(name = "结算状态")
    private String settlementStatusStr;

    @ApiModelProperty("月账期")
    private Integer period;

    @ApiModelProperty("标签")
    private List<TagDTO> tagList;

    @ApiModelProperty(value = "服务状态,1=服务中,2=已结束,3=冻结中")
    private Integer serviceStatus;

    @ApiModelProperty(value = "服务状态文案")
    private String serviceStatusStr;

    @ApiModelProperty("账务状态，1-正常，2-无需做账")
    private Integer accountingStatus;

    @ApiModelProperty("账务状态文案")
    private String accountingStatusStr;

    @ApiModelProperty("服务类型")
    private String serviceTypeStr;

    @ApiModelProperty(value = "开票金额")
    private BigDecimal allTicketAmount;

    @ApiModelProperty(value = "预认证")
    private CustomerServiceMonthPeriodItemDTO preAuth;

    @ApiModelProperty(value = "医保")
    private CustomerServiceMonthPeriodItemDTO medicalInsurance;

    @ApiModelProperty(value = "社保")
    private CustomerServiceMonthPeriodItemDTO socialInsurance;

    @ApiModelProperty(value = "个税（工资薪金）")
    private CustomerServiceMonthPeriodItemDTO tax;

    @ApiModelProperty(value = "个税（经营所得）")
    private CustomerServiceMonthPeriodItemDTO taxOperating;

    @ApiModelProperty(value = "国税")
    private CustomerServiceMonthPeriodItemDTO nationalTax;

    @ApiModelProperty("服务类型，1-代账，2-补账")
    private Integer serviceType;

    @ApiModelProperty("结算状态")
    private Integer settlementStatus;

    //异动标签
    @ApiModelProperty("异动标签")
    private List<String> unusualActionTags;

    @ApiModelProperty("预收状态")
    private Integer prepayStatus;

    @ApiModelProperty("银行流水状态，若有链接，需要调用接口/bxmCustomer/accountingCashier/bankPaymentByPeriodId获取银行流水列表")
    private CustomerServicePeriodMonthAccountingCashierDTO bankPaymentAccountingCashier;

    @ApiModelProperty(hidden = true)
    private Integer bankPaymentResult;

    @ApiModelProperty("入账状态，若有链接，直接根据账务id跳转详情")
    private CustomerServicePeriodMonthAccountingCashierDTO inAccountingCashier;

    @ApiModelProperty(hidden = true)
    private Integer inAccountStatus;

    @ApiModelProperty("结账状态，1未入账、2已入账未结账、3已入账已结账")
    private Integer settleAccountStatus;

    private LocalDateTime createTime;

    @Excel(name = "创建时间")
    private String createTimeStr;

    @Excel(name = "错误原因")
    private String errorMsg;

    @ApiModelProperty("银行列表")
    private List<CustomerServiceBankDTO> bankList;

    @ApiModelProperty("银行列表（业务公司）")
    private List<CustomerServiceBankBusinessDTO> bankBusinessList;

    @ApiModelProperty("税种列表")
    private List<CustomerPeriodTaxTypeCheckDTO> taxTypeCheckList;

    @ApiModelProperty("税种列表（业务公司")
    private List<CustomerPeriodTaxTypeCheckBusinessDTO> taxTypeCheckBusinessList;

    @ApiModelProperty("系统账号列表")
    private List<CustomerSysAccountDTO> sysAccountList;

    @ApiModelProperty("系统账号列表（业务公司")
    private List<CustomerSysAccountBusinessDTO> sysAccountBusinessList;
}
