package com.bxm.customer.domain.dto.valueAdded;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * 社保DTO
 *
 * 统一的社保数据传输对象，同时支持Excel导入和导出功能。
 * 继承基类的共同字段（方式、姓名、身份证号、手机号、备注、社保基数、养老、失业、工伤、医疗、生育），
 * 并添加导入时需要的行号字段用于错误定位。
 *
 * <AUTHOR>
 * @date 2025-01-29
 */
@Data
@EqualsAndHashCode(callSuper = true)
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("社保DTO")
public class SocialInsuranceDTO extends BaseDetailExportDTO {

    /** 行号（用于导入时错误定位） */
    @ApiModelProperty(value = "行号")
    private Integer rowNumber;
}
