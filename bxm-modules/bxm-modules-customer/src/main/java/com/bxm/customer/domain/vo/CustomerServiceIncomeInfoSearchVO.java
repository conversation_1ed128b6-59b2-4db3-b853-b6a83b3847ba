package com.bxm.customer.domain.vo;

import com.bxm.common.core.web.domain.BaseVO;
import com.bxm.common.customize.annotation.TimeField;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CustomerServiceIncomeInfoSearchVO extends BaseVO {

    @ApiModelProperty("关键字")
    private String keyWord;

    @ApiModelProperty("客户批量搜索的批次号")
    private String batchNo;

    @ApiModelProperty("服务状态，1-服务中，2-已结束")
    private Integer serviceStatus;

    @ApiModelProperty("标签包含标识，1-包含，0-不包含")
    private Integer tagIncludeFlag;

    @ApiModelProperty("标签名称")
    private String tagName;

    @ApiModelProperty("纳税人性质，1-小规模，2-一般纳税人")
    private Integer taxType;

    @ApiModelProperty("业务公司id")
    private Long businessDeptId;

    @ApiModelProperty("服务顾问小组筛选，多选，数据源/select/customerServiceIncomeInfoDeptSelect")
    private List<Long> customerServiceAdvisorDeptIds;

    @ApiModelProperty("会计区域id")
    private Long accountingTopDeptId;

    @ApiModelProperty("服务会计小组筛选，多选，数据源/select/customerServiceIncomeInfoDeptSelect")
    private List<Long> customerServiceAccountingDeptIds;

    @ApiModelProperty("近12个月收入是否异常，0-否，1-是")
    private Integer this12MonthError;

    @ApiModelProperty("取数更新时间开始，yyyy-MM-dd")
    @TimeField(type = "start")
    private String profitGetTimeStart;

    @ApiModelProperty("取数更新时间结束，yyyy-MM-dd")
    @TimeField(type = "end")
    private String profitGetTimeEnd;

    @ApiModelProperty(hidden = true)
    private Long deptId;

    @ApiModelProperty(hidden = true)
    private Long userId;
}
