package com.bxm.customer.domain.dto;

import com.bxm.common.core.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CustomerDeliverMiniDTO {

    @ApiModelProperty("客户id")
    private Long customerServiceId;

    @ApiModelProperty("账期id")
    private Long customerServicePeriodMonthId;

    @ApiModelProperty("交付单id")
    private Long deliverId;

    @ApiModelProperty("交付单标题")
    private String title;

    @ApiModelProperty("账期")
    private Integer period;

    @ApiModelProperty("客户企业名称")
    private String customerName;

    @ApiModelProperty("客户名称")
    @Excel(name = "客户名称")
    private String customerCompanyName;

    @Excel(name = "信用代码")
    private String creditCode;

    @ApiModelProperty("业务公司id")
    private Long businessDeptId;

    @Excel(name = "账期业务公司")
    private String businessDeptName;

    @Excel(name = "账期")
    private String periodStr;

    @ApiModelProperty("申报税种")
    @Excel(name = "申报税种")
    private String taxCheckType;

    @ApiModelProperty("顾问")
    @Excel(name = "顾问")
    private String advisorInfo;

    private Long advisorDeptId;

    private Integer deliverType;

    private String deliverTypeName;

    private String advisorDeptName;

    @ApiModelProperty("会计")
    @Excel(name = "会计")
    private String accountingInfo;

    private Long accountingDeptId;

    private String accountingDeptName;

    private String advisorEmployeeNames;

    private String accountingEmployeeNames;

    @ApiModelProperty("已绑定企微的会计用户id")
    private List<Long> accountingBindWechatUserIds;

    @ApiModelProperty("已绑定企微的顾问用户id")
    private List<Long> advisorBindWechatUserIds;

    @ApiModelProperty("交付状态，-1，待创建，0-已提交待申报，1-已保存待提交，2-已申报待确认，3-已确认待扣款，4-扣款已保存待提交，5-已扣款，6-待重提，7-申报异常，8-暂不申报，9-扣款异常，10-暂不扣款，101-已提交待补充，102-已补充待认证，103-已认证待确认，104-已确认，105-待重提，106-认证异常，107-暂不认证")
    private Integer deliverStatus;

    @ApiModelProperty("交付状态名称")
    @Excel(name = "交付状态")
    private String deliverStatusStr;

    private BigDecimal reportAmount;

    private BigDecimal totalAmount;

    @ApiModelProperty("扣缴金额")
    private BigDecimal amount;

    @ApiModelProperty("扣缴金额")
    @Excel(name = "扣缴金额")
    private String amountStr;

    @ApiModelProperty("是否交付待变更")
    private Boolean hasChanged;

    @ApiModelProperty("国税无票收入（收入上的）")
    private BigDecimal incomeNoTicketIncome;
}
