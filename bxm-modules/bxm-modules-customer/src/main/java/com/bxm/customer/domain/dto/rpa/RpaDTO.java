package com.bxm.customer.domain.dto.rpa;

import com.bxm.common.core.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RpaDTO {

    @ApiModelProperty("记录id")
    private Long id;

    @ApiModelProperty("客户名")
    @Excel(name = "客户名")
    private String customerName;

    @ApiModelProperty("账期")
    @Excel(name = "账期")
    private Integer period;

    @ApiModelProperty("任务类型")
    @Excel(name = "任务类型")
    private String taskName;

    @ApiModelProperty("发起人")
    @Excel(name = "发起人")
    private String source;

    @ApiModelProperty("rpa处理结果，0=处理中,1=成功,2=失败")
    @Excel(name = "RPA处理结果", readConverterExp = "0=处理中,1=成功,2=失败")
    private Integer rpaDealResult;

    @ApiModelProperty("系统交付结果，1-正常完成，2=失败")
    @Excel(name = "系统交付结果", readConverterExp = "1=正常完成,2=失败")
    private Integer sysDeliverResult;

    @ApiModelProperty("发起时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "发起时间", dateFormat = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @ApiModelProperty("交付时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "交付时间", dateFormat = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime deliverTime;
}
