package com.bxm.customer.domain.dto.materialDeliver;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MaterialRetryAnalysisDTO {

    @ApiModelProperty("总数据")
    private Long totalCount;

    @ApiModelProperty("已完成数量")
    private Long completeCount;

    @ApiModelProperty("解析正常数据")
    private Long successCount;

    @ApiModelProperty("解析失败数据")
    private Long failCount;

    @ApiModelProperty("是否执行完成")
    private Boolean isComplete;
}
