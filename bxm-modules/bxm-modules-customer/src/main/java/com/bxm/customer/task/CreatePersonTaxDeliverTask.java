package com.bxm.customer.task;

import com.bxm.customer.service.ICustomerDeliverService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class CreatePersonTaxDeliverTask {

    @Autowired
    private ICustomerDeliverService customerDeliverService;

    @XxlJob("createPersonTaxDeliverTask")
    public ReturnT<String> createPersonTaxDeliverTask(String param) {
        log.info("每月自动创建个税（工资薪金）交付单任务开始=============");
        String jobParam = XxlJobHelper.getJobParam();
        customerDeliverService.createPersonTaxDeliver(jobParam);
        log.info("每月自动创建个税（工资薪金）交付单任务结束=============");
        return ReturnT.SUCCESS;
    }
}
