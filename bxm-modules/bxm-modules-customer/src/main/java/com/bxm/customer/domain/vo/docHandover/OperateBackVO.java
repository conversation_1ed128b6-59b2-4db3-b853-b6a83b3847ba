package com.bxm.customer.domain.vo.docHandover;

import com.bxm.common.core.web.domain.CommonFileVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/7/7 19:29
 * happy coding!
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OperateBackVO {
    @ApiModelProperty("材料id")
    @NotNull
    private Long id;

    @ApiModelProperty(value = "退回备注")
    private String backRemark;

    @ApiModelProperty("退回附件")
    private List<CommonFileVO> files;
}
