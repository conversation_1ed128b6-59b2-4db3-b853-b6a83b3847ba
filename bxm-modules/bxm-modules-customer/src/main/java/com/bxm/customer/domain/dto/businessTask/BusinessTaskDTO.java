package com.bxm.customer.domain.dto.businessTask;

import com.bxm.common.core.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2024/9/25 17:03
 * happy coding!
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BusinessTaskDTO {
    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty(value = "客户服务ID")
    private Long customerServiceId;

    @Excel(name = "客户名")
    @ApiModelProperty(value = "客户名")
    private String customerName;

    @ApiModelProperty(value = "任务对应的业务的业务ID，账期任务就是账期ID")
    private Long bizId;

    @Excel(name = "账期")
    @ApiModelProperty(value = "任务对应的业务的业务值，冗余过来，账期对应的是账期值")
    private String bizValue;

    @ApiModelProperty(value = "事项，1-银行流水")
    private Integer itemType;

    @Excel(name = "事项")
    @ApiModelProperty(value = "事项，1-银行流水")
    private String itemTypeStr;

    @ApiModelProperty(value = "监管人ID")
    private Long adminUserId;

    @Excel(name = "监管人名称")
    @ApiModelProperty(value = "监管人名称")
    private String adminUserName;

    @ApiModelProperty(value = "执行人ID")
    private Long executeUserId;

    @Excel(name = "执行人名称")
    @ApiModelProperty(value = "执行人名称")
    private String executeUserName;

    @Excel(name = "账期会计")
    @ApiModelProperty("账期会计")
    private String accountingEmployeeNameFull;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "截止日期", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(value = "截止日期")
    private LocalDateTime deadline;

    @ApiModelProperty(value = "任务状态：1-待完成、2-待审核、3-已完结、4-已关闭、5-异常")
    private Integer status;

    @Excel(name = "任务状态")
    @ApiModelProperty(value = "任务状态 文案")
    private String statusStr;

    @ApiModelProperty(value = "完成结果：1-正常完成、2-已开户无流水、3-未开户、4-银行部分缺、5-无需交付、6-无法完成")
    private Integer finishResult;

    @Excel(name = "完成结果")
    @ApiModelProperty(value = "完成结果 文案")
    private String finishResultStr;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "截止日期", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(value = "执行时间")
    private LocalDateTime executeTime;

    @ApiModelProperty(value = "最后操作人")
    private Long lastOperateUserId;

    @Excel(name = "最后操作人姓名")
    @ApiModelProperty(value = "最后操作人姓名")
    private String lastOperateUserName;

    @ApiModelProperty(value = "最后操作类型")
    private Integer lastOperateType;

    @Excel(name = "最后操作类型")
    @ApiModelProperty(value = "最后操作类型 文案")
    private Integer lastOperateTypeStr;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "最后操作时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(value = "最后操作时间")
    private LocalDateTime lastOperateTime;
}
