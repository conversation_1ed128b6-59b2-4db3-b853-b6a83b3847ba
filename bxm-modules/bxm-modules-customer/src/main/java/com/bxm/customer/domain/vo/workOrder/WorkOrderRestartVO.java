package com.bxm.customer.domain.vo.workOrder;

import com.bxm.common.core.web.domain.CommonFileVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class WorkOrderRestartVO {

    @ApiModelProperty("工单id")
    private Long id;

    @ApiModelProperty("处理人类型，1-发起方，2-承接方")
    private Integer dealUserType;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("附件")
    private List<CommonFileVO> files;
}
