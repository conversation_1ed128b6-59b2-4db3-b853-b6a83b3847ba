package com.bxm.customer.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CustomerServiceFrozeVO {

    @ApiModelProperty("选择的服务id列表")
    private List<Long> ids;

    @ApiModelProperty("1-本月开始，2-次月开始")
    private Integer frozePeriod;
}
