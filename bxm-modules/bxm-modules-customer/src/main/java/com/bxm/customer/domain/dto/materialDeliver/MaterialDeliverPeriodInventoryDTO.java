package com.bxm.customer.domain.dto.materialDeliver;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MaterialDeliverPeriodInventoryDTO {

    @ApiModelProperty("账期清单id")
    private Long id;

    @ApiModelProperty("交接单编号")
    private String materialDeliverNumber;

    @ApiModelProperty("客户名")
    private String customerName;

    @ApiModelProperty("交接单类型，1-银行流水，2-普通入账，3-凭票入账")
    private Integer materialDeliverType;

    @ApiModelProperty("交接单类型名称")
    private String materialDeliverTypeName;

    @ApiModelProperty("银行名称")
    private String bankName;

    @ApiModelProperty("银行账号")
    private String bankAccountNumber;

    @ApiModelProperty("银行信息")
    private String bankInfo;

    @ApiModelProperty("账期")
    private Integer period;

    @ApiModelProperty("账期（格式化后的）")
    private String periodStr;

    @ApiModelProperty("文件数量")
    private Long fileCount;

    @ApiModelProperty("推送结果，1-成功，2-失败")
    private Integer pushResult;

    @ApiModelProperty("推送结果名称")
    private String pushResultStr;

    @ApiModelProperty("失败原因")
    private String errorMsg;
}
