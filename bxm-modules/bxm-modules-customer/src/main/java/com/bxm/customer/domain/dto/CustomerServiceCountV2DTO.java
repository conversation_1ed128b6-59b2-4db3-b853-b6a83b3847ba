package com.bxm.customer.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/8/30 23:57
 * happy coding!
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CustomerServiceCountV2DTO {
    @ApiModelProperty("更名待确认")
    private Long waitConfirmChangeNameCount;

    @ApiModelProperty("开票预警")
    private Long excessWarningCount;

    @ApiModelProperty("开票取数异常预警")
    private Long ticketExceptionCount;

    @ApiModelProperty("上期-有效户")
    private Integer validCount;

    @ApiModelProperty("上期-新增")
    private Long newCount;

    @ApiModelProperty("上期-重启")
    private Integer restartCount;

    @ApiModelProperty("上期-移出")
    private Integer endServiceCount;


    @ApiModelProperty("本期-有效户")
    private Integer thisMonthValidCount;

    public static CustomerServiceCountV2DTO noPermission() {
        return CustomerServiceCountV2DTO.builder()
                .endServiceCount(0)
                .excessWarningCount(0L)
                .newCount(0L)
                .restartCount(0)
                .thisMonthValidCount(0)
                .ticketExceptionCount(0L)
                .validCount(0)
                .waitConfirmChangeNameCount(0L)
                .build();
    }
}
