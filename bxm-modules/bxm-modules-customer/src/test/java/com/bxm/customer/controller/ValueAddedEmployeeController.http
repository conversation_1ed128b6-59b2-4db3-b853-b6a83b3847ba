### ValueAddedEmployeeController HTTP测试文件
### 增值员工信息管理接口测试
### 支持三种业务类型：1-社医保，2-个税明细，3-国税账号
###
### 主要功能：
### 1. 员工信息的增删改查操作
### 2. 批量上传Excel文件处理
### 3. 根据业务类型导出不同的Excel模板
###    - bizType=1: 社医保模板（使用SocialInsuranceDTO）
###      字段：方式、姓名、身份证号、手机号、备注、社保基数、养老、失业、工伤、医疗、生育
###    - bizType=2: 个税明细模板（使用PersonalTaxDetailExportDTO）
###      字段：方式、姓名、身份证号、手机号、备注、社保基数、养老、失业、工伤、医疗、生育、应发工资、公积金个人缴存金额、其他

### 环境变量定义
@baseUrl = http://localhost:8085/bxmCustomer
@contentType = application/json
@authorization = Bearer eyJhbGciOiJIUzUxMiJ9.eyJ1c2VyX2lkIjoxLCJ1c2VyX2tleSI6IjExYTY4MDc2LTE5ZDgtNDdmNy04ZWVhLTQzM2JlNWNhMThiMyIsInVzZXJuYW1lIjoiYWRtaW4ifQ.NCiZg2AaDS9WjdPV1DffXxT5gWWaVzik-Qk2M3qeAS8VEjyW-9XNGIB5erI9NH9BuhW70nzhch2nF8fYehddOQ
### ===========================================
### 1. 社医保业务类型测试 (bizType=1)
### ===========================================

### 1.1 社医保-提醒操作 (operationType=1)
POST {{baseUrl}}/valueAddedEmployee/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "DO-2025-001",
  "bizType": 1,
  "entryType": 2,
  "operationType": 1,
  "employeeName": "张三",
  "idNumber": "110101199001011234",
  "mobile": "***********",
  "grossSalary": 8000.00,
  "providentFundPersonal": 800.00,
  "socialInsurancePackage": "{\"yang_lao\":true,\"shi_ye\":true,\"gong_shang\":true,\"yi_liao\":true,\"sheng_yu\":true,\"qi_ta\":false}",
  "status": 1,
  "remark": "社医保提醒操作测试"
}

### 1.2 社医保-更正操作 (operationType=2)
POST {{baseUrl}}/valueAddedEmployee/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "DO-2025-002",
  "bizType": 1,
  "entryType": 1,
  "operationType": 2,
  "employeeName": "李四",
  "idNumber": "110101199002022345",
  "mobile": "13800138002",
  "grossSalary": 9500.00,
  "providentFundPersonal": 950.00,
  "socialInsurancePackage": "{\"yang_lao\":true,\"shi_ye\":true,\"gong_shang\":true,\"yi_liao\":true,\"sheng_yu\":true,\"qi_ta\":false}",
  "status": 2,
  "remark": "社医保更正操作测试"
}

### 1.3 社医保-减员操作 (operationType=3)
POST {{baseUrl}}/valueAddedEmployee/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "DO-2025-003",
  "bizType": 1,
  "entryType": 2,
  "operationType": 3,
  "employeeName": "王五",
  "idNumber": "110101199003033456",
  "mobile": "13800138003",
  "grossSalary": 7500.00,
  "providentFundPersonal": 750.00,
  "socialInsurancePackage": "{\"yang_lao\":true,\"shi_ye\":true,\"gong_shang\":true,\"yi_liao\":true,\"sheng_yu\":true,\"qi_ta\":false}",
  "status": 3,
  "remark": "社医保减员操作测试"
}

### ===========================================
### 2. 个税明细业务类型测试 (bizType=2)
### ===========================================

### 2.1 个税明细-提醒操作 (operationType=1)
POST {{baseUrl}}/valueAddedEmployee/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "DO-2025-004",
  "bizType": 2,
  "entryType": 2,
  "operationType": 1,
  "employeeName": "赵六",
  "idNumber": "110101199004044567",
  "mobile": "13800138004",
  "grossSalary": 12000.00,
  "providentFundPersonal": 1200.00,
  "status": 1,
  "extendInfo": "{\"taxableIncome\":\"10800.00\",\"taxAmount\":\"540.00\"}",
  "remark": "个税明细提醒操作测试"
}

### 2.2 个税明细-更正操作 (operationType=2)
POST {{baseUrl}}/valueAddedEmployee/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "DO-2025-005",
  "bizType": 2,
  "entryType": 1,
  "operationType": 2,
  "employeeName": "孙七",
  "idNumber": "110101199005055678",
  "mobile": "13800138005",
  "grossSalary": 15000.00,
  "providentFundPersonal": 1500.00,
  "status": 2,
  "extendInfo": "{\"taxableIncome\":\"13500.00\",\"taxAmount\":\"945.00\"}",
  "remark": "个税明细更正操作测试"
}

### ===========================================
### 3. 国税账号业务类型测试 (bizType=3)
### ===========================================

### 3.1 国税账号-会计实名 (operationType=1)
POST {{baseUrl}}/valueAddedEmployee/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "DO-2025-006",
  "bizType": 3,
  "entryType": 2,
  "operationType": 1,
  "employeeName": "周八",
  "idNumber": "110101199006066789",
  "mobile": "***********",
  "taxNumber": "91110000123456789X",
  "queryPassword": "password123",
  "status": 1,
  "extendInfo": "{\"accountType\":\"会计实名\",\"certificationDate\":\"2025-01-28\"}",
  "remark": "国税账号会计实名测试"
}

### 3.2 国税账号-异地实名 (operationType=2)
POST {{baseUrl}}/valueAddedEmployee/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "DO-2025-007",
  "bizType": 3,
  "entryType": 1,
  "operationType": 2,
  "employeeName": "吴九",
  "idNumber": "110101199007077890",
  "mobile": "***********",
  "taxNumber": "91110000987654321Y",
  "queryPassword": "password456",
  "status": 1,
  "extendInfo": "{\"accountType\":\"异地实名\",\"remoteLocation\":\"上海\"}",
  "remark": "国税账号异地实名测试"
}

### ===========================================
### 4. 更新操作测试（包含ID字段）
### ===========================================

### 4.1 更新已存在的员工信息
POST {{baseUrl}}/valueAddedEmployee/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "id": 1,
  "deliveryOrderNo": "DO-2025-001",
  "bizType": 1,
  "entryType": 2,
  "operationType": 2,
  "employeeName": "张三（已更新）",
  "idNumber": "110101199001011234",
  "mobile": "***********",
  "grossSalary": 8500.00,
  "providentFundPersonal": 850.00,
  "socialInsurancePackage": "{\"yang_lao\":true,\"shi_ye\":true,\"gong_shang\":true,\"yi_liao\":true,\"sheng_yu\":true,\"qi_ta\":false}",
  "status": 2,
  "remark": "更新操作测试"
}

### ===========================================
### 5. 参数验证失败测试
### ===========================================

### 5.1 缺少必填字段测试
POST {{baseUrl}}/valueAddedEmployee/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "bizType": 1,
  "entryType": 2,
  "operationType": 1,
  "employeeName": "测试用户"
}

### 5.2 身份证号格式错误测试
POST {{baseUrl}}/valueAddedEmployee/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "DO-2025-901",
  "bizType": 1,
  "entryType": 2,
  "operationType": 1,
  "employeeName": "格式错误测试",
  "idNumber": "123456789",
  "mobile": "***********",
  "grossSalary": 8000.00
}

### 5.3 手机号格式错误测试
POST {{baseUrl}}/valueAddedEmployee/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "DO-2025-902",
  "bizType": 1,
  "entryType": 2,
  "operationType": 1,
  "employeeName": "手机号错误测试",
  "idNumber": "110101199001011234",
  "mobile": "12345678901",
  "grossSalary": 8000.00
}

### 5.4 业务类型超出范围测试
POST {{baseUrl}}/valueAddedEmployee/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "DO-2025-903",
  "bizType": 5,
  "entryType": 2,
  "operationType": 1,
  "employeeName": "业务类型错误测试",
  "idNumber": "110101199001011234",
  "mobile": "***********",
  "grossSalary": 8000.00
}

### ===========================================
### 6. 查询接口测试（辅助验证）
### ===========================================

### 6.1 根据交付单编号和身份证号查询员工信息
GET {{baseUrl}}/valueAddedEmployee/getByDeliveryOrderAndIdNumber?deliveryOrderNo=DO-2025-001&idNumber=110101199001011234&bizType=1

### 6.2 根据ID查询员工详情
GET {{baseUrl}}/valueAddedEmployee/getById/1

### ===========================================
### 7. 边界值测试
### ===========================================

### 7.1 最大长度字段测试
POST {{baseUrl}}/valueAddedEmployee/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "DO-2025-801",
  "bizType": 1,
  "entryType": 2,
  "operationType": 1,
  "employeeName": "这是一个非常长的员工姓名用来测试最大长度限制这是一个非常长的员工姓名用来测试最大长度限制这是一个非常长的员工姓名用来测试最大长度限制",
  "idNumber": "110101199001011234",
  "mobile": "***********",
  "grossSalary": 99999999.99,
  "providentFundPersonal": 99999999.99,
  "remark": "这是一个非常长的备注信息用来测试最大长度限制这是一个非常长的备注信息用来测试最大长度限制这是一个非常长的备注信息用来测试最大长度限制这是一个非常长的备注信息用来测试最大长度限制这是一个非常长的备注信息用来测试最大长度限制这是一个非常长的备注信息用来测试最大长度限制这是一个非常长的备注信息用来测试最大长度限制这是一个非常长的备注信息用来测试最大长度限制这是一个非常长的备注信息用来测试最大长度限制这是一个非常长的备注信息用来测试最大长度限制"
}


### 7.2 最小值测试
POST {{baseUrl}}/valueAddedEmployee/upsert
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "DO-2025-802",
  "bizType": 1,
  "entryType": 1,
  "operationType": 1,
  "employeeName": "最小值测试",
  "idNumber": "110101199001011234",
  "mobile": "***********",
  "grossSalary": 0.00,
  "providentFundPersonal": 0.00,
  "status": 1
}


### ===========================================
### 8. Excel模板导出测试 (exportValueAddedEmptyExcelTemplate)
### ===========================================
###
### 接口说明：
### - 路径：GET /valueAddedEmployee/exportValueAddedEmptyExcelTemplate
### - 参数：bizType (必填) - 业务类型：1-社医保，2-个税明细
### - 功能：根据业务类型下载对应的空白Excel模板
### - 返回：Excel文件下载
###
### 业务类型说明：
### - bizType=1: 使用SocialInsuranceDTO生成社保明细模板
###   包含字段：方式、姓名、身份证号、手机号、备注、社保基数、养老、失业、工伤、医疗、生育
### - bizType=2: 使用PersonalTaxDetailExportDTO生成个税明细模板
###   包含字段：方式、姓名、身份证号、手机号、备注、社保基数、养老、失业、工伤、医疗、生育、应发工资、公积金个人缴存金额、其他
###

### 8.1 导出社保明细Excel模板 (bizType=1) - 使用SocialInsuranceDTO
GET {{baseUrl}}/valueAddedEmployee/exportValueAddedEmptyExcelTemplate?bizType=1
Authorization: {{authorization}}

### 8.2 导出个税明细Excel模板 (bizType=2) - 使用PersonalTaxDetailExportDTO
GET {{baseUrl}}/valueAddedEmployee/exportValueAddedEmptyExcelTemplate?bizType=2
Authorization: {{authorization}}

### 8.3 导出模板参数验证失败测试 - 缺少bizType参数
### 预期结果：400 Bad Request - 缺少必需的请求参数
GET {{baseUrl}}/valueAddedEmployee/exportValueAddedEmptyExcelTemplate
Authorization: {{authorization}}

### 8.4 导出模板参数验证失败测试 - bizType参数无效 (bizType=3)
### 预期结果：400 Bad Request - 业务类型参数无效，必须为1（社医保）或2（个税明细）
GET {{baseUrl}}/valueAddedEmployee/exportValueAddedEmptyExcelTemplate?bizType=3
Authorization: {{authorization}}

### 8.5 导出模板参数验证失败测试 - bizType参数无效 (bizType=0)
### 预期结果：400 Bad Request - 业务类型参数无效，必须为1（社医保）或2（个税明细）
GET {{baseUrl}}/valueAddedEmployee/exportValueAddedEmptyExcelTemplate?bizType=0
Authorization: {{authorization}}

### 8.6 导出模板参数验证失败测试 - bizType参数无效 (bizType=abc)
### 预期结果：400 Bad Request - 参数类型转换错误
GET {{baseUrl}}/valueAddedEmployee/exportValueAddedEmptyExcelTemplate?bizType=abc
Authorization: {{authorization}}

### 8.7 导出模板参数验证失败测试 - bizType参数为负数 (bizType=-1)
### 预期结果：400 Bad Request - 业务类型参数无效，必须为1（社医保）或2（个税明细）
GET {{baseUrl}}/valueAddedEmployee/exportValueAddedEmptyExcelTemplate?bizType=-1
Authorization: {{authorization}}

### ===========================================
### 9. 批量上传接口测试 (batchAddSocialInsuranceEmp)
### ===========================================

### 9.1 社医保批量上传测试 (bizType=1) - 使用现有Excel文件
POST {{baseUrl}}/valueAddedEmployee/batchAddSocialInsuranceEmp
Authorization: {{authorization}}
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary7MA4YWxkTrZu0gW

------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="deliveryOrderNo"

DO-2025-001
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="bizType"

1
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="entryType"

1
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="remark"

社医保批量上传测试
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="overrideExisting"

false
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="excelFile"; filename="2025-07-29T230358.200.xlsx"
Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet

< ./2025-07-29T230358.200.xlsx
------WebKitFormBoundary7MA4YWxkTrZu0gW--

### 9.2 个税明细批量上传测试 (bizType=2)
POST {{baseUrl}}/valueAddedEmployee/batchAddSocialInsuranceEmp
Authorization: {{authorization}}
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary7MA4YWxkTrZu0gW

------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="deliveryOrderNo"

DO-2025-004
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="bizType"

2
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="entryType"

1
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="remark"

个税明细批量上传测试
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="overrideExisting"

false
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="excelFile"; filename="2025-07-29T230358.200.xlsx"
Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet

< ./2025-07-29T230358.200.xlsx
------WebKitFormBoundary7MA4YWxkTrZu0gW--

### 9.3 国税账号批量上传测试 (bizType=3)
POST {{baseUrl}}/valueAddedEmployee/batchAddSocialInsuranceEmp
Authorization: {{authorization}}
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary7MA4YWxkTrZu0gW

------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="deliveryOrderNo"

DO-2025-006
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="bizType"

3
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="entryType"

1
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="remark"

国税账号批量上传测试
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="overrideExisting"

false
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="excelFile"; filename="2025-07-29T230358.200.xlsx"
Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet

< ./2025-07-29T230358.200.xlsx
------WebKitFormBoundary7MA4YWxkTrZu0gW--

### 9.4 覆盖现有数据测试 (overrideExisting=true)
POST {{baseUrl}}/valueAddedEmployee/batchAddSocialInsuranceEmp
Authorization: {{authorization}}
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary7MA4YWxkTrZu0gW

------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="deliveryOrderNo"

DO-2025-002
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="bizType"

1
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="entryType"

1
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="remark"

覆盖现有数据测试
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="overrideExisting"

true
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="excelFile"; filename="2025-07-29T230358.200.xlsx"
Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet

< ./2025-07-29T230358.200.xlsx
------WebKitFormBoundary7MA4YWxkTrZu0gW--

### ===========================================
### 10. 批量上传参数验证失败测试
### ===========================================

### 10.1 缺少Excel文件测试
POST {{baseUrl}}/valueAddedEmployee/batchAddSocialInsuranceEmp
Authorization: {{authorization}}
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary7MA4YWxkTrZu0gW

------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="deliveryOrderNo"

DO-2025-901
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="bizType"

1
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="remark"

缺少文件测试
------WebKitFormBoundary7MA4YWxkTrZu0gW--

### 10.2 业务类型超出范围测试 (bizType=5)
POST {{baseUrl}}/valueAddedEmployee/batchAddSocialInsuranceEmp
Authorization: {{authorization}}
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary7MA4YWxkTrZu0gW

------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="deliveryOrderNo"

DO-2025-902
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="bizType"

5
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="remark"

业务类型错误测试
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="excelFile"; filename="2025-07-29T230358.200.xlsx"
Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet

< ./2025-07-29T230358.200.xlsx
------WebKitFormBoundary7MA4YWxkTrZu0gW--

### 10.3 业务类型为0测试 (bizType=0)
POST {{baseUrl}}/valueAddedEmployee/batchAddSocialInsuranceEmp
Authorization: {{authorization}}
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary7MA4YWxkTrZu0gW

------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="deliveryOrderNo"

DO-2025-903
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="bizType"

0
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="remark"

业务类型为0测试
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="excelFile"; filename="2025-07-29T230358.200.xlsx"
Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet

< ./2025-07-29T230358.200.xlsx
------WebKitFormBoundary7MA4YWxkTrZu0gW--

### ===========================================
### 11. 文件处理状态查询测试
### ===========================================

### 11.1 查询文件处理状态 (需要先执行上面的批量上传获取fileId)
### 注意：请将下面的fileId替换为实际的文件ID
GET {{baseUrl}}/valueAddedEmployee/fileProgress/16
Authorization: {{authorization}}

### 11.2 查询不存在的文件状态
GET {{baseUrl}}/valueAddedEmployee/fileProgress/99999
Authorization: {{authorization}}

