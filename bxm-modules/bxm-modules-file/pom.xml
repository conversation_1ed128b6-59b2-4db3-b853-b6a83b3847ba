<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.bxm</groupId>
        <artifactId>bxm-modules</artifactId>
        <version>1.0.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>bxm-modules-file</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <description>
        bxm-modules-file文件服务
    </description>

    <dependencies>
    	
    	<!-- SpringCloud Alibaba Nacos -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.alibaba.nacos</groupId>
                    <artifactId>nacos-client</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.alibaba.nacos</groupId>
            <artifactId>nacos-client</artifactId>
            <version>${nacos.client.version}</version>
        </dependency>
        
        <!-- SpringCloud Alibaba Nacos Config -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>
        
        <!-- SpringCloud Alibaba Sentinel -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-sentinel</artifactId>
        </dependency>
        
        <!-- SpringBoot Actuator -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
		
        <!-- FastDFS -->
        <dependency>
            <groupId>com.github.tobato</groupId>
            <artifactId>fastdfs-client</artifactId>
        </dependency>
        
        <!-- Minio -->
        <dependency>
            <groupId>io.minio</groupId>
            <artifactId>minio</artifactId>
            <version>${minio.version}</version>
        </dependency>
        
        <!-- RuoYi Api System -->
        <dependency>
            <groupId>com.bxm</groupId>
            <artifactId>bxm-api-system</artifactId>
            <version>${bxm.api.system.version}</version>
        </dependency>
        <!-- Swagger UI -->
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger-ui</artifactId>
            <version>${swagger.fox.version}</version>
        </dependency>

        <!-- Mysql Connector -->
        <dependency>
            <groupId>com.mysql</groupId>
            <artifactId>mysql-connector-j</artifactId>
        </dependency>

        <!-- RuoYi Common DataSource -->
        <dependency>
            <groupId>com.bxm</groupId>
            <artifactId>bxm-common-datasource</artifactId>
            <version>${bxm.common.datasource.version}</version>
        </dependency>

        <!-- RuoYi Common DataScope -->
        <dependency>
            <groupId>com.bxm</groupId>
            <artifactId>bxm-common-datascope</artifactId>
            <version>${bxm.common.datascope.version}</version>
        </dependency>

        <!-- RuoYi Common Log -->
        <dependency>
            <groupId>com.bxm</groupId>
            <artifactId>bxm-common-log</artifactId>
            <version>${bxm.common.log.version}</version>
        </dependency>

        <dependency>
            <groupId>com.bxm</groupId>
            <artifactId>bxm-common-mybatisplus</artifactId>
            <version>${bxm.common.mybatisplus.version}</version>
        </dependency>
        
        <!-- RuoYi Common Swagger -->
        <dependency>
            <groupId>com.bxm</groupId>
            <artifactId>bxm-common-swagger</artifactId>
            <version>${bxm.common.swagger.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
            <version>${poi.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <version>${poi.version}</version>
        </dependency>
        <dependency>
            <groupId>com.aliyun.oss</groupId>
            <artifactId>aliyun-sdk-oss</artifactId>
            <version>${aliyun.oss.version}</version>
        </dependency>
        <!-- hutool -->
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>${hutool.all.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bxm</groupId>
            <artifactId>bxm-common-security</artifactId>
            <version>${bxm.common.security.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bxm</groupId>
            <artifactId>bxm-api-customer</artifactId>
            <version>${bxm.api.customer.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bxm</groupId>
            <artifactId>bxm-api-thirdpart</artifactId>
            <version>${bxm.api.thirdpart.version}</version>
        </dependency>
        <dependency>
            <groupId>com.github.junrar</groupId>
            <artifactId>junrar</artifactId>
            <version>${junrar.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-compress</artifactId>
            <version>${commons.compress.version}</version>
        </dependency>
        <dependency>
            <groupId>net.sf.sevenzipjbinding</groupId>
            <artifactId>sevenzipjbinding</artifactId>
            <version>${sevenzipjbinding.version}</version>
        </dependency>
        <dependency>
            <groupId>net.sf.sevenzipjbinding</groupId>
            <artifactId>sevenzipjbinding-all-platforms</artifactId>
            <version>${sevenzipjbinding.all.version}</version>
        </dependency>
    </dependencies>
    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
   
</project>