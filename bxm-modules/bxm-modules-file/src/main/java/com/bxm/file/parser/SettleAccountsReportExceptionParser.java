package com.bxm.file.parser;

import com.bxm.file.bean.dto.batchDeliverV2.SettleAccountsReportExceptionData;
import com.bxm.file.util.ExcelUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@Component
public class SettleAccountsReportExceptionParser implements ExcelV2Parser<SettleAccountsReportExceptionData> {

    @Override
    public List<SettleAccountsReportExceptionData> parse(MultipartFile file) throws Exception {
        return ExcelUtils.parseExcelFile(file, SettleAccountsReportExceptionData.class);
    }
}
