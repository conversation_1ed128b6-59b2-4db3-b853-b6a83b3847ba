package com.bxm.file.parser;

import com.bxm.file.bean.dto.batchDeliverV2.SettleAccountsConfirmData;
import com.bxm.file.util.ExcelUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@Component
public class SettleAnnualConfirmParser implements ExcelV2Parser<SettleAccountsConfirmData> {

    @Override
    public List<SettleAccountsConfirmData> parse(MultipartFile file) throws Exception {
        return ExcelUtils.parseExcelFile(file, SettleAccountsConfirmData.class);
    }
}
