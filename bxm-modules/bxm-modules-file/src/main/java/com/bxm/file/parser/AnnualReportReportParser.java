package com.bxm.file.parser;

import com.bxm.file.bean.dto.batchDeliverV2.AnnualReportReportData;
import com.bxm.file.util.ExcelUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@Component
public class AnnualReportReportParser implements ExcelV2Parser<AnnualReportReportData> {

    @Override
    public List<AnnualReportReportData> parse(MultipartFile file) throws Exception {
        return ExcelUtils.parseExcelFile(file, AnnualReportReportData.class);
    }
}
