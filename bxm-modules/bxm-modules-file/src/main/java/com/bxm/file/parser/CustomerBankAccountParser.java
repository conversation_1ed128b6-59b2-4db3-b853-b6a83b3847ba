package com.bxm.file.parser;

import com.bxm.file.bean.dto.batchDeliverV2.CustomerBankAccountData;
import com.bxm.file.util.ExcelUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@Component
public class CustomerBankAccountParser implements ExcelV2Parser<CustomerBankAccountData> {

    @Override
    public List<CustomerBankAccountData> parse(MultipartFile file) throws Exception {
        return ExcelUtils.parseExcelFile(file, CustomerBankAccountData.class);
    }
}
