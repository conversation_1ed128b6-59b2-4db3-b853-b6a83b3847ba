package com.bxm.file.parser;

import com.bxm.file.bean.dto.UpdatePersonTaxReportTotalAmountData;
import com.bxm.file.util.ExcelUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@Component
public class UpdatePersonTaxReportTotalAmountParser implements ExcelParser<UpdatePersonTaxReportTotalAmountData> {

    @Override
    public List<UpdatePersonTaxReportTotalAmountData> parse(MultipartFile file) throws Exception {
        return ExcelUtils.parseExcelFile(file, UpdatePersonTaxReportTotalAmountData.class);
    }
}
