package com.bxm.file.service.impl;

import java.util.Collections;
import java.util.List;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.common.core.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.bxm.file.mapper.CustomerRpaDetailFileMapper;
import com.bxm.file.domain.CustomerRpaDetailFile;
import com.bxm.file.service.ICustomerRpaDetailFileService;
import org.springframework.util.ObjectUtils;

/**
 * 交付明细附件Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-06-12
 */
@Service
public class CustomerRpaDetailFileServiceImpl extends ServiceImpl<CustomerRpaDetailFileMapper, CustomerRpaDetailFile> implements ICustomerRpaDetailFileService
{
    @Autowired
    private CustomerRpaDetailFileMapper customerRpaDetailFileMapper;

    /**
     * 查询交付明细附件
     * 
     * @param id 交付明细附件主键
     * @return 交付明细附件
     */
    @Override
    public CustomerRpaDetailFile selectCustomerRpaDetailFileById(Long id)
    {
        return customerRpaDetailFileMapper.selectCustomerRpaDetailFileById(id);
    }

    /**
     * 查询交付明细附件列表
     * 
     * @param customerRpaDetailFile 交付明细附件
     * @return 交付明细附件
     */
    @Override
    public List<CustomerRpaDetailFile> selectCustomerRpaDetailFileList(CustomerRpaDetailFile customerRpaDetailFile)
    {
        return customerRpaDetailFileMapper.selectCustomerRpaDetailFileList(customerRpaDetailFile);
    }

    /**
     * 新增交付明细附件
     * 
     * @param customerRpaDetailFile 交付明细附件
     * @return 结果
     */
    @Override
    public int insertCustomerRpaDetailFile(CustomerRpaDetailFile customerRpaDetailFile)
    {
        customerRpaDetailFile.setCreateTime(DateUtils.getNowDate());
        return customerRpaDetailFileMapper.insertCustomerRpaDetailFile(customerRpaDetailFile);
    }

    /**
     * 修改交付明细附件
     * 
     * @param customerRpaDetailFile 交付明细附件
     * @return 结果
     */
    @Override
    public int updateCustomerRpaDetailFile(CustomerRpaDetailFile customerRpaDetailFile)
    {
        customerRpaDetailFile.setUpdateTime(DateUtils.getNowDate());
        return customerRpaDetailFileMapper.updateCustomerRpaDetailFile(customerRpaDetailFile);
    }

    /**
     * 批量删除交付明细附件
     * 
     * @param ids 需要删除的交付明细附件主键
     * @return 结果
     */
    @Override
    public int deleteCustomerRpaDetailFileByIds(Long[] ids)
    {
        return customerRpaDetailFileMapper.deleteCustomerRpaDetailFileByIds(ids);
    }

    /**
     * 删除交付明细附件信息
     * 
     * @param id 交付明细附件主键
     * @return 结果
     */
    @Override
    public int deleteCustomerRpaDetailFileById(Long id)
    {
        return customerRpaDetailFileMapper.deleteCustomerRpaDetailFileById(id);
    }

    @Override
    public List<CustomerRpaDetailFile> selectBatchByRpaDetailId(List<Long> detailIds) {
        if (ObjectUtils.isEmpty(detailIds)) {
            return Collections.emptyList();
        }
        return list(new LambdaQueryWrapper<CustomerRpaDetailFile>()
                .in(CustomerRpaDetailFile::getRpaDetailId, detailIds));
    }
}
