package com.bxm.file.parser;

import com.bxm.file.bean.dto.batchDeliverV2.AnnualReportReportExceptionData;
import com.bxm.file.util.ExcelUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@Component
public class AnnualReportReportExceptionParser implements ExcelV2Parser<AnnualReportReportExceptionData> {

    @Override
    public List<AnnualReportReportExceptionData> parse(MultipartFile file) throws Exception {
        return ExcelUtils.parseExcelFile(file, AnnualReportReportExceptionData.class);
    }
}
