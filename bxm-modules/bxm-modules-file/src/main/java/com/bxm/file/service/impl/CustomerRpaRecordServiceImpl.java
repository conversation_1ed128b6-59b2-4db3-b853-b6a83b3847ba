package com.bxm.file.service.impl;

import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.common.core.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.bxm.file.mapper.CustomerRpaRecordMapper;
import com.bxm.file.domain.CustomerRpaRecord;
import com.bxm.file.service.ICustomerRpaRecordService;

/**
 * rap交付记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-06-12
 */
@Service
public class CustomerRpaRecordServiceImpl extends ServiceImpl<CustomerRpaRecordMapper, CustomerRpaRecord> implements ICustomerRpaRecordService
{
    @Autowired
    private CustomerRpaRecordMapper customerRpaRecordMapper;

    /**
     * 查询rap交付记录
     * 
     * @param id rap交付记录主键
     * @return rap交付记录
     */
    @Override
    public CustomerRpaRecord selectCustomerRpaRecordById(Long id)
    {
        return customerRpaRecordMapper.selectCustomerRpaRecordById(id);
    }

    /**
     * 查询rap交付记录列表
     * 
     * @param customerRpaRecord rap交付记录
     * @return rap交付记录
     */
    @Override
    public List<CustomerRpaRecord> selectCustomerRpaRecordList(CustomerRpaRecord customerRpaRecord)
    {
        return customerRpaRecordMapper.selectCustomerRpaRecordList(customerRpaRecord);
    }

    /**
     * 新增rap交付记录
     * 
     * @param customerRpaRecord rap交付记录
     * @return 结果
     */
    @Override
    public int insertCustomerRpaRecord(CustomerRpaRecord customerRpaRecord)
    {
        customerRpaRecord.setCreateTime(DateUtils.getNowDate());
        return customerRpaRecordMapper.insertCustomerRpaRecord(customerRpaRecord);
    }

    /**
     * 修改rap交付记录
     * 
     * @param customerRpaRecord rap交付记录
     * @return 结果
     */
    @Override
    public int updateCustomerRpaRecord(CustomerRpaRecord customerRpaRecord)
    {
        customerRpaRecord.setUpdateTime(DateUtils.getNowDate());
        return customerRpaRecordMapper.updateCustomerRpaRecord(customerRpaRecord);
    }

    /**
     * 批量删除rap交付记录
     * 
     * @param ids 需要删除的rap交付记录主键
     * @return 结果
     */
    @Override
    public int deleteCustomerRpaRecordByIds(Long[] ids)
    {
        return customerRpaRecordMapper.deleteCustomerRpaRecordByIds(ids);
    }

    /**
     * 删除rap交付记录信息
     * 
     * @param id rap交付记录主键
     * @return 结果
     */
    @Override
    public int deleteCustomerRpaRecordById(Long id)
    {
        return customerRpaRecordMapper.deleteCustomerRpaRecordById(id);
    }
}
