package com.bxm.file.bean.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RpaCreateResultDTO {

    @ApiModelProperty("是否校验完成")
    private Boolean isComplete;

    @ApiModelProperty("是否有异常数据，true-是，false-否")
    private Boolean hasException;

    @ApiModelProperty("批次号，下载异常文件时需要")
    private String batchNo;

    @ApiModelProperty("总数")
    private Long totalCount;

    @ApiModelProperty("文件总量")
    private Long totalFileCount;

    @ApiModelProperty("已上传文件数量")
    private Long completeFileCount;

    @ApiModelProperty("异常数据信息")
    private List<String> exceptionInfoList;

    private List<?> originList;

    private List<?> exceptionList;

    private List<?> rightList;
}
