package com.bxm.file.parser;

import com.bxm.file.bean.dto.batchDeliverV2.InAccountRpaData;
import com.bxm.file.util.ExcelUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@Component
public class InAccountRpaParser implements ExcelV2Parser<InAccountRpaData> {

    @Override
    public List<InAccountRpaData> parse(MultipartFile file) throws Exception {
        return ExcelUtils.parseExcelFile(file, InAccountRpaData.class);
    }
}
