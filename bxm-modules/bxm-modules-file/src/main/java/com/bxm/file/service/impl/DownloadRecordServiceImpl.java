package com.bxm.file.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.common.core.enums.DownloadType;
import com.bxm.common.core.exception.ServiceException;
import com.bxm.common.core.utils.StringUtils;
import com.bxm.common.security.utils.SecurityUtils;
import com.bxm.file.domain.DownloadRecord;
import com.bxm.file.mapper.DownloadRecordMapper;
import com.bxm.file.service.IDownloadRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PreDestroy;

/**
 * 标签Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-05-07
 */
@Service
@Slf4j
public class DownloadRecordServiceImpl extends ServiceImpl<DownloadRecordMapper, DownloadRecord> implements IDownloadRecordService
{
    @Autowired
    private DownloadRecordMapper downloadRecordMapper;

    @Override
    @PreDestroy
    public void updateFailWhenDestroy() {
        log.info("项目停止了======================");
    }

    @Override
    public Long createRecord(String title, Long dataCount, Long attachmentCount, Object obj, DownloadType downloadType) {
        if (checkHasDoingRecord(SecurityUtils.getUserId())) {
            throw new ServiceException("同时进行中的任务只能有一个");
        }
        DownloadRecord record = new DownloadRecord().setTitle(title)
                .setStatus(0)
                .setParam(JSONObject.toJSONString(obj))
                .setDataCount(dataCount)
                .setUserId(SecurityUtils.getUserId())
                .setDownloadType(downloadType.getCode())
                .setAttachmentCount(attachmentCount);
        save(record);
        return record.getId();
    }

    @Override
    public Boolean checkHasDoingRecord(Long userId) {
        return count(new LambdaQueryWrapper<DownloadRecord>()
                .eq(DownloadRecord::getUserId, userId).eq(DownloadRecord::getIsDel, false)
                .eq(DownloadRecord::getStatus, 0).eq(DownloadRecord::getIsFileDel, false)) > 0;
    }

    @Override
    public void updateDataCount(Long recordId, Long dataCount) {
        updateById(new DownloadRecord().setId(recordId).setDataCount(dataCount));
    }

    @Override
    public void updateDownloadError(Long recordId, String errorMsg) {
        updateById(new DownloadRecord().setId(recordId).setStatus(2).setErrorReason(!StringUtils.isEmpty(errorMsg) && errorMsg.length() > 500 ? errorMsg.substring(0, 500) : errorMsg));
    }
}
