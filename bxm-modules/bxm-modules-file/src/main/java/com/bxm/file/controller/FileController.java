package com.bxm.file.controller;

import com.bxm.common.core.domain.Result;
import com.bxm.file.bean.dto.FileInfoDTO;
import com.bxm.file.service.FileUploadService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/ossFile")
public class FileController {

    @Autowired
    private FileUploadService fileUploadService;

    @GetMapping("/getFullFileUrl")
    public Result<String> getFullFileUrl(@RequestParam("fileUrl") String fileUrl) {
        return Result.ok(fileUploadService.getFullFileUrl(fileUrl));
    }

    @GetMapping("/getFullFileUrlValidTime")
    public Result<String> getFullFileUrlValidTime(@RequestParam("fileUrl") String fileUrl,
                                                  @RequestParam("validTime") Long validTime) {
        return Result.ok(fileUploadService.getFullFileUrlValidTime(fileUrl, validTime));
    }

    @PostMapping("/batchGetFileInfo")
    public Result<List<FileInfoDTO>> batchGetFileInfo(@RequestBody List<String> fileUrls) {
        return Result.ok(fileUploadService.batchGetFileInfo(fileUrls));
    }
}
