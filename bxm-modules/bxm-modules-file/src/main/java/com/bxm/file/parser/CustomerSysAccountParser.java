package com.bxm.file.parser;

import com.bxm.file.bean.dto.batchDeliverV2.CustomerSysAccountData;
import com.bxm.file.util.ExcelUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@Component
public class CustomerSysAccountParser implements ExcelV2Parser<CustomerSysAccountData> {

    @Override
    public List<CustomerSysAccountData> parse(MultipartFile file) throws Exception {
        return ExcelUtils.parseExcelFile(file, CustomerSysAccountData.class);
    }
}
