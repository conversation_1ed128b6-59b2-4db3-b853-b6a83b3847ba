package com.bxm.file.bean.dto.rpa;

import com.bxm.common.core.annotation.Excel;
import com.bxm.file.bean.dto.AliFileDTO;

import java.util.Date;
import java.util.List;

public class RpaCustomerPeriodIncomeSheet2Data implements RpaEnterpriseData {

    @Excel(name = "序号")
    private String number;

    @Excel(name = "公司名称")
    private String enterpriseName;

    @Excel(name = "纳税识别号")
    private String creditCode;

    @Excel(name = "涉税机构纳税识别号")
    private String taxOrgCreditCode;

    @Excel(name = "登入密码")
    private String password;

    @Excel(name = "实名人")
    private String realName;

    @Excel(name = "手机号码")
    private String phone;

    @Excel(name = "月份")
    private String month;

    @Excel(name = "执行情况")
    private String doResult;

    @Excel(name = "全量发票开票金额")
    private String allTicketAmount;

    @Excel(name = "全量发票开票税额")
    private String allTicketTaxAmount;

    @Excel(name = "开票数据查询金额")
    private String ticketSearchAmount;

    @Excel(name = "开票数据查询税额")
    private String ticketSearchTaxAmount;

    @Excel(name = "开具发票取票结果")
    private String doTicketGetResult;

    @Excel(name = "取得发票取票结果")
    private String getTicketGetResult;

    @Excel(name = "开始取数时间", dateFormat = "yyyy/M/d HH:mm:ss")
    private Date startTime;

    @Excel(name = "结束取数时间", dateFormat = "yyyy/M/d HH:mm:ss")
    private Date endTime;

    @Excel(name = "销项导入情况")
    private String outputImportResult;

    @Excel(name = "进项导入情况")
    private String incomeImportResult;

    @Excel(name = "发票导入结果")
    private String ticketImportResult;

    @Excel(name = "异常信息")
    private String checkError;

    private List<AliFileDTO> files;

    private Long customerServiceId;

    private Long periodId;

    private Long deliverId;

    private String sheetIndex;

    @Override
    public boolean hasErrors() {
        return checkError != null && !checkError.isEmpty();
    }

    @Override
    public void setCheckError(String checkError) {
        this.checkError = checkError;
    }

    @Override
    public void addCheckError(String checkError) {
        if (this.checkError == null || this.checkError.isEmpty()) {
            this.checkError = checkError;
        } else {
            this.checkError += "; " + checkError;
        }
    }

    public String getNumber() {
        return number;
    }

    public void setNumber(String number) {
        this.number = number;
    }

    public String getEnterpriseName() {
        return enterpriseName;
    }

    public void setEnterpriseName(String enterpriseName) {
        this.enterpriseName = enterpriseName;
    }

    @Override
    public String getCreditCode() {
        return creditCode;
    }

    @Override
    public void setCreditCode(String creditCode) {
        this.creditCode = creditCode;
    }

    public String getTaxOrgCreditCode() {
        return taxOrgCreditCode;
    }

    public void setTaxOrgCreditCode(String taxOrgCreditCode) {
        this.taxOrgCreditCode = taxOrgCreditCode;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getRealName() {
        return realName;
    }

    public void setRealName(String realName) {
        this.realName = realName;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getMonth() {
        return month;
    }

    public void setMonth(String month) {
        this.month = month;
    }

    public String getDoResult() {
        return doResult;
    }

    public void setDoResult(String doResult) {
        this.doResult = doResult;
    }

    public String getAllTicketAmount() {
        return allTicketAmount;
    }

    public void setAllTicketAmount(String allTicketAmount) {
        this.allTicketAmount = allTicketAmount;
    }

    public String getAllTicketTaxAmount() {
        return allTicketTaxAmount;
    }

    public void setAllTicketTaxAmount(String allTicketTaxAmount) {
        this.allTicketTaxAmount = allTicketTaxAmount;
    }

    public String getTicketSearchAmount() {
        return ticketSearchAmount;
    }

    public void setTicketSearchAmount(String ticketSearchAmount) {
        this.ticketSearchAmount = ticketSearchAmount;
    }

    public String getTicketSearchTaxAmount() {
        return ticketSearchTaxAmount;
    }

    public void setTicketSearchTaxAmount(String ticketSearchTaxAmount) {
        this.ticketSearchTaxAmount = ticketSearchTaxAmount;
    }

    public String getDoTicketGetResult() {
        return doTicketGetResult;
    }

    public void setDoTicketGetResult(String doTicketGetResult) {
        this.doTicketGetResult = doTicketGetResult;
    }

    public String getGetTicketGetResult() {
        return getTicketGetResult;
    }

    public void setGetTicketGetResult(String getTicketGetResult) {
        this.getTicketGetResult = getTicketGetResult;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public String getOutputImportResult() {
        return outputImportResult;
    }

    public void setOutputImportResult(String outputImportResult) {
        this.outputImportResult = outputImportResult;
    }

    public String getIncomeImportResult() {
        return incomeImportResult;
    }

    public void setIncomeImportResult(String incomeImportResult) {
        this.incomeImportResult = incomeImportResult;
    }

    public String getTicketImportResult() {
        return ticketImportResult;
    }

    public void setTicketImportResult(String ticketImportResult) {
        this.ticketImportResult = ticketImportResult;
    }

    @Override
    public String getCheckError() {
        return checkError;
    }

    @Override
    public List<AliFileDTO> getFiles() {
        return files;
    }

    public void setFiles(List<AliFileDTO> files) {
        this.files = files;
    }

    public Long getCustomerServiceId() {
        return customerServiceId;
    }

    public void setCustomerServiceId(Long customerServiceId) {
        this.customerServiceId = customerServiceId;
    }

    public Long getPeriodId() {
        return periodId;
    }

    public void setPeriodId(Long periodId) {
        this.periodId = periodId;
    }

    public Long getDeliverId() {
        return deliverId;
    }

    public void setDeliverId(Long deliverId) {
        this.deliverId = deliverId;
    }

    @Override
    public String getSheetIndex() {
        return sheetIndex;
    }

    public void setSheetIndex(String sheetIndex) {
        this.sheetIndex = sheetIndex;
    }
}
