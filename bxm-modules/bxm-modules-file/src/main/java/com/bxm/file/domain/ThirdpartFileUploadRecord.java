package com.bxm.file.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bxm.common.core.annotation.Excel;
import com.bxm.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 第三方文件上传记录对象 c_thirdpart_file_upload_record
 * 
 * <AUTHOR>
 * @date 2025-03-04
 */
@Data
@ApiModel("第三方文件上传记录对象")
@Accessors(chain = true)
@TableName("c_thirdpart_file_upload_record")
public class ThirdpartFileUploadRecord extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    @TableId(value = "id", type = IdType.AUTO)
    @Excel(name = "id")
    @ApiModelProperty(value = "id")
    private Long id;

    /** 第三方文件地址 */
    @Excel(name = "第三方文件地址")
    @TableField("thirdpart_file_url")
    @ApiModelProperty(value = "第三方文件地址")
    private String thirdpartFileUrl;

    /** 系统文件地址 */
    @Excel(name = "系统文件地址")
    @TableField("file_url")
    @ApiModelProperty(value = "系统文件地址")
    private String fileUrl;

    /** 文件名 */
    @Excel(name = "文件名")
    @TableField("file_name")
    @ApiModelProperty(value = "文件名")
    private String fileName;

    /** 税局下载的报表名称 */
    @Excel(name = "税局下载的报表名称")
    @TableField("offical_file_name")
    @ApiModelProperty(value = "税局下载的报表名称")
    private String officalFileName;

    @Excel(name = "交付单id")
    @TableField("deliver_id")
    @ApiModelProperty(value = "交付单id")
    private Long deliverId;

}
