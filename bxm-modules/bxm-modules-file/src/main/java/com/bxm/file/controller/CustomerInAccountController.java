package com.bxm.file.controller;

import com.bxm.common.core.domain.Result;
import com.bxm.common.core.enums.DownloadType;
import com.bxm.common.core.exception.ServiceException;
import com.bxm.common.core.utils.StringUtils;
import com.bxm.common.core.web.domain.CommonFileVO;
import com.bxm.common.security.utils.SecurityUtils;
import com.bxm.customer.api.RemoteCustomerInAccountService;
import com.bxm.customer.api.domain.dto.RemoteCustomerInAccountDTO;
import com.bxm.customer.api.domain.vo.RemoteCustomerInAccountVO;
import com.bxm.file.service.AsyncService;
import com.bxm.file.service.IDownloadRecordService;
import com.bxm.file.service.OssService;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/8/21 9:50
 * happy coding!
 */
@RequestMapping("/customer/inAccount")
@RestController
@Api(tags = "入账交付文件相关")
@Slf4j
public class CustomerInAccountController {
    @Autowired
    private OssService ossService;

    @Autowired
    private RemoteCustomerInAccountService remoteCustomerInAccountService;

    @Autowired
    private IDownloadRecordService downloadRecordService;

    @Autowired
    private AsyncService asyncService;

    @PostMapping("/exportInAccountList")
    @ApiOperation(value = "导出入账交付单", notes = "导出入账交付单")
    public void exportInAccountList(HttpServletResponse response, RemoteCustomerInAccountVO vo, @RequestHeader("deptId") Long deptId) {
        try {
            //远程调用设置一下登陆者信息
            vo.setDeptId(deptId);
            vo.setUserId(SecurityUtils.getUserId());
            vo.setNickName(SecurityUtils.getLoginUser().getSysUser().getNickName());

            //分页接口，直接设置第1页和最大值，远程获取数据
            vo.setPageNum(1);
            //vo.setPageSize(Integer.MAX_VALUE);
            vo.setPageSize(5000);
            log.info("start inAccountList");
            List<RemoteCustomerInAccountDTO> list = remoteCustomerInAccountService.inAccountList(vo).getDataThrowException();
            log.info("end inAccountList");

            Map<String, Class<?>> sheetClassMap = new HashMap<>();
            sheetClassMap.put("入账交付单数据", RemoteCustomerInAccountDTO.class);

            Map<String, List<?>> dataMap = new HashMap<>();
            dataMap.put("入账交付单数据", list);

            ossService.downloadFilesAndGenerateZiByFiles(buildFiles(vo, list), dataMap, sheetClassMap, "入账交付单数据", response);
        } catch (Exception e) {
            log.error("导出文件异常:{}", e.getMessage());
            throw new ServiceException("下载失败");
        }
    }

    @PostMapping("/exportInAccountListAndUpload")
    @ApiOperation(value = "导出入账交付单", notes = "导出入账交付单")
    public Result exportInAccountListAndUpload(RemoteCustomerInAccountVO vo, @RequestHeader("deptId") Long deptId) {
        String title = "交付-入账（含附件）" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        vo.setDeptId(deptId);
        vo.setUserId(SecurityUtils.getUserId());
        vo.setNickName(SecurityUtils.getLoginUser().getSysUser().getNickName());
        Long downloadRecordId = downloadRecordService.createRecord(title, 0L, 0L, vo, DownloadType.DELIVERY_ACCOUNTING_ATTACHMENT);
        CompletableFuture.runAsync(() -> {
            try {
                //远程调用设置一下登陆者信息
                List<RemoteCustomerInAccountDTO> list = Lists.newArrayList();
                Integer pageNum = 1;
                Integer pageSize = 5000;
                vo.setPageSize(pageSize);
                while (true) {
                    vo.setPageNum(pageNum);
                    List<RemoteCustomerInAccountDTO> l = remoteCustomerInAccountService.inAccountList(vo).getDataThrowException();
                    if (!ObjectUtils.isEmpty(l)) {
                        list.addAll(l);
                        pageNum++;
                    } else {
                        break;
                    }
                }
                downloadRecordService.updateDataCount(downloadRecordId, (long) list.size());
                Map<String, Class<?>> sheetClassMap = new HashMap<>();
                sheetClassMap.put(title, RemoteCustomerInAccountDTO.class);

                Map<String, List<?>> dataMap = new HashMap<>();
                dataMap.put(title, list);

                asyncService.uploadExport(buildFiles(vo, list), dataMap, sheetClassMap, title, downloadRecordId);
            } catch (Exception e) {
                downloadRecordService.updateDownloadError(downloadRecordId, e.getMessage());
            }
        });
        return Result.ok();
    }

    @PostMapping("/exportInAccountListAndUploadRetry")
    @ApiIgnore
    public Result exportInAccountListAndUploadRetry(@RequestBody RemoteCustomerInAccountVO vo) {
        CompletableFuture.runAsync(() -> {
            try {
                List<RemoteCustomerInAccountDTO> list = Lists.newArrayList();
                Integer pageNum = 1;
                Integer pageSize = 5000;
                vo.setPageSize(pageSize);
                while (true) {
                    vo.setPageNum(pageNum);
                    List<RemoteCustomerInAccountDTO> l = remoteCustomerInAccountService.inAccountList(vo).getDataThrowException();
                    if (!ObjectUtils.isEmpty(l)) {
                        list.addAll(l);
                        pageNum++;
                    } else {
                        break;
                    }
                }
                downloadRecordService.updateDataCount(vo.getDownloadRecordId(), (long) list.size());
                Map<String, Class<?>> sheetClassMap = new HashMap<>();
                sheetClassMap.put(vo.getDownloadRecordTitle(), RemoteCustomerInAccountDTO.class);

                Map<String, List<?>> dataMap = new HashMap<>();
                dataMap.put(vo.getDownloadRecordTitle(), list);

                asyncService.uploadExport(buildFiles(vo, list), dataMap, sheetClassMap, vo.getDownloadRecordTitle(), vo.getDownloadRecordId());
            } catch (Exception e) {
                downloadRecordService.updateDownloadError(vo.getDownloadRecordId(), e.getMessage());
            }
        });
        return Result.ok();
    }

    private List<CommonFileVO> buildFiles(RemoteCustomerInAccountVO vo, List<RemoteCustomerInAccountDTO> list) {
        if (StringUtils.isEmpty(vo.getDownloadFileTypes())) {
            return Collections.emptyList();
        }

        List<Integer> downloadFileTypes = Arrays.stream(vo.getDownloadFileTypes().split(",")).map(Integer::valueOf).collect(Collectors.toList());

        List<CommonFileVO> files = Lists.newArrayList();

        for (Integer downloadFileType : downloadFileTypes) {
            String baseDir = getBaseDir(downloadFileType);
            if (StringUtils.isEmpty(baseDir)) {
                continue;
            }

            for (RemoteCustomerInAccountDTO dto : list) {
                String dirPath = baseDir + dto.getPeriod() + "/" + dto.getCustomerServiceId() + "-" + dto.getCreditCode() + "-" + dto.getCustomerName();

                List<CommonFileVO> fileList = getFileList(downloadFileType, dto);

                if (ObjectUtils.isEmpty(fileList)) {
                    continue;
                }

                for (CommonFileVO file : fileList) {
                    file.setBaseDir(dirPath);
                }

                files.addAll(fileList);
            }
        }

        if (!ObjectUtils.isEmpty(files)) {
            CustomerDeliverController.dealFileNames(files);
        }

        return files;
    }

    private static String getBaseDir(Integer downloadFileType) {
        String baseDir;

        switch (downloadFileType) {
            case 1:
                baseDir = "入账";
                break;
            case 2:
                baseDir = "RPA";
                break;
            default:
                baseDir = null;
                break;
        }

        return baseDir;
    }

    private List<CommonFileVO> getFileList(Integer downloadFileType, RemoteCustomerInAccountDTO dto) {
        List<CommonFileVO> fileList;

        switch (downloadFileType) {
            case 1:
                fileList = dto.getFiles();
                break;
            case 2:
                fileList = dto.getRpaFiles();
                break;
            default:
                fileList = Collections.emptyList();
                break;
        }

        return fileList;
    }
}
