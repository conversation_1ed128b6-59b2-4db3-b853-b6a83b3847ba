package com.bxm.file.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bxm.file.domain.CustomerRpaRecordDetail;

/**
 * rap明细Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-06-12
 */
@Mapper
public interface CustomerRpaRecordDetailMapper extends BaseMapper<CustomerRpaRecordDetail>
{
    /**
     * 查询rap明细
     * 
     * @param id rap明细主键
     * @return rap明细
     */
    public CustomerRpaRecordDetail selectCustomerRpaRecordDetailById(Long id);

    /**
     * 查询rap明细列表
     * 
     * @param customerRpaRecordDetail rap明细
     * @return rap明细集合
     */
    public List<CustomerRpaRecordDetail> selectCustomerRpaRecordDetailList(CustomerRpaRecordDetail customerRpaRecordDetail);

    /**
     * 新增rap明细
     * 
     * @param customerRpaRecordDetail rap明细
     * @return 结果
     */
    public int insertCustomerRpaRecordDetail(CustomerRpaRecordDetail customerRpaRecordDetail);

    /**
     * 修改rap明细
     * 
     * @param customerRpaRecordDetail rap明细
     * @return 结果
     */
    public int updateCustomerRpaRecordDetail(CustomerRpaRecordDetail customerRpaRecordDetail);

    /**
     * 删除rap明细
     * 
     * @param id rap明细主键
     * @return 结果
     */
    public int deleteCustomerRpaRecordDetailById(Long id);

    /**
     * 批量删除rap明细
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCustomerRpaRecordDetailByIds(Long[] ids);
}
