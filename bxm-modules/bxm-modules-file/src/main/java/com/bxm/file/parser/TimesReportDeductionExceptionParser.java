package com.bxm.file.parser;

import com.bxm.file.bean.dto.batchDeliverV2.TimesReportDeductionExceptionData;
import com.bxm.file.util.ExcelUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@Component
public class TimesReportDeductionExceptionParser implements ExcelV2Parser<TimesReportDeductionExceptionData> {

    @Override
    public List<TimesReportDeductionExceptionData> parse(MultipartFile file) throws Exception {
        return ExcelUtils.parseExcelFile(file, TimesReportDeductionExceptionData.class);
    }
}
