package com.bxm.file.parser;

import com.bxm.file.bean.dto.batchDeliverV2.TimesReportCreateData;
import com.bxm.file.bean.dto.batchDeliverV2.TimesReportDeductionData;
import com.bxm.file.util.ExcelUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@Component
public class TimesReportDeductionParser implements ExcelV2Parser<TimesReportDeductionData> {

    @Override
    public List<TimesReportDeductionData> parse(MultipartFile file) throws Exception {
        return ExcelUtils.parseExcelFile(file, TimesReportDeductionData.class);
    }
}
