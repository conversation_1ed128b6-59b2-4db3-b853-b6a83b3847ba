package com.bxm.file.parser;

import com.bxm.file.bean.dto.batchDeliverV2.AccountingCashierFlowCheckFileData;
import com.bxm.file.util.ExcelUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@Component
public class AccountingCashierFlowCheckFileParser implements ExcelV2Parser<AccountingCashierFlowCheckFileData> {

    @Override
    public List<AccountingCashierFlowCheckFileData> parse(MultipartFile file) throws Exception {
        return ExcelUtils.parseExcelFile(file, AccountingCashierFlowCheckFileData.class);
    }
}
