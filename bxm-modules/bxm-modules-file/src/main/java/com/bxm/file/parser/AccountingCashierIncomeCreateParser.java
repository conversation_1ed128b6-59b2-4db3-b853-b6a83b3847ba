package com.bxm.file.parser;

import com.bxm.file.bean.dto.batchDeliverV2.AccountingCashierIncomeCreateData;
import com.bxm.file.util.ExcelUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@Component
public class AccountingCashierIncomeCreateParser implements ExcelV2Parser<AccountingCashierIncomeCreateData> {

    @Override
    public List<AccountingCashierIncomeCreateData> parse(MultipartFile file) throws Exception {
        return ExcelUtils.parseExcelFile(file, AccountingCashierIncomeCreateData.class);
    }
}
