package com.bxm.file.controller;

import com.bxm.common.core.domain.Result;
import com.bxm.common.security.annotation.Logical;
import com.bxm.common.security.annotation.RequiresPermissions;
import com.bxm.file.bean.dto.BatchDeliverConfirmResultDTO;
import com.bxm.file.bean.dto.CheckResult;
import com.bxm.file.service.DeliverBatchService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

@RestController
@RequestMapping("/deliver/batch")
@Api(tags = "批量交付")
public class DeliverBatchController {

    @Autowired
    private DeliverBatchService deliverBatchService;

    @PostMapping("/batchDeliverCheck")
    @ApiOperation("上传模板校验，返回批次号，权限字符：file:deliver:batchDeliver")
    @RequiresPermissions(value = {"file:deliver:batchDeliver", "file:deliver:inAccount:BatchDeliver"}, logical = Logical.OR)
    public Result<String> batchDeliverCheck(@RequestParam("deliverType") @ApiParam("交付类型，1-医保，2-社保，3-个税（工资薪金），4-国税，5-预认证，6-个税（经营收入），7-入账，8-结算，9-收入，10-年度汇总") Integer deliverType,
                                            @RequestParam("operType") @ApiParam("操作类型，1-新建，2-申报，3-解除申报异常，4-扣款，5-解除扣款异常，6-补充，7-认证，8-解除认证异常，9-补充申报附件，，10-入账，11-确认，12-新入账编辑，13-入账RPA更新，14-入账结算，15-新户预收结算，16-驳回,17-完结，18-解除完结异常，19-收入编辑，20-年度汇总编辑，21-更新个税总额") Integer operType,
                                            @RequestParam("excelFile") @ApiParam("excl文件") MultipartFile excelFile,
                                            @RequestParam(value = "zipFile", required = false) @ApiParam("附件文件") MultipartFile zipFile,
                                            @RequestParam(value = "personChangeZipFile", required = false) @ApiParam("人员变动附件") MultipartFile personChangeZipFile,
                                            @RequestParam(value = "period", required = false) @ApiParam("账期") Integer period,
                                            @RequestHeader("deptId") Long deptId) throws Exception {
        return Result.ok(deliverBatchService.uploadFiles(excelFile, zipFile, personChangeZipFile, deliverType, operType, period, deptId));
    }

    @GetMapping("/progress/{batchNo}")
    @ApiOperation("查询校验进度")
    public Result<CheckResult> getProgress(@PathVariable String batchNo) {
        return Result.ok(deliverBatchService.getProgress(batchNo));
    }

    @PostMapping("/confirm/{batchNo}")
    @ApiOperation("确认数据，返回处理结果文案")
    public Result<BatchDeliverConfirmResultDTO> confirmData(@PathVariable @ApiParam("批次号") String batchNo,
                                                            @RequestHeader("deptId") Long deptId) {
        return Result.ok(deliverBatchService.confirmData(batchNo, deptId));
    }

    @PostMapping("/downloadErrorFile")
    @ApiOperation("下载异常文件")
    public void downloadErrorFile(@RequestParam("batchNo") @ApiParam("批次号") String batchNo, HttpServletResponse response) {
        deliverBatchService.downloadErrorFile(batchNo, response);
    }
}
