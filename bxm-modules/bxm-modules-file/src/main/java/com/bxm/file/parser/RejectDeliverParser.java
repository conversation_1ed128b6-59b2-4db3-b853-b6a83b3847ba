package com.bxm.file.parser;

import com.bxm.file.bean.dto.RejectDeliverData;
import com.bxm.file.util.ExcelUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@Component
public class RejectDeliverParser implements ExcelParser<RejectDeliverData> {
    @Override
    public List<RejectDeliverData> parse(MultipartFile file) throws Exception {
        return ExcelUtils.parseExcelFile(file, RejectDeliverData.class);
    }
}
