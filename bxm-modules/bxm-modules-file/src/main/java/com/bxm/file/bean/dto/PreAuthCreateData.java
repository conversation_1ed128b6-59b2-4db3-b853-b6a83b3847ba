package com.bxm.file.bean.dto;

import com.bxm.common.core.annotation.Excel;

import java.util.List;

public class PreAuthCreateData implements EnterpriseData {

    @Excel(name = "企业名称")
    private String enterpriseName;

    @Excel(name = "信用代码")
    private String creditCode;

    @Excel(name = "销项普票金额")
    private String outNormalTicketAmount;

    @Excel(name = "销项普票税额")
    private String outNormalTicketTaxAmount;

    @Excel(name = "销项专票金额")
    private String outSpecialTicketAmount;

    @Excel(name = "销项专票税额")
    private String outSpecialTicketTaxAmount;

    @Excel(name = "简易计税收入")
    private String simpleTaxIncome;

    @Excel(name = "简易计税税额")
    private String simpleTaxTaxAmount;

    @Excel(name = "无票收入")
    private String noTicketIncomeAmount;

    @Excel(name = "无票收入税额")
    private String noTicketIncomeTaxAmount;

    @Excel(name = "进项税额")
    private String inTaxAmount;

    @Excel(name = "上期留抵税额")
    private String lastTaxAmount;

    @Excel(name = "备注")
    private String remark;

    @Excel(name = "DDL（期望完成时间）")
    private String ddl;

    public String getDdl() {
        return ddl;
    }

    public void setDdl(String ddl) {
        this.ddl = ddl;
    }

    @Excel(name = "异常信息")
    private String checkError;

    private List<AliFileDTO> files;

    private Long customerServiceId;

    private Long periodId;

    private Long deliverId;

    public String getOutNormalTicketAmount() {
        return outNormalTicketAmount;
    }

    public String getOutNormalTicketTaxAmount() {
        return outNormalTicketTaxAmount;
    }

    public String getOutSpecialTicketAmount() {
        return outSpecialTicketAmount;
    }

    public String getOutSpecialTicketTaxAmount() {
        return outSpecialTicketTaxAmount;
    }

    public String getSimpleTaxIncome() {
        return simpleTaxIncome;
    }

    public String getSimpleTaxTaxAmount() {
        return simpleTaxTaxAmount;
    }

    public String getNoTicketIncomeAmount() {
        return noTicketIncomeAmount;
    }

    public String getNoTicketIncomeTaxAmount() {
        return noTicketIncomeTaxAmount;
    }

    public String getInTaxAmount() {
        return inTaxAmount;
    }

    public String getLastTaxAmount() {
        return lastTaxAmount;
    }

    public Long getCustomerServiceId() {
        return customerServiceId;
    }

    public void setCustomerServiceId(Long customerServiceId) {
        this.customerServiceId = customerServiceId;
    }

    public Long getPeriodId() {
        return periodId;
    }

    public void setPeriodId(Long periodId) {
        this.periodId = periodId;
    }

    public Long getDeliverId() {
        return deliverId;
    }

    public void setDeliverId(Long deliverId) {
        this.deliverId = deliverId;
    }

    @Override
    public boolean hasErrors() {
        return checkError != null && !checkError.isEmpty();
    }

    @Override
    public void setCheckError(String checkError) {
        this.checkError = checkError;
    }

    @Override
    public void addCheckError(String checkError) {
        if (this.checkError == null || this.checkError.isEmpty()) {
            this.checkError = checkError;
        } else {
            this.checkError += "; " + checkError;
        }
    }

    public String getEnterpriseName() {
        return enterpriseName;
    }

    public void setEnterpriseName(String enterpriseName) {
        this.enterpriseName = enterpriseName;
    }

    @Override
    public String getCreditCode() {
        return creditCode;
    }

    @Override
    public void setCreditCode(String creditCode) {
        this.creditCode = creditCode;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getCheckError() {
        return checkError;
    }

    @Override
    public List<AliFileDTO> getFiles() {
        return files;
    }

    public void setFiles(List<AliFileDTO> files) {
        this.files = files;
    }
}
