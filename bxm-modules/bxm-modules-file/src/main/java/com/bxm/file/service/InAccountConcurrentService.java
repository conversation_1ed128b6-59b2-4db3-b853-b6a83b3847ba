package com.bxm.file.service;

import com.bxm.common.core.domain.R;
import com.bxm.customer.api.RemoteCustomerInAccountService;
import com.bxm.customer.api.domain.vo.RemoteOperateInAccountRpaUpdateVO;
import com.bxm.customer.api.domain.vo.RemoteUpdateInAccountV2VO;
import com.bxm.customer.api.domain.vo.RemoteUpdateInAccountV3VO;
import com.bxm.file.util.ListeningExecutorServiceUtil;
import com.google.common.collect.Lists;
import com.google.common.util.concurrent.ListenableFuture;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/8/23 16:49
 * happy coding!
 */
@Service
@Slf4j
public class InAccountConcurrentService {
    @Autowired
    private RemoteCustomerInAccountService remoteCustomerInAccountService;

    public List<R<Integer>> batchUpdateInAccount(Long deptId, List<RemoteUpdateInAccountV2VO> vos) {
        List<R<Integer>> result = Lists.newArrayList();

        //构建并发任务
        List<ListenableFuture<R<Integer>>> listenableFutures = vos.stream()
                .map(vo -> ListeningExecutorServiceUtil.SERVICE.submit(() -> remoteCustomerInAccountService.updateInAccount(deptId, vo)))
                .collect(Collectors.toList());

        //获取并发任务返回的信息，有timeout限制
        listenableFutures.forEach(row -> {
            try {
                R<Integer> data = row.get(ListeningExecutorServiceUtil.FUTURE_GET_ACTIVITY_TIME_OUT, TimeUnit.MILLISECONDS);

                result.add(data);

            } catch (InterruptedException | ExecutionException | TimeoutException e) {
                log.error("batchUpdateInAccount ListenableFuture updateInAccount error e={}", e.getMessage());
                e.printStackTrace();
            }
        });

        return result;
    }

    public List<R<Integer>> batchUpdateInAccountV3(Long deptId, List<RemoteUpdateInAccountV3VO> vos) {
        List<R<Integer>> result = Lists.newArrayList();

        //构建并发任务
        List<ListenableFuture<R<Integer>>> listenableFutures = vos.stream()
                .map(vo -> ListeningExecutorServiceUtil.SERVICE.submit(() -> remoteCustomerInAccountService.updateInAccountV3(deptId, vo)))
                .collect(Collectors.toList());

        //获取并发任务返回的信息，有timeout限制
        listenableFutures.forEach(row -> {
            try {
                R<Integer> data = row.get(ListeningExecutorServiceUtil.FUTURE_GET_ACTIVITY_TIME_OUT, TimeUnit.MILLISECONDS);

                result.add(data);

            } catch (InterruptedException | ExecutionException | TimeoutException e) {
                log.error("batchUpdateInAccountV3 ListenableFuture updateInAccount error e={}", e.getMessage());
                e.printStackTrace();
            }
        });

        return result;
    }

    public List<R<Integer>> batchInAccountRpaUpdate(Long deptId, List<RemoteOperateInAccountRpaUpdateVO> vos) {
        List<R<Integer>> result = Lists.newArrayList();

        //构建并发任务
        List<ListenableFuture<R<Integer>>> listenableFutures = vos.stream()
                .map(vo -> ListeningExecutorServiceUtil.SERVICE.submit(() -> remoteCustomerInAccountService.inAccountRpaUpdateInner(deptId, vo)))
                .collect(Collectors.toList());

        //获取并发任务返回的信息，有timeout限制
        listenableFutures.forEach(row -> {
            try {
                R<Integer> data = row.get(ListeningExecutorServiceUtil.FUTURE_GET_ACTIVITY_TIME_OUT, TimeUnit.MILLISECONDS);

                result.add(data);

            } catch (InterruptedException | ExecutionException | TimeoutException e) {
                log.error("batchInAccountRpaUpdate ListenableFuture updateInAccount error e={}", e.getMessage());
                e.printStackTrace();
            }
        });

        return result;
    }
}
