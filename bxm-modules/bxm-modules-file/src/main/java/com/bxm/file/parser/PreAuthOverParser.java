package com.bxm.file.parser;

import com.bxm.file.bean.dto.PreAuthOverData;
import com.bxm.file.util.ExcelUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@Component
public class PreAuthOverParser implements ExcelParser<PreAuthOverData>{
    @Override
    public List<PreAuthOverData> parse(MultipartFile file) throws Exception {
        // 使用 ExcelUtils 解析预认证的 Excel 文件
        return ExcelUtils.parseExcelFile(file, PreAuthOverData.class);
    }
}
