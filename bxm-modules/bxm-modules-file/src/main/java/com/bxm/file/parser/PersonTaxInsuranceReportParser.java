package com.bxm.file.parser;

import com.bxm.file.bean.dto.PersonTaxReportData;
import com.bxm.file.util.ExcelUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@Component
public class PersonTaxInsuranceReportParser implements ExcelParser<PersonTaxReportData>{
    @Override
    public List<PersonTaxReportData> parse(MultipartFile file) throws Exception {
        // 使用 ExcelUtils 解析个税（工资薪金）的 Excel 文件
        return ExcelUtils.parseExcelFile(file, PersonTaxReportData.class);
    }
}
