package com.bxm.file.factory;

import com.bxm.file.bean.dto.EnterpriseData;
import com.bxm.file.parser.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
public class ExcelParserFactory {

    private final Map<String, ExcelParser<? extends EnterpriseData>> parserMap = new HashMap<>();

    @Autowired
    public ExcelParserFactory(MedicalInsuranceCreateParser medicalInsuranceCreateParser, MedicalInsuranceReportParser medicalInsuranceReportParser, MedicalInsuranceDeductionParser medicalInsuranceDeductionParser, MedicalInsuranceReportExceptionParser medicalInsuranceReportExceptionParser, MedicalInsuranceDeductionExceptionParser medicalInsuranceDeductionExceptionParser,
                              SocialInsuranceCreateParser socialInsuranceCreateParser, SocialInsuranceReportParser socialInsuranceReportParser, SocialInsuranceDeductionParser socialInsuranceDeductionParser, SocialInsuranceReportExceptionParser socialInsuranceReportExceptionParser, SocialInsuranceDeductionExceptionParser socialInsuranceDeductionExceptionParser,
                              PersonTaxInsuranceCreateParser personTaxInsuranceCreateParser, PersonTaxInsuranceReportParser personTaxInsuranceReportParser, PersonTaxInsuranceDeductionParser personTaxInsuranceDeductionParser, PersonTaxInsuranceReportExceptionParser personTaxInsuranceReportExceptionParser, PersonTaxInsuranceDeductionExceptionParser personTaxInsuranceDeductionExceptionParser,
                              CountryTaxInsuranceCreateParser countryTaxInsuranceCreateParser, CountryTaxInsuranceReportParser countryTaxInsuranceReportParser, CountryTaxInsuranceDeductionParser countryTaxInsuranceDeductionParser, CountryTaxInsuranceReportExceptionParser countryTaxInsuranceReportExceptionParser, CountryTaxInsuranceDeductionExceptionParser countryTaxInsuranceDeductionExceptionParser,
                              PreAuthCreateParser preAuthCreateParser, PreAuthSupplementParser preAuthSupplementParser, PreAuthAuthParser preAuthAuthParser, PreAuthAuthExceptionParser preAuthAuthExceptionParser,
                              OperatingIncomePersonTaxInsuranceCreateParser operatingIncomePersonTaxInsuranceCreateParser, OperatingIncomePersonTaxInsuranceReportParser operatingIncomePersonTaxInsuranceReportParser, OperatingIncomePersonTaxInsuranceDeductionParser operatingIncomePersonTaxInsuranceDeductionParser, OperatingIncomePersonTaxInsuranceReportExceptionParser operatingIncomePersonTaxInsuranceReportExceptionParser, OperatingIncomePersonTaxInsuranceDeductionExceptionParser operatingIncomePersonTaxInsuranceDeductionExceptionParser,
                              CommonSupplementReportFileParser commonSupplementReportFileParser, CustomerServiceInAccountParser customerServiceInAccountParser, ConfirmDeliverParser confirmDeliverParser,
                              CustomerServiceInAccountV3Parser customerServiceInAccountV3Parser,
                              CustomerServiceInAccountRpaUpdateParser customerServiceInAccountRpaUpdateParser,
                              RejectDeliverParser rejectDeliverParser,
                              PreAuthOverParser preAuthOverParser,
                              PreAuthOverExceptionParser preAuthOverExceptionParser,
                              CustomerServiceIncomeUpdateParser customerServiceIncomeUpdateParser,
                              CustomerServicePeriodYearParser customerServicePeriodYearParser,
                              UpdatePersonTaxReportTotalAmountParser updatePersonTaxReportTotalAmountParser
                              ) {
        parserMap.put("1-1", medicalInsuranceCreateParser);
        parserMap.put("1-2", medicalInsuranceReportParser);
        parserMap.put("1-3", medicalInsuranceReportExceptionParser);
        parserMap.put("1-4", medicalInsuranceDeductionParser);
        parserMap.put("1-5", medicalInsuranceDeductionExceptionParser);
        parserMap.put("1-9", commonSupplementReportFileParser);
        parserMap.put("2-1", socialInsuranceCreateParser);
        parserMap.put("2-2", socialInsuranceReportParser);
        parserMap.put("2-3", socialInsuranceReportExceptionParser);
        parserMap.put("2-4", socialInsuranceDeductionParser);
        parserMap.put("2-5", socialInsuranceDeductionExceptionParser);
        parserMap.put("2-9", commonSupplementReportFileParser);
        parserMap.put("3-1", personTaxInsuranceCreateParser);
        parserMap.put("3-2", personTaxInsuranceReportParser);
        parserMap.put("3-3", personTaxInsuranceReportExceptionParser);
        parserMap.put("3-4", personTaxInsuranceDeductionParser);
        parserMap.put("3-5", personTaxInsuranceDeductionExceptionParser);
        parserMap.put("3-9", commonSupplementReportFileParser);
        parserMap.put("4-1", countryTaxInsuranceCreateParser);
        parserMap.put("4-2", countryTaxInsuranceReportParser);
        parserMap.put("4-3", countryTaxInsuranceReportExceptionParser);
        parserMap.put("4-4", countryTaxInsuranceDeductionParser);
        parserMap.put("4-5", countryTaxInsuranceDeductionExceptionParser);
        parserMap.put("4-9", commonSupplementReportFileParser);
        parserMap.put("5-1", preAuthCreateParser);
        parserMap.put("5-6", preAuthSupplementParser);
        parserMap.put("5-7", preAuthAuthParser);
        parserMap.put("5-8", preAuthAuthExceptionParser);
        parserMap.put("6-1", operatingIncomePersonTaxInsuranceCreateParser);
        parserMap.put("6-2", operatingIncomePersonTaxInsuranceReportParser);
        parserMap.put("6-3", operatingIncomePersonTaxInsuranceReportExceptionParser);
        parserMap.put("6-4", operatingIncomePersonTaxInsuranceDeductionParser);
        parserMap.put("6-5", operatingIncomePersonTaxInsuranceDeductionExceptionParser);
        parserMap.put("6-9", commonSupplementReportFileParser);
        parserMap.put("7-10", customerServiceInAccountParser);
        parserMap.put("1-11", confirmDeliverParser);
        parserMap.put("2-11", confirmDeliverParser);
        parserMap.put("3-11", confirmDeliverParser);
        parserMap.put("4-11", confirmDeliverParser);
        parserMap.put("5-11", confirmDeliverParser);
        parserMap.put("6-11", confirmDeliverParser);
        parserMap.put("7-12", customerServiceInAccountV3Parser);
        parserMap.put("7-13", customerServiceInAccountRpaUpdateParser);
        parserMap.put("1-16", rejectDeliverParser);
        parserMap.put("2-16", rejectDeliverParser);
        parserMap.put("3-16", rejectDeliverParser);
        parserMap.put("4-16", rejectDeliverParser);
        parserMap.put("5-16", rejectDeliverParser);
        parserMap.put("6-16", rejectDeliverParser);
        parserMap.put("5-17", preAuthOverParser);
        parserMap.put("5-18", preAuthOverExceptionParser);
        parserMap.put("9-19", customerServiceIncomeUpdateParser);
        parserMap.put("10-20", customerServicePeriodYearParser);
        parserMap.put("3-21", updatePersonTaxReportTotalAmountParser);
    }

    public ExcelParser<? extends EnterpriseData> getParser(int deliverType, int operType) {
        return parserMap.get(deliverType + "-" + operType);
    }
}
