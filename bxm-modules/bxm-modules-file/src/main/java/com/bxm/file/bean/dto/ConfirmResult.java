package com.bxm.file.bean.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ConfirmResult {

    @ApiModelProperty("数据总处理量")
    private Long totalDataCount;

    @ApiModelProperty("已处理数据量")
    private Long completeDataCount;

    @ApiModelProperty("是否有异常")
    private Boolean hasException;

    @ApiModelProperty("是否处理完成")
    private Boolean isComplete;

    @ApiModelProperty("成功处理数量")
    private Long successDataCount;

    @ApiModelProperty("失败处理数量")
    private Long failDataCount;
}
