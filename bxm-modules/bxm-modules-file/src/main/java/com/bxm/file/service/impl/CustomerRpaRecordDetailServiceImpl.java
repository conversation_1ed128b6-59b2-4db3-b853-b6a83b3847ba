package com.bxm.file.service.impl;

import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.common.core.exception.ServiceException;
import com.bxm.common.core.utils.DateUtils;
import com.bxm.common.core.utils.ServletUtils;
import com.bxm.common.core.utils.StringUtils;
import com.bxm.common.core.web.domain.CommonFileVO;
import com.bxm.common.security.utils.SecurityUtils;
import com.bxm.customer.api.RemoteCustomerDeliverService;
import com.bxm.customer.api.domain.vo.RemoteBatchDeliverCheckResultVO;
import com.bxm.customer.api.domain.vo.RemoteCustomerDeliverCheckResultVO;
import com.bxm.customer.api.domain.vo.RemoteCustomerDeliverReportVO;
import com.bxm.file.bean.dto.CustomerPeriodIncomeDTO;
import com.bxm.file.bean.dto.PersonTaxImportDTO;
import com.bxm.file.bean.dto.rpa.RpaCountryTaxCheckResultSheetData;
import com.bxm.file.bean.dto.rpa.RpaEnterpriseData;
import com.bxm.file.bean.dto.rpa.RpaPersonTaxReportData;
import com.bxm.file.domain.CustomerRpaDetailFile;
import com.bxm.file.service.ICustomerRpaDetailFileService;
import com.bxm.system.api.RemoteDeptService;
import com.bxm.system.api.domain.SysEmployee;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.bxm.file.mapper.CustomerRpaRecordDetailMapper;
import com.bxm.file.domain.CustomerRpaRecordDetail;
import com.bxm.file.service.ICustomerRpaRecordDetailService;
import org.springframework.util.ObjectUtils;

/**
 * rap明细Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-06-12
 */
@Service
public class CustomerRpaRecordDetailServiceImpl extends ServiceImpl<CustomerRpaRecordDetailMapper, CustomerRpaRecordDetail> implements ICustomerRpaRecordDetailService
{
    @Autowired
    private CustomerRpaRecordDetailMapper customerRpaRecordDetailMapper;

    @Autowired
    private ICustomerRpaDetailFileService customerRpaDetailFileService;

    @Autowired
    private RemoteDeptService remoteDeptService;

    @Autowired
    private RemoteCustomerDeliverService remoteCustomerDeliverService;

    /**
     * 查询rap明细
     * 
     * @param id rap明细主键
     * @return rap明细
     */
    @Override
    public CustomerRpaRecordDetail selectCustomerRpaRecordDetailById(Long id)
    {
        return customerRpaRecordDetailMapper.selectCustomerRpaRecordDetailById(id);
    }

    /**
     * 查询rap明细列表
     * 
     * @param customerRpaRecordDetail rap明细
     * @return rap明细
     */
    @Override
    public List<CustomerRpaRecordDetail> selectCustomerRpaRecordDetailList(CustomerRpaRecordDetail customerRpaRecordDetail)
    {
        return customerRpaRecordDetailMapper.selectCustomerRpaRecordDetailList(customerRpaRecordDetail);
    }

    /**
     * 新增rap明细
     * 
     * @param customerRpaRecordDetail rap明细
     * @return 结果
     */
    @Override
    public int insertCustomerRpaRecordDetail(CustomerRpaRecordDetail customerRpaRecordDetail)
    {
        customerRpaRecordDetail.setCreateTime(DateUtils.getNowDate());
        return customerRpaRecordDetailMapper.insertCustomerRpaRecordDetail(customerRpaRecordDetail);
    }

    /**
     * 修改rap明细
     * 
     * @param customerRpaRecordDetail rap明细
     * @return 结果
     */
    @Override
    public int updateCustomerRpaRecordDetail(CustomerRpaRecordDetail customerRpaRecordDetail)
    {
        customerRpaRecordDetail.setUpdateTime(DateUtils.getNowDate());
        return customerRpaRecordDetailMapper.updateCustomerRpaRecordDetail(customerRpaRecordDetail);
    }

    /**
     * 批量删除rap明细
     * 
     * @param ids 需要删除的rap明细主键
     * @return 结果
     */
    @Override
    public int deleteCustomerRpaRecordDetailByIds(Long[] ids)
    {
        return customerRpaRecordDetailMapper.deleteCustomerRpaRecordDetailByIds(ids);
    }

    /**
     * 删除rap明细信息
     * 
     * @param id rap明细主键
     * @return 结果
     */
    @Override
    public int deleteCustomerRpaRecordDetailById(Long id)
    {
        return customerRpaRecordDetailMapper.deleteCustomerRpaRecordDetailById(id);
    }

    @Override
    public void savePersonTaxDetails(Long recordId, List<PersonTaxImportDTO> originList) {
        if (Objects.isNull(recordId)) {
            return;
        }
        originList.forEach(item -> {
            CustomerRpaRecordDetail detail = new CustomerRpaRecordDetail()
                    .setRpaRecordId(recordId)
                    .setCustomerName(item.getCustomerName())
                    .setCreditCode(item.getCreditCode())
                    .setCustomerServiceId(item.getCustomerServiceId())
                    .setStatus(0)
                    .setExceptionMsg(item.getExceptionReason())
                    .setContent(JSONObject.toJSONString(item));
            save(detail);
            if (!ObjectUtils.isEmpty(item.getAttachFiles())) {
                customerRpaDetailFileService.saveBatch(item.getAttachFiles().stream().map(f ->
                    new CustomerRpaDetailFile().setRpaDetailId(detail.getId())
                            .setFileUrl(f.getUrl())
                            .setFileName(f.getFileName())
                ).collect(Collectors.toList()));
            }
        });
    }

    @Override
    public void savePeriodIncomeDetails(Long recordId, List<CustomerPeriodIncomeDTO> originList) {
        if (Objects.isNull(recordId)) {
            return;
        }
        originList.forEach(item -> {
            CustomerRpaRecordDetail detail = new CustomerRpaRecordDetail()
                    .setRpaRecordId(recordId)
                    .setCustomerName(item.getCustomerName())
                    .setCreditCode(item.getCreditCode())
                    .setCustomerServiceId(item.getCustomerServiceId())
                    .setStatus(0)
                    .setExceptionMsg(item.getExceptionReason())
                    .setContent(JSONObject.toJSONString(item));
            save(detail);
        });
    }

    @Override
    public void saveRpaRecordDetails(Long recordId, Integer rpaType, Integer operType, List<? extends RpaEnterpriseData> dataList) {
        if (Objects.isNull(recordId) || ObjectUtils.isEmpty(dataList)) {
            return;
        }
        Long userId = SecurityUtils.getUserId();
        Long deptId = Long.parseLong(ServletUtils.getRequest().getHeader("deptId"));
        List<SysEmployee> employees = remoteDeptService.getEmployeesBySecondDeptIdAndUserId(deptId, userId).getDataThrowException();
        List<RemoteBatchDeliverCheckResultVO> checkResultDataList = Lists.newArrayList();
        dataList.forEach(item -> {
            CustomerRpaRecordDetail detail = new CustomerRpaRecordDetail()
                    .setRpaRecordId(recordId)
                    .setCustomerName(invokeGetMethod(item, "getEnterpriseName"))
                    .setCreditCode(invokeGetMethod(item, "getCreditCode"))
                    .setCustomerServiceId(invokeGetLongMethod(item, "getCustomerServiceId"))
                    .setFailReason("")
                    .setExceptionMsg(item.getCheckError())
                    .setContent(JSONObject.toJSONString(item));
            if (rpaType == 2 && operType == 1) {
                // 申报查询结果
                String reportSearchResult = invokeGetMethod(item, "getReportSearchResult");
                String reportResult = invokeGetMethod(item, "getReportResult");
                detail.setReportResult(reportSearchResult);
                detail.setRpaResult(reportResult);
                if (!StringUtils.isEmpty(item.getCheckError())) {
                    detail.setStatus(3);
                } else {
                    if (!StringUtils.isEmpty(reportSearchResult) && reportSearchResult.contains("成功")) {
                        detail.setStatus(2);
                        RpaPersonTaxReportData personTaxReportData = (RpaPersonTaxReportData) item;
                        RemoteCustomerDeliverReportVO vo = new RemoteCustomerDeliverReportVO();
                        vo.setId(personTaxReportData.getDeliverId());
                        vo.setCurrentPeriodAmount(StringUtils.isEmpty(personTaxReportData.getReportAmount()) ? null : new BigDecimal(personTaxReportData.getReportAmount()));
                        vo.setOverdueAmount(BigDecimal.ZERO);
                        vo.setSupplementAmount(BigDecimal.ZERO);
                        vo.setReportAmount(StringUtils.isEmpty(personTaxReportData.getReportAmount()) ? null : new BigDecimal(personTaxReportData.getReportAmount()));
                        vo.setReportRemark(personTaxReportData.getReportSearchResult());
                        vo.setReportFiles(ObjectUtils.isEmpty(personTaxReportData.getFiles()) ? Lists.newArrayList() :
                                personTaxReportData.getFiles().stream().map(f -> CommonFileVO.builder().fileUrl(f.getUrl()).fileName(f.getFileName()).build()).collect(Collectors.toList()));
                        vo.setReportStatus(!StringUtils.isEmpty(personTaxReportData.getReportSearchResult()) && personTaxReportData.getReportSearchResult().contains("成功") ? 1 : 2);
                        vo.setSaveType(2);
                        vo.setEmployeeId(ObjectUtils.isEmpty(employees) ? null : employees.get(0).getEmployeeId());
                        vo.setEmployeeName(ObjectUtils.isEmpty(employees) ? SecurityUtils.getLoginUser().getSysUser().getNickName() : employees.get(0).getEmployeeName());
                        vo.setUserId(SecurityUtils.getUserId());
                        vo.setDeptId(deptId);
                        vo.setIsRpa(true);
                        try {
                            remoteCustomerDeliverService.remoteReport(vo).getDataThrowException();
                        } catch (ServiceException e) {
                            detail.setStatus(3);
                            detail.setFailReason(e.getMessage());
                        }
                    } else {
                        detail.setStatus(0);
                    }
                }
            } else if (rpaType == 2 && operType == 4) {
                // 个税申报检查
                if (!StringUtils.isEmpty(item.getCheckError())) {
                    detail.setStatus(3);
                } else {
                    RpaPersonTaxReportData personTaxReportData = (RpaPersonTaxReportData) item;
                    detail.setStatus(2);
                    checkResultDataList.add(RemoteBatchDeliverCheckResultVO.builder().id(personTaxReportData.getDeliverId())
                            .checkFiles(ObjectUtils.isEmpty(personTaxReportData.getFiles()) ? Lists.newArrayList() :
                                    personTaxReportData.getFiles().stream().map(f -> CommonFileVO.builder().fileUrl(f.getUrl()).fileName(f.getFileName()).build()).collect(Collectors.toList())).build());
                }
            } else if (rpaType == 3 && operType == 4) {
                // 国税申报检查
                if (!StringUtils.isEmpty(item.getCheckError())) {
                    detail.setStatus(3);
                } else {
                    RpaCountryTaxCheckResultSheetData countryTaxCheckResult = (RpaCountryTaxCheckResultSheetData) item;
                    detail.setStatus(2);
                    checkResultDataList.add(RemoteBatchDeliverCheckResultVO.builder().id(countryTaxCheckResult.getDeliverId())
                            .checkFiles(ObjectUtils.isEmpty(countryTaxCheckResult.getFiles()) ? Lists.newArrayList() :
                                    countryTaxCheckResult.getFiles().stream().map(f -> CommonFileVO.builder().fileUrl(f.getUrl()).fileName(f.getFileName()).build()).collect(Collectors.toList())).build());
                }
            } else {
                detail.setStatus(StringUtils.isEmpty(item.getCheckError()) ? 0 : 3);
            }
            save(detail);
            if (!ObjectUtils.isEmpty(item.getFiles())) {
                customerRpaDetailFileService.saveBatch(item.getFiles().stream().map(f ->
                        new CustomerRpaDetailFile().setRpaDetailId(detail.getId())
                                .setFileUrl(f.getUrl())
                                .setFileName(f.getFileName())
                ).collect(Collectors.toList()));
            }
        });
        if (!ObjectUtils.isEmpty(checkResultDataList)) {
            remoteCustomerDeliverService.batchDeliverCheckResult(RemoteCustomerDeliverCheckResultVO.builder()
                    .userId(SecurityUtils.getUserId())
                    .deptId(deptId)
                    .dataList(checkResultDataList).build()).getDataThrowException();
        }
    }

    @Override
    public List<CustomerRpaRecordDetail> selectByRpaRecordId(Long rpaRecordId) {
        if (Objects.isNull(rpaRecordId)) {
            return Collections.emptyList();
        }
        return list(new LambdaQueryWrapper<CustomerRpaRecordDetail>()
                .eq(CustomerRpaRecordDetail::getRpaRecordId, rpaRecordId));
    }

    private String invokeGetMethod(RpaEnterpriseData data, String methodName) {
        try {
            Method method = data.getClass().getMethod(methodName);
            return (String) method.invoke(data);
        } catch (Exception e) {
            return null;
        }
    }

    private Long invokeGetLongMethod(RpaEnterpriseData data, String methodName) {
        try {
            Method method = data.getClass().getMethod(methodName);
            Object obj = method.invoke(data);
            return Objects.isNull(obj) ? null : Long.parseLong(obj.toString());
        } catch (Exception e) {
            return null;
        }
    }
}
