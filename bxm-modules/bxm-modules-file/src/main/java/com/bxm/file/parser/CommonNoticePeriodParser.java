package com.bxm.file.parser;

import com.bxm.file.bean.dto.batchDeliverV2.CommonNoticePeriodData;
import com.bxm.file.util.ExcelUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@Component
public class CommonNoticePeriodParser implements ExcelV2Parser<CommonNoticePeriodData> {

    @Override
    public List<CommonNoticePeriodData> parse(MultipartFile file) throws Exception {
        return ExcelUtils.parseExcelFile(file, CommonNoticePeriodData.class);
    }
}
