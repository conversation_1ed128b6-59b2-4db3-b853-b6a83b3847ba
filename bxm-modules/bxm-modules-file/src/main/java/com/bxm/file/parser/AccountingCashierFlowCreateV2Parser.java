package com.bxm.file.parser;

import com.bxm.file.bean.dto.batchDeliverV2.AccountingCashierFlowCreateV2Data;
import com.bxm.file.util.ExcelUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@Component
public class AccountingCashierFlowCreateV2Parser implements ExcelV2Parser<AccountingCashierFlowCreateV2Data> {

    @Override
    public List<AccountingCashierFlowCreateV2Data> parse(MultipartFile file) throws Exception {
        return ExcelUtils.parseExcelFile(file, AccountingCashierFlowCreateV2Data.class);
    }
}
