package com.bxm.file.parser;

import com.bxm.file.bean.dto.batchDeliverV2.SettleAccountsReportData;
import com.bxm.file.util.ExcelUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@Component
public class SettleAccountsReportParser implements ExcelV2Parser<SettleAccountsReportData> {

    @Override
    public List<SettleAccountsReportData> parse(MultipartFile file) throws Exception {
        return ExcelUtils.parseExcelFile(file, SettleAccountsReportData.class);
    }
}
