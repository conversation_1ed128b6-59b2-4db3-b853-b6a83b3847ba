package com.bxm.file.parser;

import com.bxm.file.bean.dto.batchDeliverV2.AccountingCashierIncomeSupplementFileData;
import com.bxm.file.util.ExcelUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@Component
public class AccountingCashierIncomeSupplementFileParser implements ExcelV2Parser<AccountingCashierIncomeSupplementFileData> {

    @Override
    public List<AccountingCashierIncomeSupplementFileData> parse(MultipartFile file) throws Exception {
        return ExcelUtils.parseExcelFile(file, AccountingCashierIncomeSupplementFileData.class);
    }
}
