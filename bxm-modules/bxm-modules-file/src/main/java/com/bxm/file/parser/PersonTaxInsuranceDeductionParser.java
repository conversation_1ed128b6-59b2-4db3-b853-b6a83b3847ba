package com.bxm.file.parser;

import com.bxm.file.bean.dto.PersonTaxDeductionData;
import com.bxm.file.util.ExcelUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@Component
public class PersonTaxInsuranceDeductionParser implements ExcelParser<PersonTaxDeductionData>{
    @Override
    public List<PersonTaxDeductionData> parse(MultipartFile file) throws Exception {
        // 使用 ExcelUtils 解析个税（工资薪金）的 Excel 文件
        return ExcelUtils.parseExcelFile(file, PersonTaxDeductionData.class);
    }
}
