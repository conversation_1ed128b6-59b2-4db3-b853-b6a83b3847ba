package com.bxm.file.parser;

import com.bxm.file.bean.dto.batchDeliverV2.AccountingCashierFlowDealExceptionData;
import com.bxm.file.util.ExcelUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@Component
public class AccountingCashierFlowDealExceptionParser implements ExcelV2Parser<AccountingCashierFlowDealExceptionData> {

    @Override
    public List<AccountingCashierFlowDealExceptionData> parse(MultipartFile file) throws Exception {
        return ExcelUtils.parseExcelFile(file, AccountingCashierFlowDealExceptionData.class);
    }
}
