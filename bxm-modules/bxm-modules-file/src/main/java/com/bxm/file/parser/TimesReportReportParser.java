package com.bxm.file.parser;

import com.bxm.file.bean.dto.batchDeliverV2.TimesReportCreateData;
import com.bxm.file.bean.dto.batchDeliverV2.TimesReportReportData;
import com.bxm.file.util.ExcelUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@Component
public class TimesReportReportParser implements ExcelV2Parser<TimesReportReportData> {

    @Override
    public List<TimesReportReportData> parse(MultipartFile file) throws Exception {
        return ExcelUtils.parseExcelFile(file, TimesReportReportData.class);
    }
}
