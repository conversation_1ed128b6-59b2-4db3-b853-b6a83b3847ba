package com.bxm.file.bean.dto.batchDeliverV2;

import com.bxm.common.core.annotation.Excel;
import com.bxm.file.bean.dto.AliFileDTO;

import java.util.List;

public class AccountingCashierIncomeDeliverData implements EnterpriseV2Data {

    @Excel(name = "企业名")
    private String enterpriseName;

    @Excel(name = "信用代码")
    private String creditCode;

    @Excel(name = "账期：yyyymm")
    private String period;

    @Excel(name = "交付结果：正常、无账务、无需交付、异常")
    private String deliverResult;

    @Excel(name = "材料完整：齐、缺、缺但齐")
    private String materialIntegrity;

    @Excel(name = "是否结账：是/否")
    private String isSettleAccount;

    @Excel(name = "本年累计主营收入")
    private String majorIncomeTotal;

    /** 本年累计主营成本 */
    @Excel(name = "本年累计主营成本")
    private String majorCostTotal;

    /** 本年累计会计利润 */
    @Excel(name = "本年累计主营净利润")
    private String profitTotal;

    /** 本年费用调增 */
    @Excel(name = "本年费用调增")
    private String priorYearExpenseIncrease;

    /** 个税申报人数 */
    @Excel(name = "个税申报人数")
    private String taxReportCount;

    /** 本年个税申报工资总额 */
    @Excel(name = "本年个税申报工资总额")
    private String taxReportSalaryTotal;

    @Excel(name = "文件夹名")
    private String materialDirectName;

    @Excel(name = "备注")
    private String remark;

    @Excel(name = "异常信息")
    private String checkError;

    private List<AliFileDTO> medicalFiles;

    private List<AliFileDTO> socialFiles;

    private Long customerServiceId;

    private Long periodId;

    private Long medicalDeliverId;

    private Long socialDeliverId;

    private Boolean doMedical;

    private Boolean doSocial;

    private Boolean isMedical;

    private Boolean isSocial;

    private String medicalCheckError;

    private String socialCheckError;

    @Override
    public String getMedicalCheckError() {
        return medicalCheckError;
    }

    @Override
    public String getSocialCheckError() {
        return socialCheckError;
    }

    @Override
    public boolean hasMedicalErrors() {
        return medicalCheckError != null && !medicalCheckError.isEmpty();
    }

    @Override
    public void addMedicalCheckError(String checkError) {
        if (this.medicalCheckError == null || this.medicalCheckError.isEmpty()) {
            this.medicalCheckError = checkError;
        } else {
            this.medicalCheckError += "; " + checkError;
        }
    }

    @Override
    public boolean hasSocialErrors() {
        return socialCheckError != null && !socialCheckError.isEmpty();
    }

    @Override
    public void addSocialCheckError(String checkError) {
        if (this.socialCheckError == null || this.socialCheckError.isEmpty()) {
            this.socialCheckError = checkError;
        } else {
            this.socialCheckError += "; " + checkError;
        }
    }

    @Override
    public Boolean getIsMedical() {
        return isMedical;
    }

    @Override
    public void setIsMedical(Boolean medical) {
        isMedical = medical;
    }

    @Override
    public Boolean getIsSocial() {
        return isSocial;
    }

    @Override
    public void setIsSocial(Boolean social) {
        isSocial = social;
    }

    @Override
    public Boolean getDoMedical() {
        return doMedical;
    }

    @Override
    public void setDoMedical(Boolean doMedical) {
        this.doMedical = doMedical;
    }

    @Override
    public Boolean getDoSocial() {
        return doSocial;
    }

    @Override
    public void setDoSocial(Boolean doSocial) {
        this.doSocial = doSocial;
    }

    @Override
    public boolean hasErrors() {
        return checkError != null && !checkError.isEmpty();
    }

    @Override
    public void addCheckError(String checkError) {
        if (this.checkError == null || this.checkError.isEmpty()) {
            this.checkError = checkError;
        } else {
            this.checkError += "; " + checkError;
        }
    }

    public String getEnterpriseName() {
        return enterpriseName;
    }

    public void setEnterpriseName(String enterpriseName) {
        this.enterpriseName = enterpriseName;
    }

    @Override
    public String getCreditCode() {
        return creditCode;
    }

    @Override
    public void setCreditCode(String creditCode) {
        this.creditCode = creditCode;
    }

    @Override
    public String getCheckError() {
        return checkError;
    }

    @Override
    public void setCheckError(String checkError) {
        this.checkError = checkError;
    }

    @Override
    public List<AliFileDTO> getMedicalFiles() {
        return medicalFiles;
    }

    public void setMedicalFiles(List<AliFileDTO> medicalFiles) {
        this.medicalFiles = medicalFiles;
    }

    @Override
    public List<AliFileDTO> getSocialFiles() {
        return socialFiles;
    }

    public void setSocialFiles(List<AliFileDTO> socialFiles) {
        this.socialFiles = socialFiles;
    }

    public Long getCustomerServiceId() {
        return customerServiceId;
    }

    public void setCustomerServiceId(Long customerServiceId) {
        this.customerServiceId = customerServiceId;
    }

    public Long getPeriodId() {
        return periodId;
    }

    public void setPeriodId(Long periodId) {
        this.periodId = periodId;
    }

    @Override
    public Long getMedicalDeliverId() {
        return medicalDeliverId;
    }

    public void setMedicalDeliverId(Long medicalDeliverId) {
        this.medicalDeliverId = medicalDeliverId;
    }

    @Override
    public Long getSocialDeliverId() {
        return socialDeliverId;
    }

    public void setSocialDeliverId(Long socialDeliverId) {
        this.socialDeliverId = socialDeliverId;
    }

    public String getPeriod() {
        return period;
    }

    public void setPeriod(String period) {
        this.period = period;
    }

    public String getDeliverResult() {
        return deliverResult;
    }

    public void setDeliverResult(String deliverResult) {
        this.deliverResult = deliverResult;
    }

    public String getIsSettleAccount() {
        return isSettleAccount;
    }

    public void setIsSettleAccount(String isSettleAccount) {
        this.isSettleAccount = isSettleAccount;
    }

    public String getMajorIncomeTotal() {
        return majorIncomeTotal;
    }

    public void setMajorIncomeTotal(String majorIncomeTotal) {
        this.majorIncomeTotal = majorIncomeTotal;
    }

    public String getMajorCostTotal() {
        return majorCostTotal;
    }

    public void setMajorCostTotal(String majorCostTotal) {
        this.majorCostTotal = majorCostTotal;
    }

    public String getProfitTotal() {
        return profitTotal;
    }

    public void setProfitTotal(String profitTotal) {
        this.profitTotal = profitTotal;
    }

    public String getPriorYearExpenseIncrease() {
        return priorYearExpenseIncrease;
    }

    public void setPriorYearExpenseIncrease(String priorYearExpenseIncrease) {
        this.priorYearExpenseIncrease = priorYearExpenseIncrease;
    }

    public String getTaxReportCount() {
        return taxReportCount;
    }

    public void setTaxReportCount(String taxReportCount) {
        this.taxReportCount = taxReportCount;
    }

    public String getTaxReportSalaryTotal() {
        return taxReportSalaryTotal;
    }

    public void setTaxReportSalaryTotal(String taxReportSalaryTotal) {
        this.taxReportSalaryTotal = taxReportSalaryTotal;
    }

    public String getMaterialDirectName() {
        return materialDirectName;
    }

    public void setMaterialDirectName(String materialDirectName) {
        this.materialDirectName = materialDirectName;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getMaterialIntegrity() {
        return materialIntegrity;
    }

    public void setMaterialIntegrity(String materialIntegrity) {
        this.materialIntegrity = materialIntegrity;
    }
}
