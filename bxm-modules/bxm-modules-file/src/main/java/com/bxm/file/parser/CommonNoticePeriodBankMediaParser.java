package com.bxm.file.parser;

import com.bxm.file.bean.dto.batchDeliverV2.CommonNoticePeriodBankMediaData;
import com.bxm.file.util.ExcelUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@Component
public class CommonNoticePeriodBankMediaParser implements ExcelV2Parser<CommonNoticePeriodBankMediaData> {

    @Override
    public List<CommonNoticePeriodBankMediaData> parse(MultipartFile file) throws Exception {
        return ExcelUtils.parseExcelFile(file, CommonNoticePeriodBankMediaData.class);
    }
}
