package com.bxm.file.bean.dto;

import com.bxm.common.core.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PersonTaxImportDTO {

    @Excel(name = "序号")
    private Integer number;

    @Excel(name = "公司名称")
    private String customerName;

    @Excel(name = "企业税号")
    private String creditCode;

    private Long customerServiceId;

    @Excel(name = "申报提交结果")
    private String reportResult;

    @Excel(name = "申报结果查询")
    private String reportSearchResult;

    @Excel(name = "申报表下载结果")
    private String reportDownloadResult;

    @Excel(name = "税款缴纳金额")
    private String reportAmount;

    @Excel(name = "申报所属期")
    private String reportPeriod;

    @Excel(name = "开始处理时间")
    private Date dealStartTime;

    @Excel(name = "结束处理时间")
    private Date dealEndTime;

    @Excel(name = "异常原因")
    private String exceptionReason;

    private List<AliFileDTO> attachFiles;
}
