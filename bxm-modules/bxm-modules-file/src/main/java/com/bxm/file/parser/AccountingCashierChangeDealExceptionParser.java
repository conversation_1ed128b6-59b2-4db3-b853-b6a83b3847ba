package com.bxm.file.parser;

import com.bxm.file.bean.dto.batchDeliverV2.AccountingCashierChangeDealExceptionData;
import com.bxm.file.util.ExcelUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@Component
public class AccountingCashierChangeDealExceptionParser implements ExcelV2Parser<AccountingCashierChangeDealExceptionData> {

    @Override
    public List<AccountingCashierChangeDealExceptionData> parse(MultipartFile file) throws Exception {
        return ExcelUtils.parseExcelFile(file, AccountingCashierChangeDealExceptionData.class);
    }
}
