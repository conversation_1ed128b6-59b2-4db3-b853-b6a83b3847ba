package com.bxm.file.parser;

import com.bxm.file.bean.dto.batchDeliverV2.CustomerTagData;
import com.bxm.file.util.ExcelUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@Component
public class CustomerTagParser implements ExcelV2Parser<CustomerTagData> {

    @Override
    public List<CustomerTagData> parse(MultipartFile file) throws Exception {
        return ExcelUtils.parseExcelFile(file, CustomerTagData.class);
    }
}
