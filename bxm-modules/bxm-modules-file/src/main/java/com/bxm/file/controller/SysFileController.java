package com.bxm.file.controller;

import com.bxm.common.core.domain.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import com.bxm.file.service.ISysFileService;
import springfox.documentation.spring.web.readers.operation.ApiOperationReader;

import java.util.Objects;

/**
 * 文件请求处理
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/file")
@Api("文件相关")
public class SysFileController
{
    private static final Logger log = LoggerFactory.getLogger(SysFileController.class);

    @Autowired
    private ISysFileService sysFileService;
    @Autowired
    private ApiOperationReader apiOperationReader;

    @PostMapping("/batchUploadService")
    @ApiOperation("批量上传服务")
    public Result batchUploadService(MultipartFile file) throws Exception {
        if (Objects.isNull(file)) {
            throw new RuntimeException("文件为空");
        }
        // 获取文件名
        String fileName = file.getOriginalFilename();
        // 创建Workbook对象
        Workbook workbook;
        if (fileName.endsWith(".xls")) {
            workbook = new HSSFWorkbook(file.getInputStream());
        } else if (fileName.endsWith(".xlsx")) {
            workbook = new XSSFWorkbook(file.getInputStream());
        } else {
            throw new IllegalArgumentException("只支持xls和xlsx格式文件");
        }
        Sheet sheet = workbook.getSheetAt(0);
        return Result.ok();
    }
}