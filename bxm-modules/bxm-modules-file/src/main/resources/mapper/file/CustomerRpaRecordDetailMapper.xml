<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bxm.file.mapper.CustomerRpaRecordDetailMapper">
    
    <resultMap type="com.bxm.file.domain.CustomerRpaRecordDetail" id="CustomerRpaRecordDetailResult">
        <result property="id"    column="id"    />
        <result property="rpaRecordId"    column="rpa_record_id"    />
        <result property="customerName"    column="customer_name"    />
        <result property="creditCode"    column="credit_code"    />
        <result property="customerServiceId"    column="customer_service_id"    />
        <result property="status"    column="status"    />
        <result property="failReason"    column="fail_reason"    />
        <result property="exceptionMsg"    column="exception_msg"    />
        <result property="content"    column="content"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectCustomerRpaRecordDetailVo">
        select id, rpa_record_id, customer_name, credit_code, customer_service_id, status, fail_reason, exception_msg, content, create_by, create_time, update_by, update_time from c_customer_rpa_record_detail
    </sql>

    <select id="selectCustomerRpaRecordDetailList" parameterType="com.bxm.file.domain.CustomerRpaRecordDetail" resultMap="CustomerRpaRecordDetailResult">
        <include refid="selectCustomerRpaRecordDetailVo"/>
        <where>  
            <if test="rpaRecordId != null "> and rpa_record_id = #{rpaRecordId}</if>
            <if test="customerName != null  and customerName != ''"> and customer_name like concat('%', #{customerName}, '%')</if>
            <if test="creditCode != null  and creditCode != ''"> and credit_code = #{creditCode}</if>
            <if test="customerServiceId != null "> and customer_service_id = #{customerServiceId}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="failReason != null  and failReason != ''"> and fail_reason = #{failReason}</if>
            <if test="exceptionMsg != null  and exceptionMsg != ''"> and exception_msg = #{exceptionMsg}</if>
            <if test="content != null  and content != ''"> and content = #{content}</if>
        </where>
    </select>
    
    <select id="selectCustomerRpaRecordDetailById" parameterType="Long" resultMap="CustomerRpaRecordDetailResult">
        <include refid="selectCustomerRpaRecordDetailVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertCustomerRpaRecordDetail" parameterType="com.bxm.file.domain.CustomerRpaRecordDetail" useGeneratedKeys="true" keyProperty="id">
        insert into c_customer_rpa_record_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="rpaRecordId != null">rpa_record_id,</if>
            <if test="customerName != null and customerName != ''">customer_name,</if>
            <if test="creditCode != null and creditCode != ''">credit_code,</if>
            <if test="customerServiceId != null">customer_service_id,</if>
            <if test="status != null">status,</if>
            <if test="failReason != null">fail_reason,</if>
            <if test="exceptionMsg != null">exception_msg,</if>
            <if test="content != null">content,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="rpaRecordId != null">#{rpaRecordId},</if>
            <if test="customerName != null and customerName != ''">#{customerName},</if>
            <if test="creditCode != null and creditCode != ''">#{creditCode},</if>
            <if test="customerServiceId != null">#{customerServiceId},</if>
            <if test="status != null">#{status},</if>
            <if test="failReason != null">#{failReason},</if>
            <if test="exceptionMsg != null">#{exceptionMsg},</if>
            <if test="content != null">#{content},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateCustomerRpaRecordDetail" parameterType="com.bxm.file.domain.CustomerRpaRecordDetail">
        update c_customer_rpa_record_detail
        <trim prefix="SET" suffixOverrides=",">
            <if test="rpaRecordId != null">rpa_record_id = #{rpaRecordId},</if>
            <if test="customerName != null and customerName != ''">customer_name = #{customerName},</if>
            <if test="creditCode != null and creditCode != ''">credit_code = #{creditCode},</if>
            <if test="customerServiceId != null">customer_service_id = #{customerServiceId},</if>
            <if test="status != null">status = #{status},</if>
            <if test="failReason != null">fail_reason = #{failReason},</if>
            <if test="exceptionMsg != null">exception_msg = #{exceptionMsg},</if>
            <if test="content != null">content = #{content},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCustomerRpaRecordDetailById" parameterType="Long">
        delete from c_customer_rpa_record_detail where id = #{id}
    </delete>

    <delete id="deleteCustomerRpaRecordDetailByIds" parameterType="String">
        delete from c_customer_rpa_record_detail where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>