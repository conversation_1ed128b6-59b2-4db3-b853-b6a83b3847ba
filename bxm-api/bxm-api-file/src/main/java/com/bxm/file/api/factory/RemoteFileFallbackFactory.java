package com.bxm.file.api.factory;

import com.bxm.common.core.domain.R;
import com.bxm.common.core.web.domain.CommonFileVO;
import com.bxm.file.api.RemoteFileService;
import com.bxm.file.api.domain.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@Component
public class RemoteFileFallbackFactory implements FallbackFactory<RemoteFileService> {

    private static final Logger log = LoggerFactory.getLogger(RemoteFileFallbackFactory.class);

    @Override
    public RemoteFileService create(Throwable cause) {
        log.error("远程调用失败:{}", cause.getMessage());
        return new RemoteFileService() {

            @Override
            public R<String> getFullFileUrl(String fileUrl) {
                log.error("远程调用获取文件全路径失败:{}", cause.getMessage());
                return R.ok("");
            }

            @Override
            public R<String> getFullFileUrlValidTime(String fileUrl, Long validTime) {
                log.error("远程调用获取文件全路径失败:{}", cause.getMessage());
                return R.ok("");
            }

            @Override
            public R<List<RemoteAliFileDTO>> batchGetFileInfo(List<String> fileUrls) {
                log.error("批量获取文件信息失败:{}", cause.getMessage());
                return R.fail("批量获取文件信息失败");
            }

            @Override
            public R<RemoteAliFileDTO> uploadFile(MultipartFile file) {
                log.error("远程调用上传文件失败:{}", cause.getMessage());
                return R.fail("上传文件失败");
            }

            @Override
            public R socialSecurityExportAndUploadRetry(RemoteCustomerDeliverSearchDownloadVO vo) {
                log.error("远程调用导出医社保交付单（带附件）失败:{}", cause.getMessage());
                return R.fail("导出医社保交付单失败");
            }

            @Override
            public R personalTaxExportAndUploadRetry(RemoteCustomerDeliverSearchDownloadVO vo) {
                log.error("远程调用导出个税（工资薪金）（带附件）失败:{}", cause.getMessage());
                return R.fail("导出个税（工资薪金）交付单失败");
            }

            @Override
            public R operatingIncomeTaxExportAndUploadRetry(RemoteCustomerDeliverSearchDownloadVO vo) {
                log.error("远程调用导出个税（经营所得）（带附件）失败:{}", cause.getMessage());
                return R.fail("导出个税（经营所得）交付单失败");
            }

            @Override
            public R countryTaxExportAndUploadRetry(RemoteCustomerDeliverSearchDownloadVO vo) {
                log.error("远程调用导出国税交付单（带附件）失败:{}", cause.getMessage());
                return R.fail("导出国税交付单失败");
            }

            @Override
            public R preAuthExportAndUploadRetry(RemoteCustomerDeliverSearchDownloadVO vo) {
                log.error("远程调用导出预认证交付单（带附件）失败:{}", cause.getMessage());
                return R.fail("导出预认证交付单失败");
            }

            @Override
            public R settleAccountsExportAndUploadRetry(RemoteCustomerDeliverSearchDownloadVO vo) {
                log.error("远程调用导出汇算交付单（带附件）失败:{}", cause.getMessage());
                return R.fail("导出汇算交付单失败");
            }

            @Override
            public R residualBenefitsExportAndUploadRetry(RemoteCustomerDeliverSearchDownloadVO vo) {
                log.error("远程调用导出残保金交付单（带附件）失败:{}", cause.getMessage());
                return R.fail("导出残保金交付单失败");
            }

            @Override
            public R timesReportExportAndUploadRetry(RemoteCustomerDeliverSearchDownloadVO vo) {
                log.error("远程调用导出次报交付单（带附件）失败:{}", cause.getMessage());
                return R.fail("远程调用导出次报交付单");
            }

            @Override
            public R annualReportExportAndUploadRetry(RemoteCustomerDeliverSearchDownloadVO vo) {
                log.error("远程调用导出年报交付单（带附件）失败:{}", cause.getMessage());
                return R.fail("导出年报交付单失败");
            }

            @Override
            public R exportInAccountListAndUploadRetry(RemoteCustomerInAccountDownloadVO vo) {
                log.error("远程调用导出入账交付单（带附件）失败:{}", cause.getMessage());
                return R.fail("导出入账交付单失败");
            }

            @Override
            public R accountingCashierExportAndUploadRetry(RemoteAccountingCashierSearchDownloadVO vo, String source) {
                log.error("远程调用导出账务列表（带附件）失败:{}", cause.getMessage());
                return R.fail("远程调用导出账务列表失败");
            }

            @Override
            public R materialFilesExportAndUploadRetry(RemoteMaterialFilesDownloadRetryDTO vo, String source) {
                log.error("远程调用重试账期材料下载失败:{}", cause.getMessage());
                return R.fail("远程调用重试账期材料下载失败");
            }

            @Override
            public R pushReviewExportAndUploadRetry(RemotePushReviewErrorDataRetryDTO vo, String source) {
                log.error("远程调用重试推送异常数据下载失败:{}", cause.getMessage());
                return R.fail("远程调用重试推送异常数据下载失败");
            }

            @Override
            public R workOrderExportAndUploadRetry(RemoteWorkOrderDownloadVO vo, String source) {
                log.error("远程调用重试工单数据下载失败:{}", cause.getMessage());
                return R.fail("远程调用重试工单数据下载失败");
            }

            @Override
            public R deleteOssFile(String url, String source) {
                log.error("远程调用删除oss文件失败:{}", cause.getMessage());
                return R.fail("删除oss文件失败");
            }

            @Override
            public R stopAnalysisTask(String taskId, String source) {
                log.error("中止解析任务失败:{}", cause.getMessage());
                return R.fail("中止解析任务失败");
            }

            @Override
            public R<List<CommonFileVO>> uploadByThirdFileUrls(List<RemoteThirdpartFileVO> files, String source) {
                log.error("上传第三方图片失败:{}", cause.getMessage());
                return R.fail("上传第三方图片失败");
            }

            @Override
            public R<List<CommonFileVO>> uploadByReportTable(RemoteThirdpartFileVO file, String source) {
                log.error("上传申报表文件失败:{}", cause.getMessage());
                return R.fail("上传申报表文件失败");
            }
        };
    }
}
