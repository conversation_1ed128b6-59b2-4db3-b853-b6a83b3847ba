package com.bxm.file.api.domain;

import com.bxm.common.core.web.domain.BaseVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/21 9:55
 * happy coding!
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RemoteCustomerInAccountDownloadVO {
    @ApiModelProperty("关键词")
    private String keyWord;

    @ApiModelProperty("标签名称")
    private String tagName;

    @ApiModelProperty(value = "标签是否包含，1-包含，0-不包含", allowableValues = "0,1")
    private Integer tagIncludeFlag;

    @ApiModelProperty(value = "纳税人性质，1-小规模，2-一般纳税人", allowableValues = "1,2")
    private Integer taxType;

    @ApiModelProperty("账期 开始")
    private Integer periodStart;

    @ApiModelProperty("账期 结束")
    private Integer periodEnd;

    @ApiModelProperty("会计")
    private String accountingEmployee;

    /*
     * 未入账/已入账未结账/已入账已结账
     * 逻辑状态：
     * 入账时间和结账时间都为空的：未入账
     * 有入账时间无结账时间：已入账未结账
     * 二者皆有：已入账已结账
     */
    @ApiModelProperty(value = "状态: 1未入账、2已入账未结账、3已入账已结账", allowableValues = "1,2,3")
    private Integer status;

    @ApiModelProperty(value = "状态: 1未入账、2已入账未结账、3已入账已结账，支持1个或多个，空列表的话代表不搜索这个字段")
    private List<Integer> statusList;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
//    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @ApiModelProperty("入账时间 开始，格式 2024-01-08")
    private LocalDate inTimeStart;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
//    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @ApiModelProperty("入账时间 结束，格式 2024-01-08")
    private LocalDate inTimeEnd;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
//    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @ApiModelProperty("结账时间 开始，格式 2024-01-08")
    private LocalDate endTimeStart;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
//    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @ApiModelProperty("结账时间 结束，格式 2024-01-08")
    private LocalDate endTimeEnd;

    /*
     * 材料完整度分2种：
     *
     * 1、材料交接单的完整度，在核验之后才有，状态为：无材料、有缺失、缺但齐、已完整；
     *
     * 2、账期材料完整度，是根据账期下关联的多个材料交接单逻辑计算的，逻辑和状态展示顺序如下：
     *
     *
     *
     * 未提交材料：无材料交接单
     * 待核验：有至少一条交接单是待核验
     * 无材料：所有交接单都是已核验，且都是无材料
     * 有缺失：所有交接单都是已核验，且有一条是有缺失
     * 缺但齐：所有交接单都是已核验，且没有交接单是已缺失，且有一条是缺但齐
     * 已完整：所有交接单都是已核验，且所有交接单都是已完整
     *
     *
     * 会用到材料交接单的完整度的只有：
     *
     * 【材料交接】列表、详情
     *
     * 【月度材料详情】抽屉的材料交接单列表
     *
     *
     *
     * 以下场景里的都是用账期材料完整度：
     *
     * 【月度汇总】列表、月度汇总列表搜索-材料完整度
     *
     * 【入账】列表，入账列表搜索-材料完整度
     *
     * 【服务详情】抽屉-账期明细列表
     */
    //@ApiModelProperty(value = "材料缺失：1-完整、2-缺但齐、3-有缺失，  0-无材料", allowableValues = "0,1,2,3")
    @ApiModelProperty(value = "材料缺失：1-已完整、2-缺但齐、3-有缺失，  -2是未提交材料  4是待核验  5是无材料", allowableValues = "-2,1,2,3,4,5")
    private Integer wholeLevel;

    // add by lym
    @ApiModelProperty("查询部门ID")
    private Long queryDeptId;

    //****************************** 导出附件额外用到的字段
    @ApiModelProperty("附件范围，1-入账附件，2-RPA附件，多个逗号隔开")
    private String downloadFileTypes;


    //****************************** 远程调用额外用到的参数
    private Long deptId;

    private Long userId;

    private String nickName;

    private Integer pageNum;

    private Integer pageSize;

    private String downloadRecordTitle;

    private Long downloadRecordId;
}
