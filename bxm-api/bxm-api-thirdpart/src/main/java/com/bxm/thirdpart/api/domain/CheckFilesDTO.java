package com.bxm.thirdpart.api.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CheckFilesDTO {

    private Boolean statementFileExist;

    private Integer bankReceiptFileExist;

    private Integer bankReceiptFileCount;

    private Integer bankReceiptSuccessCount;

    private Integer bankReceiptFailCount;

    private Integer bankReceiptFileCheckStatus;

    private String uuid;
}
