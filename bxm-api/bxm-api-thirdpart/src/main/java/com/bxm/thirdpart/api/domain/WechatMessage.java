package com.bxm.thirdpart.api.domain;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class WechatMessage {

    private String touser;
    private String msgtype;
    private String agentid;
    private Text text;

    @Getter
    @Setter
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Text {
        private String content;
    }
}
