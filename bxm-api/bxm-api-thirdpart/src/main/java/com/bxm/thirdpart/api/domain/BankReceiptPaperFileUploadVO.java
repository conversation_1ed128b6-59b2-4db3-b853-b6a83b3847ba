package com.bxm.thirdpart.api.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BankReceiptPaperFileUploadVO {

    private String platType;

    private String groupName;

    private String groupId;

    private String taxNumber;

    private String customerName;

    private String creditCode;

    private String operator;

    private Integer period;

    private String statementFileLink;

    private String bankReceiptFileLink;

    private String bankNumber;

    private Long deliverId;

    private Long businessTaskId;

    private Long customerServiceId;

    private Long customerServicePeriodMonthId;

    private Long userId;

    private Long deptId;

    private String batchNo;
}
