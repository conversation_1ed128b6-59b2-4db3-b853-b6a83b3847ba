package com.bxm.thirdpart.api.domain;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class WechatUserDetail {

    private int errcode;
    private String errmsg;
    private String userid;
    private String name;
    private List<Long> department;
    private String position;
    private String mobile;
    private String gender;
    private String email;
    private String avatar;
    private String status;
}
