package com.bxm.thirdpart.api.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class QualityCheckingVO {

    private String groupName;

    private String groupId;

    private String platType;

    private String taxNumber;

    private String customerName;

    private String creditCode;

    private String operator;

    private Integer period;

    private Integer qualityType;

    private Long qualityCheckingResultId;

    private Long qualityCheckingRecordId;

    private Long customerServiceId;

    private Long customerServicePeriodMonthId;

    private Long userId;

    private Long deptId;

    private String batchNo;
}
