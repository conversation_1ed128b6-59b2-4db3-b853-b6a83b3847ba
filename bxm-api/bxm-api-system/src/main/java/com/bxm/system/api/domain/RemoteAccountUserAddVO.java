package com.bxm.system.api.domain;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RemoteAccountUserAddVO {

    @ApiModelProperty("账号")
    private String userName;

    @ApiModelProperty("员工名")
    private String nickName;

    @ApiModelProperty("手机号")
    private String phonenumber;

    @ApiModelProperty("角色id列表")
    private List<Long> roleIds;

    @ApiModelProperty("部门/小组id列表")
    private List<Long> deptIds;

    @ApiModelProperty("是否主管，true-是，false-否")
    private Boolean isLeader;

    private Long deptId;

    private Long userId;
}
