package com.bxm.system.api.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bxm.common.core.annotation.Excel;
import com.bxm.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 员工对象 sys_employee
 * 
 * <AUTHOR>
 * @date 2024-05-08
 */
@Data
@ApiModel("员工对象")
@TableName("sys_employee")
@Accessors(chain = true)
public class SysEmployee extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 员工ID */
    @TableId(value = "employee_id", type = IdType.AUTO)
    @Excel(name = "员工ID")
    @ApiModelProperty(value = "员工ID")
    private Long employeeId;

    /** 用户ID */
    @Excel(name = "用户ID")
    @TableField("user_id")
    @ApiModelProperty(value = "用户ID")
    private Long userId;

    /** 部门ID */
    @Excel(name = "部门ID")
    @TableField("dept_id")
    @ApiModelProperty(value = "部门ID")
    private Long deptId;

    /** 顶级部门ID */
    @Excel(name = "顶级部门ID")
    @TableField("top_dept_id")
    @ApiModelProperty(value = "顶级部门ID")
    private Long topDeptId;

    /** 员工姓名 */
    @Excel(name = "员工姓名")
    @TableField("employee_name")
    @ApiModelProperty(value = "员工姓名")
    private String employeeName;

    /** 员工手机号 */
    @Excel(name = "员工手机号")
    @TableField("employee_mobile")
    @ApiModelProperty(value = "员工手机号")
    private String employeeMobile;

    /** 备注 */
    @Excel(name = "备注")
    @TableField("remark")
    @ApiModelProperty(value = "备注")
    private String remark;

    @TableField("is_leader")
    @ApiModelProperty("是否部门主管，0-否，1-是")
    private Boolean isLeader;

    @TableField(exist = false)
    @ApiModelProperty("角色")
    private List<SysRole> roles;

    @ApiModelProperty("账号")
    @TableField(exist = false)
    private String account;

    @ApiModelProperty("密码")
    @TableField(exist = false)
    private String password;

    @TableField(exist = false)
    private String ancestor;

    @TableField(exist = false)
    private Boolean hasBindWechatUser;
}
