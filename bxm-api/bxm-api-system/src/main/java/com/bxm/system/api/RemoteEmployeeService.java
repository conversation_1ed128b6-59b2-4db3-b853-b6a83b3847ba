package com.bxm.system.api;

import com.bxm.common.core.constant.ServiceNameConstants;
import com.bxm.common.core.domain.R;
import com.bxm.system.api.domain.RemoteSendMessageVO;
import com.bxm.system.api.domain.SysEmployee;
import com.bxm.system.api.domain.UserCompanyDTO;
import com.bxm.system.api.factory.RemoteEmployeeFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 部门服务
 * 
 * <AUTHOR>
 */
@FeignClient(contextId = "remoteEmployeeService", value = ServiceNameConstants.SYSTEM_SERVICE, fallbackFactory = RemoteEmployeeFallbackFactory.class)
public interface RemoteEmployeeService
{
    @GetMapping("/bxmSystem/employee/{employeeId}")
    R<SysEmployee> getEmployeeInfo(@PathVariable("employeeId") Long employeeId);

    @GetMapping("/bxmSystem/employee/getEmployeeByUserIdAndDeptId")
    R<SysEmployee> getEmployeeByUserIdAndDeptId(@RequestParam("userId") Long userId,
                                                @RequestParam("deptId") Long deptId);

    @GetMapping("/bxmSystem/employee/getEmployeeListByDeptId")
    R<List<SysEmployee>> getEmployeeListByDeptId(@RequestParam("deptId") Long deptId);

    @PostMapping("/bxmSystem/employee/getBatchEmployeeByDeptIds")
    R<List<SysEmployee>> getBatchEmployeeByDeptIds(@RequestBody List<Long> deptIds);

    @PostMapping("/bxmSystem/employee/getBatchEmployeeByIds")
    R<List<SysEmployee>> getBatchEmployeeByIds(@RequestBody List<Long> employeeIds);

    @GetMapping("/bxmSystem/userCompany/getByUserIdAndCompanyId")
    R<UserCompanyDTO> getByUserIdAndCompanyId(@RequestParam("userId") Long userId,
                                              @RequestParam("companyId") Long companyId);

    @PostMapping("/bxmSystem/employee/sendTextMessage")
    R sendTextMessage(@RequestBody RemoteSendMessageVO vo);

    @PostMapping("/bxmSystem/employee/getBatchByUserIds")
    R<List<SysEmployee>> getBatchByUserIds(@RequestBody List<Long> userIds);
}
