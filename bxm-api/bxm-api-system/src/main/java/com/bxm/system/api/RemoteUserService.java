package com.bxm.system.api;

import com.bxm.common.core.constant.SecurityConstants;
import com.bxm.common.core.constant.ServiceNameConstants;
import com.bxm.common.core.domain.R;
import com.bxm.common.core.web.domain.CommonIdVO;
import com.bxm.system.api.domain.LoginVO;
import com.bxm.system.api.domain.RemoteUserMessagePageResult;
import com.bxm.system.api.domain.RemoteUserMessageVO;
import com.bxm.system.api.domain.SysUser;
import com.bxm.system.api.factory.RemoteUserFallbackFactory;
import com.bxm.system.api.model.LoginUser;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 用户服务
 *
 * <AUTHOR>
 */
@FeignClient(contextId = "remoteUserService", value = ServiceNameConstants.SYSTEM_SERVICE, fallbackFactory = RemoteUserFallbackFactory.class)
public interface RemoteUserService
{
    /**
     * 通过用户名查询用户信息
     *
     * @param username 用户名
     * @param source 请求来源
     * @return 结果
     */
    @GetMapping("/bxmSystem/user/info/{username}")
    public R<LoginUser> getUserInfo(@PathVariable("username") String username, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 注册用户信息
     *
     * @param sysUser 用户信息
     * @param source 请求来源
     * @return 结果
     */
    @PostMapping("/bxmSystem/user/register")
    public R<Boolean> registerUserInfo(@RequestBody SysUser sysUser, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @PostMapping("/bxmSystem/user/getBatchUserByIds")
    public R<Map<Long, SysUser>> getBatchUserByIds(@RequestBody List<Long> userIds);

    @GetMapping("/bxmSystem/user/getByUserId")
    public R<SysUser> getByUserId(@RequestParam("userId") Long userId, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @GetMapping("/bxmSystem/user/getAllByRole")
    public R<List<SysUser>> getAllByRole(@RequestParam("roleId") Long roleId);

    @GetMapping("/bxmSystem/user/userByUserFromEmployeeAndDept")
    public R<List<SysUser>> userByUserFromEmployeeAndDept(
            @RequestParam("userId") Long userId,
            @RequestParam(value = "deptId", required = false) Long deptId
    );

    @PostMapping("/bxmSystem/userMessage/sendMessage")
    public R sendMessage(@RequestBody List<RemoteUserMessageVO> voList);

    @GetMapping("/bxmSystem/userMessage/getNotReadMessageCountByUserId")
    public R<Map<String, Integer>> getNotReadMessageCountByUserId(@RequestParam("userId") Long userId, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @GetMapping("/bxmSystem/userMessage/getUserMessageListByUserId")
    public R<RemoteUserMessagePageResult> getUserMessageListByUserId(@RequestParam("pageNum") Integer pageNum,
                                                                     @RequestParam("pageSize") Integer pageSize,
                                                                     @RequestParam("userId") Long userId, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);
    @PostMapping("/bxmSystem/userMessage/readMessageInner")
    public R readMessageInner(@RequestBody CommonIdVO vo, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @PostMapping("/bxmSystem/userMessage/allReadInner")
    public R allReadInner(@RequestBody Map<String, Object> params, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @GetMapping("/bxmSystem/user/getUserByNickName")
    R<SysUser> getUserByNickName(@RequestParam("nickName") String nickName, @RequestParam(value = "deptId", required = false) Long deptId, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @GetMapping("/bxmSystem/user/login")
    R<SysUser> login(@RequestBody LoginVO vo, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);
}
