package com.bxm.system.api.domain;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 用户和公司关联对象 sys_user_company
 * 
 * <AUTHOR>
 * @date 2024-05-31
 */
@Data
@ApiModel("用户和公司关联对象")
@Accessors(chain = true)
public class UserCompanyDTO implements Serializable
{
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "用户ID")
    private Long userId;

    @ApiModelProperty(value = "公司ID")
    private Long companyId;

    @ApiModelProperty(value = "员工名称")
    private String employeeName;

    @ApiModelProperty(value = "员工手机号")
    private String employeeMobile;
}
