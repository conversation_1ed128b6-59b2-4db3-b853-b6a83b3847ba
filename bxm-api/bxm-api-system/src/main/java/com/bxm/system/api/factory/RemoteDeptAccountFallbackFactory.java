package com.bxm.system.api.factory;

import com.bxm.common.core.domain.R;
import com.bxm.system.api.RemoteDeptAccountService;
import com.bxm.system.api.domain.CancelBalanceDetailVO;
import com.bxm.system.api.domain.ConfirmBalanceDetailVO;
import com.bxm.system.api.domain.RemoteDeptAccountBalanceDetail;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class RemoteDeptAccountFallbackFactory implements FallbackFactory<RemoteDeptAccountService> {

    @Override
    public RemoteDeptAccountService create(Throwable cause) {
        return new RemoteDeptAccountService() {
            @Override
            public R remoteCreateBalanceDetail(RemoteDeptAccountBalanceDetail detail) {
                return R.fail("创建余额流水失败:" + cause.getMessage());
            }

            @Override
            public R remoteBatchCreateBalanceDetail(List<RemoteDeptAccountBalanceDetail> details) {
                return R.fail("批量创建余额流水失败:" + cause.getMessage());
            }

            @Override
            public R remoteBatchCancelBalanceDetail(CancelBalanceDetailVO vo) {
                return R.fail("批量取消余额流水失败:" + cause.getMessage());
            }

            @Override
            public R remoteBatchConfirmBalanceDetail(ConfirmBalanceDetailVO vo) {
                return R.fail("批量确认余额流水失败:" + cause.getMessage());
            }
        };
    }
}
