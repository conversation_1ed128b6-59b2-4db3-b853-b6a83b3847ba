package com.bxm.system.api;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bxm.common.core.domain.Result;
import com.bxm.system.api.domain.*;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;
import com.bxm.common.core.constant.SecurityConstants;
import com.bxm.common.core.constant.ServiceNameConstants;
import com.bxm.common.core.domain.R;
import com.bxm.system.api.factory.RemoteLogFallbackFactory;

import java.util.List;

/**
 * 日志服务
 * 
 * <AUTHOR>
 */
@FeignClient(contextId = "remoteLogService", value = ServiceNameConstants.SYSTEM_SERVICE, fallbackFactory = RemoteLogFallbackFactory.class)
public interface RemoteLogService
{
    /**
     * 保存系统日志
     *
     * @param sysOperLog 日志实体
     * @param source 请求来源
     * @return 结果
     */
    @PostMapping("/bxmSystem/operlog")
    public R<Boolean> saveLog(@RequestBody SysOperLog sysOperLog, @RequestHeader(SecurityConstants.FROM_SOURCE) String source) throws Exception;

    /**
     * 保存访问记录
     *
     * @param sysLogininfor 访问实体
     * @param source 请求来源
     * @return 结果
     */
    @PostMapping("/bxmSystem/logininfor")
    public R<Boolean> saveLogininfor(@RequestBody SysLogininfor sysLogininfor, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @PostMapping("/bxmSystem/businessLog/addLog")
    public R saveBusinessLog(@RequestBody BusinessLogDTO dto);

    @PostMapping("/bxmSystem/businessLog/batchAddLog")
    public R batchAddLog(@RequestBody List<BusinessLogDTO> dtoList);

    @GetMapping("/bxmSystem/businessLog/getByBusinessIdAndBusinessType")
    public R<IPage<BusinessLogDTO>> getByBusinessIdAndBusinessType(@RequestParam("businessType") Integer businessType,
                                                                   @RequestParam("businessId") Long businessId,
                                                                   @RequestParam("pageNum") Integer pageNum,
                                                                   @RequestParam("pageSize") Integer pageSize);

    @PostMapping("/bxmSystem/businessLog/getByBatchBusinessIdAndBusinessType")
    R<List<BusinessLogDTO>> getByBatchBusinessIdAndBusinessType(@RequestBody BatchGetBusinessLogVO vo);

    @PostMapping("/bxmSystem/businessLog/exportCustomerBusinessLogAndUploadRetry")
    R exportCustomerBusinessLogAndUploadRetry(@RequestBody RemoteCustomerServiceLogSearchVO vo);

    @GetMapping("/bxmSystem/errorCode/getErrorMsgByErrorCode")
    R<String> getErrorMsgByErrorCode(@RequestParam("errorCode") String errorCode, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);
}
