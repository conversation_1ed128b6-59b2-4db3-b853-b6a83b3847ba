package com.bxm.system.api.domain;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RemoteSendMessageVO {

    @ApiModelProperty("发送对象用户id列表")
    private List<Long> userIds;

    @ApiModelProperty("消息内容")
    private String content;
}
