package com.bxm.system.api.domain;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RemoteUserMessageVO {

    @ApiModelProperty("发送者员工id")
    private Long sendEmployeeId;

    @ApiModelProperty(value = "发送者员工姓名")
    private String sendEmployeeName;

    @ApiModelProperty(value = "发送者部门id")
    private Long sendDeptId;

    @ApiModelProperty(value = "发送者部门名称")
    private String sendDeptName;

    @ApiModelProperty(value = "接收者用户id")
    private Long receiveUserId;

    @ApiModelProperty(value = "接收者员工id")
    private Long receiveEmployeeId;

    @ApiModelProperty(value = "接收者员工姓名")
    private String receiveEmployeeName;

    @ApiModelProperty(value = "接收者部门id")
    private Long receiveDeptId;

    @ApiModelProperty(value = "接收者部门名称")
    private String receiveDeptName;

    @ApiModelProperty(value = "消息内容")
    private String content;

    @ApiModelProperty(value = "消息类型,1-主动催办，2-系统触发")
    private Integer messageType;
}
