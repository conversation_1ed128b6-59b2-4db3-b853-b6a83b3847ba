package com.bxm.system.api.factory;

import com.bxm.common.core.domain.R;
import com.bxm.common.core.web.domain.CommonIdVO;
import com.bxm.system.api.RemoteUserService;
import com.bxm.system.api.domain.LoginVO;
import com.bxm.system.api.domain.RemoteUserMessagePageResult;
import com.bxm.system.api.domain.RemoteUserMessageVO;
import com.bxm.system.api.domain.SysUser;
import com.bxm.system.api.model.LoginUser;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

import java.util.List;
import java.util.Map;

/**
 * 用户服务降级处理
 * 
 * <AUTHOR>
 */
@Component
public class RemoteUserFallbackFactory implements FallbackFactory<RemoteUserService>
{
    private static final Logger log = LoggerFactory.getLogger(RemoteUserFallbackFactory.class);

    @Override
    public RemoteUserService create(Throwable throwable)
    {
        log.error("用户服务调用失败:{}", throwable.getMessage());
        return new RemoteUserService()
        {
            @Override
            public R<LoginUser> getUserInfo(String username, String source)
            {
                return R.fail("获取用户失败:" + throwable.getMessage());
            }

            @Override
            public R<Boolean> registerUserInfo(SysUser sysUser, String source)
            {
                return R.fail("注册用户失败:" + throwable.getMessage());
            }

            @Override
            public R<Map<Long, SysUser>> getBatchUserByIds(List<Long> userIds) {
                return R.fail("批量获取用户失败:" + throwable.getMessage());
            }

            @Override
            public R<SysUser> getByUserId(Long userId, String source) {
                return R.fail("根据用户id获取用户失败:" + throwable.getMessage());
            }

            @Override
            public R<List<SysUser>> getAllByRole(Long roleId) {
                return R.fail("获取角色下的所有用户失败:" + throwable.getMessage());
            }

            @Override
            public R<List<SysUser>> userByUserFromEmployeeAndDept(Long userId, Long deptId) {
                return R.fail("获取用户失败userByUserFromEmployeeAndDept:" + throwable.getMessage());
            }

            @Override
            public R sendMessage(List<RemoteUserMessageVO> voList) {
                return R.fail("发送用户消息失败:" + throwable.getMessage());
            }

            @Override
            public R<Map<String, Integer>> getNotReadMessageCountByUserId(Long userId, String source) {
                return R.fail("获取未读消息数量失败:" + throwable.getMessage());
            }

            @Override
            public R<RemoteUserMessagePageResult> getUserMessageListByUserId(Integer pageNum, Integer pageSize, Long userId, String source) {
                return R.fail("获取消息列表失败:" + throwable.getMessage());
            }

            @Override
            public R readMessageInner(CommonIdVO vo, String source) {
                return R.fail("已读消息失败:" + throwable.getMessage());
            }

            @Override
            public R allReadInner(Map<String, Object> params, String source) {
                return R.fail("一键已读消息失败:" + throwable.getMessage());
            }

            @Override
            public R<SysUser> getUserByNickName(String nickName, Long deptId, String source) {
                return R.fail("根据昵称获取用户失败:" + throwable.getMessage());
            }

            @Override
            public R<SysUser> login(LoginVO vo, String source) {
                return R.fail("登录调用失败:" + throwable.getMessage());
            }
        };
    }
}
