package com.bxm.system.api.factory;

import com.bxm.common.core.domain.R;
import com.bxm.system.api.RemoteEmployeeService;
import com.bxm.system.api.domain.RemoteSendMessageVO;
import com.bxm.system.api.domain.SysEmployee;
import com.bxm.system.api.domain.UserCompanyDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 部门服务降级处理
 * 
 * <AUTHOR>
 */
@Component
public class RemoteEmployeeFallbackFactory implements FallbackFactory<RemoteEmployeeService>
{
    private static final Logger log = LoggerFactory.getLogger(RemoteEmployeeFallbackFactory.class);

    @Override
    public RemoteEmployeeService create(Throwable throwable) {
        log.error("员工服务调用失败:{}", throwable.getMessage());
        return new RemoteEmployeeService()
        {
            @Override
            public R<SysEmployee> getEmployeeInfo(Long employeeId) {
                return R.fail("获取员工失败:" + throwable.getMessage());
            }

            @Override
            public R<SysEmployee> getEmployeeByUserIdAndDeptId(Long userId, Long deptId) {
                return R.fail("获取员工失败:" + throwable.getMessage());
            }

            @Override
            public R<List<SysEmployee>> getEmployeeListByDeptId(Long deptId) {
                return R.fail("获取部门下所有员工失败:" + throwable.getMessage());
            }

            @Override
            public R<List<SysEmployee>> getBatchEmployeeByDeptIds(List<Long> deptIds) {
                return R.fail("批量获取部门下员工失败:" + throwable.getMessage());
            }

            @Override
            public R<List<SysEmployee>> getBatchEmployeeByIds(List<Long> employeeIds) {
                return R.fail("批量获取员工失败:" + throwable.getMessage());
            }

            @Override
            public R<UserCompanyDTO> getByUserIdAndCompanyId(Long userId, Long companyId) {
                return R.fail("获取公司员工失败:" + throwable.getMessage());
            }

            @Override
            public R sendTextMessage(RemoteSendMessageVO vo) {
                return R.fail("企微发送文本消息失败:" + throwable.getMessage());
            }

            @Override
            public R<List<SysEmployee>> getBatchByUserIds(List<Long> userIds) {
                return R.fail("批量查询员工信息失败:" + throwable.getMessage());
            }
        };
    }
}
