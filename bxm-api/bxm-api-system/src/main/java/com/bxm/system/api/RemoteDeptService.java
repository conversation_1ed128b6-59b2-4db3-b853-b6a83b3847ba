package com.bxm.system.api;

import com.bxm.common.core.constant.SecurityConstants;
import com.bxm.common.core.constant.ServiceNameConstants;
import com.bxm.common.core.domain.R;
import com.bxm.system.api.domain.*;
import com.bxm.system.api.domain.dept.UserDeptDTO;
import com.bxm.system.api.factory.RemoteDeptFallbackFactory;
import io.swagger.annotations.ApiParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 部门服务
 * 
 * <AUTHOR>
 */
@FeignClient(contextId = "remoteDeptService", value = ServiceNameConstants.SYSTEM_SERVICE, fallbackFactory = RemoteDeptFallbackFactory.class)
public interface RemoteDeptService
{
    @GetMapping("/bxmSystem/dept/deptInfo/{deptId}")
    R<SysDept> getDeptInfo(@PathVariable("deptId") Long deptId);

    @GetMapping("/bxmSystem/dept/userDeptList")
    R<UserDeptDTO> userDeptList(@RequestParam("userId") Long userId,
                                @RequestParam("deptId") Long deptId);

    @GetMapping("/bxmSystem/dept/workOrderUserDeptList")
    R<UserDeptDTO> workOrderUserDeptList(@RequestParam("userId") Long userId,
                                @RequestParam("deptId") Long deptId);

    @PostMapping("/bxmSystem/dept/getDeptByUserDepts")
    R<List<SysDept>> getDeptByUserDepts(UserDeptDTO data);

    @GetMapping("/bxmSystem/dept/getEmployeesBySecondDeptIdAndUserId")
    R<List<SysEmployee>> getEmployeesBySecondDeptIdAndUserId(@RequestParam("deptId") Long deptId,
                                                                         @RequestParam("userId") Long userId);

    @GetMapping("/bxmSystem/dept/getAllDeptBySecondDeptId")
    R<List<SysDept>> getAllDeptBySecondDeptId(@RequestParam("deptId") Long deptId);

    @PostMapping("/bxmSystem/dept/getByDeptIds")
    R<List<SysDept>> getByDeptIds(@RequestBody List<Long> deptIds);

    @GetMapping("/bxmSystem/dept/getAllChildrenIdByTopDeptId")
    R<List<Long>> getAllChildrenIdByTopDeptId(@RequestParam("deptId") Long deptId);

    @GetMapping("/bxmSystem/dept/selectDeptIdsByDeptEmployeeName")
    R<List<Long>> selectDeptIdsByDeptEmployeeName(@RequestParam("deptEmployeeName") String deptEmployeeName);

    @GetMapping("/bxmSystem/dept/getAllDept")
    R<List<SysDept>> getAllDept();

    @GetMapping("/bxmSystem/role/getAllRole")
    R<List<SysRole>> getAllRole();

    @GetMapping("/bxmSystem/dept/getByParentId")
    R<List<SysDept>> getByParentId(@RequestParam("deptId") Long deptId);

    @GetMapping("/bxmSystem/account/remoteAddAccountUser")
    R remoteAddAccountUser(@RequestBody RemoteAccountUserAddVO vo, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @GetMapping("/bxmSystem/calendarConfig/checkDateIsWorkDay")
    R<Boolean> checkDateIsWorkDay(@RequestParam("year") Integer year,
                                  @RequestParam("month") Integer month,
                                  @RequestParam("day") Integer day, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @GetMapping("/bxmSystem/dept/remoteCommonDeptEmployeeTreeSelect")
    R<List<RemoteTreeSelect>> remoteCommonDeptEmployeeTreeSelect(@RequestHeader(value = "deptId", required = false) Long deptId,
                                                                             @RequestParam(value = "deptType", required = false) @ApiParam("部门类型，1-业务部门，2-工厂，不传代表全部") Integer deptType,
                                                                             @RequestParam("selectType") @ApiParam("搜索范围，1-全部范围，2-当前选择的公司下，3-仅当前登陆用户管理权限下，4-当前选择的公司+对应集团的总部") Integer selectType,
                                                                             @RequestParam("showType") @ApiParam("显示方式，1-仅部门，2-部门带员工名称，3-部门带员工数量") Integer showType,
                                                                             @RequestParam(value = "level", required = false) @ApiParam("组织级别，1-集团，2-公司，3-部门，4-小组") Integer level,
                                                                             @RequestParam(value = "businessDeptId", required = false) @ApiParam("业务公司id") Long businessDeptId,
                                                                             @RequestParam(value = "topDeptId", required = false) @ApiParam("集团id") Long topDeptId,
                                                                             @RequestParam(value = "isFilterHead", required = false) @ApiParam("是否过滤总部") Integer isFilterHead, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @GetMapping("/bxmSystem/calendarConfig/getWeekDays")
    R<List<String>> getWeekDays(@RequestHeader(SecurityConstants.FROM_SOURCE) String source);
}
