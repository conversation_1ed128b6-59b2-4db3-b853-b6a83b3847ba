package com.bxm.system.api.domain;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LoginVO {

    @ApiModelProperty("用户名")
    private String username;

    @ApiModelProperty("密码，明文")
    private String password;

    @ApiModelProperty("集团id（一级组织id）")
    private Long deptId;
}
