package com.bxm.customer.api.domain.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.bxm.common.core.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 入账、入账交付对象 c_customer_service_in_account
 *
 * <AUTHOR>
 * @date 2024-07-11
 */
@Data
public class RemoteCustomerServiceInAccount implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @ApiModelProperty(value = "主键ID")
    private Long id;

    /** 状态：1未入账、2已入账未结账、3已入账已结账 */
    @Excel(name = "状态：1未入账、2已入账未结账、3已入账已结账")
    @TableField("status")
    @ApiModelProperty(value = "状态：1未入账、2已入账未结账、3已入账已结账")
    private Integer status;

    private Long customerServicePeriodMonthId;
}
