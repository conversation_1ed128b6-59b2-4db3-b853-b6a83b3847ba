package com.bxm.customer.api.domain.dto;

import com.bxm.common.core.web.domain.CommonFileVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RemoteWorkOrderDetailDTO {

    @ApiModelProperty("工单id")
    private Long id;

    @ApiModelProperty("工单标题")
    private String title;

    @ApiModelProperty("状态，1-待完结，2-已完结，3-超时关闭，4-已完结待确认，5-超时确认")
    private Integer status;

    @ApiModelProperty("状态名称")
    private String statusStr;

    @ApiModelProperty("发起人信息")
    private String initiateInfo;

    @ApiModelProperty("承接人信息")
    private String undertakeInfo;

    @ApiModelProperty("服务id")
    private Long customerServiceId;

    @ApiModelProperty("客户企业名称")
    private String customerName;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("附件")
    private List<CommonFileVO> files;

    @ApiModelProperty("ddl")
    private String ddl;

    @ApiModelProperty("账期开始")
    private String periodStart;

    @ApiModelProperty("账期结束")
    private String periodEnd;

    @ApiModelProperty("当前处理组的全路径，用于跟进时候回显默认的组织树")
    private List<Long> currentDeptPath;

    @ApiModelProperty("当前处理人员工id，用于跟进时候回显默认员工")
    private Long currentEmployeeId;

    @ApiModelProperty("当前处理组类型，1-业务，2-工厂")
    private Integer currentDeptType;

    @ApiModelProperty("发起组的全路径，用于转交时候回显默认的组织树")
    private List<Long> initiateDeptPath;

    @ApiModelProperty("当前发起人员工id，用于转交时候回显默认员工")
    private Long initiateEmployeeId;

    @ApiModelProperty("发起组类型，1-业务，2-工厂")
    private Integer initiateDeptType;

    @ApiModelProperty("当前承接组的全路径，用于转交时候回显默认的组织树")
    private List<Long> undertakeDeptPath;

    @ApiModelProperty("当前承接人员工id，用于跟进时候回显默认员工")
    private Long undertakeEmployeeId;

    @ApiModelProperty("承接组类型，1-业务，2-工厂")
    private Integer undertakeDeptType;

    @ApiModelProperty("账务交付单列表")
    private List<RemoteWorkOrderAccountingCashierDTO> accountingCashierList;

    @ApiModelProperty("是否发起方")
    private Boolean isInitiate;

    @ApiModelProperty("是否显示账务交付单")
    private Boolean isShowAccountingCashierDeliver;

    @ApiModelProperty("当前用户能否跟进")
    private Boolean canFollowUp;

    @ApiModelProperty("当前用户能否转交发起方")
    private Boolean canTransmitInitiate;

    @ApiModelProperty("当前用户能否转交承接方")
    private Boolean canTransmitUndertake;

    @ApiModelProperty("当前用户能否确认")
    private Boolean canConfirm;

    @ApiModelProperty("当前用户能否评论")
    private Boolean canComment;
}
