package com.bxm.customer.api.domain.vo;

import com.bxm.common.core.web.domain.CommonFileVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RemoteAccountingCashierUpdateProfitVO {

    @ApiModelProperty("单个操作的id")
    private Long id;

    @ApiModelProperty("入账交付相关信息（仅交付操作且类型为入账需要传）")
    private RemoteInAccountDeliverVO inAccountDeliverInfo;

    @ApiModelProperty("备注（仅交付和退回操作时需要传）")
    private String remark;

    @ApiModelProperty("附件列表（仅交付和退回操作时需要传）")
    private List<CommonFileVO> files;

    @ApiModelProperty(hidden = true)
    private Long userId;

    @ApiModelProperty(hidden = true)
    private Long deptId;

    @ApiModelProperty(hidden = true)
    private String operName;

    @ApiModelProperty(hidden = true)
    private Boolean isCoverFiles;
}
