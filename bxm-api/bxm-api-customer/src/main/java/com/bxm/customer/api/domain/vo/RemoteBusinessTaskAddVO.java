package com.bxm.customer.api.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/25 18:05
 * happy coding!
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RemoteBusinessTaskAddVO {

    @ApiModelProperty(value = "任务类型，1-账期任务")
    private Integer type;

    @ApiModelProperty(value = "事项，1-银行流水")
    private Integer itemType;

    @ApiModelProperty(value = "监管人")
    private Long adminUserId;

    @ApiModelProperty(value = "账期id")
    private Long periodId;

    @ApiModelProperty(value = "介质")
    private List<Integer> medium;

    private Long userId;

    private Long deptId;

    private String operName;
}
