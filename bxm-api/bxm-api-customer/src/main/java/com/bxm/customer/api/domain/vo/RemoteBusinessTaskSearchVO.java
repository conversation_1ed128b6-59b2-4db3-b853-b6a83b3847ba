package com.bxm.customer.api.domain.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RemoteBusinessTaskSearchVO {

    // 账期id列表
    private List<Long> customerServicePeriodMonthIds;

    // 任务类型 BusinessTaskType.class
    private Integer type;

    // 事项类型 BusinessTaskItemType.class
    private Integer itemType;
}
