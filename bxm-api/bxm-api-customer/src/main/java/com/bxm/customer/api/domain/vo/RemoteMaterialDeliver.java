package com.bxm.customer.api.domain.vo;

import com.bxm.common.core.web.domain.CommonFileVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 材料交接单对象 c_material_deliver
 * 
 * <AUTHOR>
 * @date 2024-12-08
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RemoteMaterialDeliver
{
    @ApiModelProperty(value = "标题")
    private String title;

    @ApiModelProperty("批次号")
    private String batchNo;

    @ApiModelProperty(value = "交接单类型，1-银行流水，2-普通入账，3-凭票入账")
    private Integer materialDeliverType;

    @ApiModelProperty(value = "解析状态，0-队列中，1-解析中，2-解析完成，3-解析失败，4-解析中止")
    private Integer analysisStatus;

    @ApiModelProperty(value = "解析结果，1-正常，2-异常")
    private Integer analysisResult;

    @ApiModelProperty(value = "推送状态，1-待推送，2-已推送")
    private Integer pushStatus;

    @ApiModelProperty(value = "提交小组id")
    private Long commitDeptId;

    @ApiModelProperty(value = "提交用户id")
    private Long commitUserId;

    @ApiModelProperty(value = "提交用户昵称")
    private String commitUserNickName;

    @ApiModelProperty(value = "解析结果")
    private String analysisResultMsg;

    @ApiModelProperty(value = "excel文件")
    private CommonFileVO exclFile;

    @ApiModelProperty(value = "压缩文件")
    private CommonFileVO zipFile;

    @ApiModelProperty(value = "异常文件")
    private CommonFileVO errorFile;

    @ApiModelProperty(hidden = true)
    private Long deptId;

    @ApiModelProperty(hidden = true)
    private String operName;

    @ApiModelProperty("文件清单列表")
    private List<RemoteMaterialDeliverFileInventory> fileInventories;
}
