package com.bxm.customer.api;

import com.bxm.common.core.constant.SecurityConstants;
import com.bxm.common.core.constant.ServiceNameConstants;
import com.bxm.common.core.domain.R;
import com.bxm.customer.api.domain.dto.RemoteBusinessTaskDTO;
import com.bxm.customer.api.domain.vo.RemoteBusinessTaskAddVO;
import com.bxm.customer.api.domain.vo.RemoteBusinessTaskFinishVO;
import com.bxm.customer.api.domain.vo.RemoteBusinessTaskSearchV2VO;
import com.bxm.customer.api.domain.vo.RemoteBusinessTaskSearchVO;
import com.bxm.customer.api.factory.RemoteBusinessTaskFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

import java.util.List;

/**
 * 客户服务
 */
@FeignClient(contextId = "remoteBusinessTaskService", value = ServiceNameConstants.CUSTOMER_SERVICE, fallbackFactory = RemoteBusinessTaskFallbackFactory.class)
public interface RemoteBusinessTaskService {

    @PostMapping("/bxmCustomer/task/getByPeriodIdsAndTypeAndItemType")
    R<List<RemoteBusinessTaskDTO>> getByPeriodIdsAndTypeAndItemType(@RequestBody RemoteBusinessTaskSearchVO vo, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @PostMapping("/bxmCustomer/task/remoteAddSingle")
    R remoteAddSingle(@RequestBody RemoteBusinessTaskAddVO vo, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @PostMapping("/bxmCustomer/task/remoteFinishSingle")
    R remoteFinishSingle(@RequestBody RemoteBusinessTaskFinishVO vo, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @PostMapping("/bxmCustomer/task/getByPeriodAndBankAccountNumber")
    R<List<RemoteBusinessTaskDTO>> getByPeriodAndBankAccountNumber(@RequestBody RemoteBusinessTaskSearchV2VO vo, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);
}
