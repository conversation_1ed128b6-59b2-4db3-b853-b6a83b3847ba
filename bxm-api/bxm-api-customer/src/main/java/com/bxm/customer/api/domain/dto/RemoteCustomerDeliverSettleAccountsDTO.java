package com.bxm.customer.api.domain.dto;

import com.bxm.common.core.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RemoteCustomerDeliverSettleAccountsDTO {

    @ApiModelProperty("客户名")
    @Excel(name = "客户名")
    private String customerCompanyName;

    @ApiModelProperty("信用代码")
    @Excel(name = "信用代码")
    private String creditCode;

    @ApiModelProperty("账期")
    @Excel(name = "账期")
    private String periodStr;

    @Excel(name = "ddl")
    private String ddlStr;

    @ApiModelProperty("账期业务公司")
    @Excel(name = "账期归属业务公司")
    private String businessDeptName;

    @ApiModelProperty("顾问信息")
    @Excel(name = "服务顾问")
    private String advisorDeptInfo;

    @ApiModelProperty("会计信息")
    @Excel(name = "服务会计")
    private String accountingDeptInfo;

    /** 提交人名称 */
    @ApiModelProperty(value = "提交人名称")
    @Excel(name = "提交人")
    private String employeeName;

    @ApiModelProperty("提交时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Excel(name = "提交时间", dateFormat = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @ApiModelProperty("事项备忘")
    @Excel(name = "事项备忘")
    private String mattersNotesDetail;

    @ApiModelProperty("交付要求")
    @Excel(name = "交付要求")
    private String deliverRequire;

    @ApiModelProperty("预认证提报附件数量")
    @Excel(name = "交付单附件")
    private Long createFileCount;

    @ApiModelProperty(value = "本期金额")
    @Excel(name = "本期")
    private String currentPeriodAmount;

    @ApiModelProperty(value = "逾期金额")
    @Excel(name = "滞纳金")
    private String overdueAmount;

    @ApiModelProperty(value = "补缴金额")
    @Excel(name = "往期")
    private String supplementAmount;

    /** 申报金额 */
    @ApiModelProperty(value = "金额/个税（工资薪金）款")
    @Excel(name = "总扣缴金额")
    private String reportAmount;

    @ApiModelProperty("状态名称")
    @Excel(name = "状态")
    private String statusStr;

    @ApiModelProperty("交付备注")
    @Excel(name = "交付备注")
    private String reportRemark;

    @ApiModelProperty("标准附件")
    @Excel(name = "标准附件")
    private String standardFile;

    @ApiModelProperty("交付附件数量")
    @Excel(name = "交付附件数")
    private Long deliverFileCount;

    @ApiModelProperty("最后操作人")
    @Excel(name = "最后操作人")
    private String lastOperName;

    @Excel(name = "最后操作时间")
    private String lastOperTime;

    @ApiModelProperty("最后操作类型")
    @Excel(name = "最后操作")
    private String lastOperType;

    @ApiModelProperty("最后操作备注")
    @Excel(name = "最后操作备注")
    private String lastOperRemark;
}
