package com.bxm.customer.api.domain.dto;

import com.bxm.common.core.web.domain.CommonFileVO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class RemoteCustomerDeliverDTO {

    private Long id;

    private Long customerServicePeriodMonthId;

    private Integer status;

    private Integer deliverType;

    private Boolean hasChanged;

    private Boolean hasPersonChange;

    private List<CommonFileVO> files;

    private String taxCheckType;
}
