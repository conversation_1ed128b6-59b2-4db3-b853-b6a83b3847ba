package com.bxm.customer.api.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RemoteCustomerPeriodBankDTO {

    private Long customerServiceId;

    private String customerName;

    private String bankAccountNumber;

    private String bankName;

    private Long customerServicePeriodMonthId;

    private Integer period;
}
