package com.bxm.customer.api.domain.vo;

import com.bxm.common.core.web.domain.CommonFileVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RemoteWorkOrderCommentVO {

    @ApiModelProperty("工单id")
    private Long id;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("附件")
    private List<CommonFileVO> files;

    private Long userId;
}
