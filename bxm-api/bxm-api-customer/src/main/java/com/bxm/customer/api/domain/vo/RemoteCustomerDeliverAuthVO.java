package com.bxm.customer.api.domain.vo;

import com.bxm.common.core.web.domain.CommonFileVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RemoteCustomerDeliverAuthVO {

    @ApiModelProperty("交付单id")
    private Long id;

    @ApiModelProperty(value = "认证结果，1-正常，2-异常")
    private Integer authStatus;

    @ApiModelProperty("认证数据")
    private RemotePreAuthInfoVO preAuthInfoVO;

    /** 认证备注 */
    @ApiModelProperty(value = "认证备注")
    private String authRemark;

    @ApiModelProperty("认证相关附件")
    private List<CommonFileVO> authFiles;

    @ApiModelProperty("认证提醒")
    private String preAuthRemind;

    private Long employeeId;

    private String employeeName;

    private Long userId;

    private Long deptId;
}
