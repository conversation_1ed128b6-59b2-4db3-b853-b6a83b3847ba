package com.bxm.customer.api.domain.vo;

import com.bxm.common.core.web.domain.CommonFileVO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/1 16:54
 * happy coding!
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RemoteUpdateInAccountV2VO {
    @ApiModelProperty("入账交付id")
    private Long id;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "银行流水录入日期")
    private LocalDate bankPaymentInputTime;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "入账时间")
    private LocalDate inTime;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "结账时间，虽然是通过入账时间和银行流水录入日期，的最大值，计算出来的，但任然需要把这个值传过来，这个值的逻辑就是 在 bankPaymentInputTime 和 inTime 都不为空的情况下，的最大值")
    private LocalDate endTime;

    @ApiModelProperty(value = "主营收入累计")
    private BigDecimal majorIncomeTotal;

    @ApiModelProperty(value = "主营成本累计")
    private BigDecimal majorCostTotal;

    @ApiModelProperty(value = "利润总计")
    private BigDecimal profitTotal;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty("附件")
    private List<CommonFileVO> files;

    // 以下3个参数是远程调用时传的
    private Long userId;

    @ApiModelProperty("是否要处理附件")
    private Boolean dealFiles;

    //下标
    private Integer index;
}
