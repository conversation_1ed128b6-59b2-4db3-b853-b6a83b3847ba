package com.bxm.customer.api.domain.vo;

import com.bxm.common.core.web.domain.CommonFileVO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/29 21:55
 * happy coding!
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RemoteOperateInAccountRpaUpdateVO {
    @ApiModelProperty("id")
    @NotNull
    private Long id;

    @ApiModelProperty(value = "RPA执行结果：1-成功、0-失败")
    @NotNull
    private Integer rpaExeResult;

    @ApiModelProperty(value = "本年累计主营收入")
    private BigDecimal majorIncomeTotal;

    @ApiModelProperty(value = "本年累计主营成本")
    private BigDecimal majorCostTotal;

    @ApiModelProperty(value = "本年累计会计利润")
    private BigDecimal profitTotal;

    @ApiModelProperty(value = "个税申报人数")
    private Integer taxReportCount;

    @ApiModelProperty(value = "本年个税申报工资总额")
    private BigDecimal taxReportSalaryTotal;

    @ApiModelProperty(value = "报表状态是否平衡")
    private String tableStatusBalance;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("RPA查询时间")
    private LocalDateTime rpaSearchTime;

    @ApiModelProperty(value = "RPA备注")
    private String rpaRemark;

    @ApiModelProperty("RPA附件")
    private List<CommonFileVO> rpaFiles;

    // 以下4个参数是远程调用时传的
    private Long userId;

    @ApiModelProperty("是否要处理附件")
    private Boolean dealFiles;

    //下标
    private Integer index;

    private String rpaExeResultStr;
}
