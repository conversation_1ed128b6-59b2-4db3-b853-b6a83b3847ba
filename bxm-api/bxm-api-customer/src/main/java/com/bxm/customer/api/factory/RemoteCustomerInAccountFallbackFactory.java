package com.bxm.customer.api.factory;

import com.bxm.common.core.domain.R;
import com.bxm.customer.api.RemoteCustomerInAccountService;
import com.bxm.customer.api.domain.dto.RemoteCustomerInAccountDTO;
import com.bxm.customer.api.domain.dto.RemoteCustomerServiceInAccount;
import com.bxm.customer.api.domain.vo.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class RemoteCustomerInAccountFallbackFactory implements FallbackFactory<RemoteCustomerInAccountService> {

    private static final Logger log = LoggerFactory.getLogger(RemoteCustomerInAccountService.class);

    @Override
    public RemoteCustomerInAccountService create(Throwable throwable) {
        log.error("客户入账服务调用失败:{}", throwable.getMessage());
        return new RemoteCustomerInAccountService() {
            @Override
            public R<Integer> updateInAccount(Long deptId, RemoteUpdateInAccountV2VO vo) {
                return R.fail("更新入账交付失败:" + throwable.getMessage());
            }

            @Override
            public R<Integer> updateInAccountV3(Long deptId, RemoteUpdateInAccountV3VO vo) {
                return R.fail("更新入账交付V3失败:" + throwable.getMessage());
            }

            @Override
            public R<Integer> inAccountRpaUpdateInner(Long deptId, RemoteOperateInAccountRpaUpdateVO vo) {
                return R.fail("入账交付更新RPA失败:" + throwable.getMessage());
            }

            @Override
            public R<RemoteCustomerServiceInAccount> getByPeriodId(Long customerServicePeriodMonthId) {
                return R.fail("根据账期id查询入账交付失败:" + throwable.getMessage());
            }

            @Override
            public R<List<RemoteCustomerServiceInAccount>> getByPeriodIdList(List<Long> periodIds) {
                return R.fail("根据账期id批量查询入账交付失败:" + throwable.getMessage());
            }

            @Override
            public R<List<RemoteCustomerInAccountDTO>> inAccountList(RemoteCustomerInAccountVO vo) {
                return R.fail("获取入账交付单失败:" + throwable.getMessage());
            }
        };
    }
}
