package com.bxm.customer.api.domain.vo;

import com.bxm.common.core.web.domain.CommonFileVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RemoteCustomerDeliverReportVO {

    @ApiModelProperty("交付单id")
    private Long id;

    @ApiModelProperty(value = "申报结果，1-正常，2-异常")
    private Integer reportStatus;

    @ApiModelProperty(value = "本期金额")
    private BigDecimal currentPeriodAmount;

    @ApiModelProperty(value = "逾期金额")
    private BigDecimal overdueAmount;

    @ApiModelProperty(value = "补缴金额")
    private BigDecimal supplementAmount;

    /** 申报金额 */
    @ApiModelProperty(value = "申报金额")
    private BigDecimal reportAmount;

    @ApiModelProperty(value = "增值税")
    private BigDecimal valueAddTaxAmount;

    /** 附加税 */
    @ApiModelProperty(value = "附加税")
    private BigDecimal additionalTaxAmount;

    /** 印花税 */
    @ApiModelProperty(value = "印花税")
    private BigDecimal stampDutyTaxAmount;

    /** 其他税 */
    @ApiModelProperty(value = "其他税")
    private BigDecimal otherTaxAmount;

    @ApiModelProperty(value = "税款总额")
    private BigDecimal totalTaxAmount;

    /** 申报备注 */
    @ApiModelProperty(value = "申报备注")
    private String reportRemark;

    @ApiModelProperty("申报相关附件")
    private List<CommonFileVO> reportFiles;

    @ApiModelProperty("1-保存，2-提交")
    private Integer saveType;

    @ApiModelProperty("个税申报总额")
    private BigDecimal taxReportTotalAmount;

    private Long employeeId;

    private String employeeName;

    private Long userId;

    private Long deptId;

    private Boolean isRpa;
}
