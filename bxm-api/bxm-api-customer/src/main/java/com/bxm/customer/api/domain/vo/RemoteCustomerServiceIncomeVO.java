package com.bxm.customer.api.domain.vo;

import com.bxm.common.core.annotation.Excel;
import com.bxm.common.core.web.domain.CommonFileVO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RemoteCustomerServiceIncomeVO  {

    private Long id;

    private Long customerServiceId;

    private Integer period;

    private BigDecimal allTicketAmount;

    private BigDecimal allTicketTaxAmount;

    private BigDecimal totalInvoiceAmount;

    private BigDecimal totalInvoiceTaxAmount;

    private BigDecimal noTicketIncomeAmount;

    private LocalDateTime ticketTime;

    private LocalDateTime noTicketIncomeTime;

    private LocalDateTime rpaTime;

    private Integer rpaResult;

    private String remark;

    private List<CommonFileVO> files;

    private Long userId;

    private Long deptId;

    private String operName;
}
