package com.bxm.customer.api.factory;

import com.bxm.common.core.domain.R;
import com.bxm.customer.api.RemoteBusinessTaskService;
import com.bxm.customer.api.domain.dto.RemoteBusinessTaskDTO;
import com.bxm.customer.api.domain.vo.RemoteBusinessTaskAddVO;
import com.bxm.customer.api.domain.vo.RemoteBusinessTaskFinishVO;
import com.bxm.customer.api.domain.vo.RemoteBusinessTaskSearchV2VO;
import com.bxm.customer.api.domain.vo.RemoteBusinessTaskSearchVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class RemoteBusinessTaskFallbackFactory implements FallbackFactory<RemoteBusinessTaskService> {

    private static final Logger log = LoggerFactory.getLogger(RemoteBusinessTaskFallbackFactory.class);

    @Override
    public RemoteBusinessTaskService create(Throwable cause) {
        log.error("任务服务调用失败:{}", cause.getMessage());
        return new RemoteBusinessTaskService() {
            @Override
            public R<List<RemoteBusinessTaskDTO>> getByPeriodIdsAndTypeAndItemType(RemoteBusinessTaskSearchVO vo, String source) {
                return R.fail("远程获取任务数据失败:" + cause.getMessage());
            }

            @Override
            public R remoteAddSingle(RemoteBusinessTaskAddVO vo, String source) {
                return R.fail("远程创建任务失败:" + cause.getMessage());
            }

            @Override
            public R remoteFinishSingle(RemoteBusinessTaskFinishVO vo, String source) {
                return R.fail("远程完成任务失败:" + cause.getMessage());
            }

            @Override
            public R<List<RemoteBusinessTaskDTO>> getByPeriodAndBankAccountNumber(RemoteBusinessTaskSearchV2VO vo, String source) {
                return R.fail("远程获取任务数据失败:" + cause.getMessage());
            }
        };
    }
}
