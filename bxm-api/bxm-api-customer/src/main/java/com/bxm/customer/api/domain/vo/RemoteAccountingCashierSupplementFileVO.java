package com.bxm.customer.api.domain.vo;

import com.bxm.common.core.web.domain.CommonFileVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RemoteAccountingCashierSupplementFileVO {

    @ApiModelProperty("单个操作的id")
    private Long id;

    @ApiModelProperty("补充材料方式，1-追加，2-覆盖，3-无法补充")
    private Integer coverFileType;

    @ApiModelProperty("补充材料")
    private List<CommonFileVO> supplementFiles;

    @ApiModelProperty("回单材料补充方式，1-追加，2-覆盖，3-无法补充")
    private Integer receiptCoverFileType;

    @ApiModelProperty("回单材料")
    private List<CommonFileVO> receiptFiles;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("类型，1-入账，2-流水，3-改账")
    private Integer type;

    @ApiModelProperty(hidden = true)
    private Long userId;

    @ApiModelProperty(hidden = true)
    private Long deptId;

    @ApiModelProperty(hidden = true)
    private String operName;
}
