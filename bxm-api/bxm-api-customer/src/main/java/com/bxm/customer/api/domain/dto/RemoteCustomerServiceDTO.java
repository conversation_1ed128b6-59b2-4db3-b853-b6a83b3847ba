package com.bxm.customer.api.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class RemoteCustomerServiceDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("业务公司名称")
    private String businessDeptName;

    @ApiModelProperty(value = "服务类型")
    private Integer serviceType;

    @ApiModelProperty(value = "客户名")
    private String customerName;

    @ApiModelProperty(value = "客户企业名")
    private String customerCompanyName;

    @ApiModelProperty(value = "信用代码")
    private String creditCode;

    @ApiModelProperty(value = "税号")
    private String taxNumber;

    @ApiModelProperty(value = "服务编号")
    private String serviceNumber;

    @ApiModelProperty(value = "首个账期")
    private String firstAccountPeriod;

    @ApiModelProperty(value = "纳税人性质，1-小规模，2-一般纳税人")
    private Integer taxType;

    @ApiModelProperty("顾问小组名称")
    private String advisorDeptName;

    @ApiModelProperty(value = "会计部门名称")
    private String accountingDeptName;

    private Integer lingjilingbao;

    private Integer yibao;

    private Integer shebao;

    private Integer yougongzi;

    private Integer gongzitiao;
    
    private Integer pingpiaoruchang;
    
    private Integer shiwuhaochuzhang;

    private Integer shougoupiaoOrErshouche;

    private Integer wupiaoshouru;

    private String shuifu;

    private Integer chaezhengshou;

    private Integer jianyijishui;
}
