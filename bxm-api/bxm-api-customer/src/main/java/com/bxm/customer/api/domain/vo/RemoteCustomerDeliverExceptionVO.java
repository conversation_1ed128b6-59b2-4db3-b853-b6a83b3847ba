package com.bxm.customer.api.domain.vo;

import com.bxm.common.core.web.domain.CommonFileVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RemoteCustomerDeliverExceptionVO {

    @ApiModelProperty("交付单id")
    private Long id;

    @ApiModelProperty(value = "异常处理结果，1-解除异常，2-关闭交付")
    private Integer exceptionStatus;

    /** 扣款备异常处理备注注 */
    @ApiModelProperty(value = "异常处理备注")
    private String exceptionRemark;

    @ApiModelProperty("是否有人员变动，0-无变动，1-有变动")
    private Integer hasPersonChange;

    @ApiModelProperty("人员变动信息")
    private String personChangeInfo;

    @ApiModelProperty("无票收入")
    private BigDecimal noTicketIncome;

    @ApiModelProperty("人员变动相关附件")
    private List<CommonFileVO> personChangeFiles;

    @ApiModelProperty("异常处理相关附件")
    private List<CommonFileVO> exceptionFiles;

    private Long employeeId;

    private String employeeName;

    private Long userId;

    private Long deptId;
}
