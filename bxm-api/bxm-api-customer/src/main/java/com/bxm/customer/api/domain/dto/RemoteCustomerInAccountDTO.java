package com.bxm.customer.api.domain.dto;

import com.bxm.common.core.annotation.Excel;
import com.bxm.common.core.web.domain.CommonFileVO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/21 9:55
 * happy coding!
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RemoteCustomerInAccountDTO {
    @ApiModelProperty("入账交付id")
    private Long id;

    @ApiModelProperty(value = "客户服务ID")
    private Long customerServiceId;

    @Excel(name = "客户名")
    @ApiModelProperty(value = "客户名")
    private String customerName;

    @Excel(name = "信用代码")
    @ApiModelProperty(value = "信用代码")
    private String creditCode;

    @ApiModelProperty(value = "月度账期id")
    private Long customerServicePeriodMonthId;

    @ApiModelProperty(value = "账期")
    private Integer period;

    @Excel(name = "账期")
    @ApiModelProperty(value = "账期")
    private String periodStr;

    @Excel(name = "服务会计区域")
    private String customerServiceAccountingTopDeptName;

    @Excel(name = "账期会计区域")
    private String periodAccountingTopDeptName;

    @Excel(name = "会计")
    @ApiModelProperty("会计")
    private String accountingEmployeeNameFull;

    @ApiModelProperty(value = "材料缺失：1-已完整、2-缺但齐、3-有缺失待补充、0无材料")
    private Integer wholeLevel;

//    @Excel(name = "材料缺失")
    @ApiModelProperty(value = "材料缺失 字符串")
    private String wholeLevelStr;

    @ApiModelProperty(value = "交付结果：1-正常、2-无需交付")
    private Integer deliverResult;

//    @Excel(name = "交付结果")
    @ApiModelProperty(value = "交付结果 字符串：1-正常、2-无需交付")
    private String deliverResultStr;

    @ApiModelProperty(value = "入账状态，1未入账、2已入账未结账、3已入账已结账")
    private Integer status;

    @ApiModelProperty(value = "入账状态，字符串")
    @Excel(name = "入账状态")
    private String statusStr;

    @ApiModelProperty(value = "银行流水录入结果，1-正常2-异常3-无需交付4-已开户无流水5-未开户6-银行部分缺")
    private Integer bankPaymentInputResult;

    @ApiModelProperty(value = "银行流水录入结果文案")
    @Excel(name = "银行流水录入结果")
    private String bankPaymentInputResultStr;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @Excel(name = "银行流水录入日期", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(value = "银行流水录入日期")
    private LocalDate bankPaymentInputTime;

    @ApiModelProperty(value = "入账结果，1-正常2-异常3-无需交付4-无账务")
    private Integer inAccountResult;

    @ApiModelProperty(value = "入账结果文案")
    @Excel(name = "入账结果")
    private String inAccountResultStr;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @Excel(name = "入账时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(value = "入账时间")
    private LocalDate inTime;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @Excel(name = "结账时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(value = "结账时间")
    private LocalDate endTime;

    @Excel(name = "入账备注")
    @ApiModelProperty(value = "入账备注")
    private String remark;

    @ApiModelProperty("入账附件")
    private List<CommonFileVO> files;

    @Excel(name = "入账附件")
    @ApiModelProperty("入账附件 数量字符串")
    private Integer filesStr;

    @ApiModelProperty(value = "本年累计主营收入")
    private BigDecimal majorIncomeTotal;

    @Excel(name = "本年累计主营收入")
    private String majorIncomeTotalStr;

    @ApiModelProperty(value = "本年累计主营成本")
    private BigDecimal majorCostTotal;

    @Excel(name = "本年累计主营成本")
    private String majorCostTotalStr;

    @ApiModelProperty(value = "本年累计会计利润")
    private BigDecimal profitTotal;

    @Excel(name = "本年累计会计利润")
    private String profitTotalStr;

    @Excel(name = "个税申报人数")
    @ApiModelProperty(value = "个税申报人数")
    private Integer taxReportCount;

    @ApiModelProperty(value = "本年个税申报工资总额")
    private BigDecimal taxReportSalaryTotal;

    @Excel(name = "本年个税申报工资总额")
    private String taxReportSalaryTotalStr;

    @Excel(name = "报表状态是否平衡")
    @ApiModelProperty(value = "报表状态是否平衡")
    private String tableStatusBalance;

    @ApiModelProperty(value = "RPA执行结果：1-成功、0-失败")
    private Integer rpaExeResult;

    @Excel(name = "RPA执行情况")
    @ApiModelProperty(value = "RPA执行结果 字符串：1-成功、0-失败")
    private String rpaExeResultStr;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "RPA查询时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty("RPA查询时间")
    private LocalDateTime rpaSearchTime;

    @Excel(name = "RPA备注")
    @ApiModelProperty(value = "RPA备注")
    private String rpaRemark;

    @ApiModelProperty("RPA附件")
    private List<CommonFileVO> rpaFiles;

    @Excel(name = "RPA附件")
    @ApiModelProperty("RPA附件 数量字符串")
    private Integer rpaFilesStr;
}
