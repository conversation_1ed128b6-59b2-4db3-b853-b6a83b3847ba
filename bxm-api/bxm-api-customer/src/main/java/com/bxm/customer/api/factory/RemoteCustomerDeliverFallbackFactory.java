package com.bxm.customer.api.factory;

import com.bxm.common.core.domain.R;
import com.bxm.common.core.web.domain.CommonFileVO;
import com.bxm.customer.api.RemoteCustomerDeliverService;
import com.bxm.customer.api.domain.dto.RemoteCustomerDeliverDTO;
import com.bxm.customer.api.domain.dto.RemoteCustomerDeliverMiniDTO;
import com.bxm.customer.api.domain.dto.RemoteCustomerServiceDeliverDTO;
import com.bxm.customer.api.domain.vo.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class RemoteCustomerDeliverFallbackFactory implements FallbackFactory<RemoteCustomerDeliverService> {

    private static final Logger log = LoggerFactory.getLogger(RemoteCustomerDeliverFallbackFactory.class);

    @Override
    public RemoteCustomerDeliverService create(Throwable cause) {
        log.error("交付服务调用失败:{}", cause.getMessage());
        return new RemoteCustomerDeliverService() {
            @Override
            public R<RemoteCustomerDeliverDTO> getByCustomerServiceIdAndPeriodIdAndDeliverType(Long periodId, Long customerServiceId, Integer deliverType) {
                return R.fail("根据服务id+账期id+交付类型查询交付单失败:" + cause.getMessage());
            }

            @Override
            public R<List<RemoteCustomerDeliverDTO>> getByPeriodIdsAndDeliverTypes(RemoteDeliverSearchVO vo) {
                return R.fail("根据账期id+交付类型批量查询交付单失败:" + cause.getMessage());
            }

            @Override
            public R remoteCreate(RemoteCustomerDeliverCreateVO vo) {
                return R.fail("创建交付单失败:" + cause.getMessage());
            }

            @Override
            public R remoteReport(RemoteCustomerDeliverReportVO vo) {
                return R.fail("申报失败:" + cause.getMessage());
            }

            @Override
            public R remoteDeduction(RemoteCustomerDeliverDeductionVO vo) {
                return R.fail("扣款失败:" + cause.getMessage());
            }

            @Override
            public R remoteExceptionDeal(RemoteCustomerDeliverExceptionVO vo) {
                return R.fail("异常解除失败:" + cause.getMessage());
            }

            @Override
            public R remoteSupplement(RemoteCustomerDeliverCreateVO vo) {
                return R.fail("预认证补充失败:" + cause.getMessage());
            }

            @Override
            public R remoteAuth(RemoteCustomerDeliverAuthVO vo) {
                return R.fail("预认证认证失败:" + cause.getMessage());
            }

            @Override
            public R<List<RemoteCustomerServiceDeliverDTO>> remoteList(RemoteCustomerDeliverSearchVO vo) {
                return R.fail("查询交付列表失败:" + cause.getMessage());
            }

            @Override
            public R batchDeliverCheckResult(RemoteCustomerDeliverCheckResultVO vo) {
                return R.fail("批量检查调用失败:" + cause.getMessage());
            }

            @Override
            public R remoteSupplementReportFiles(List<RemoteSupplementReportFilesVO> voList) {
                return R.fail("批量补充附件调用失败:" + cause.getMessage());
            }

            @Override
            public R remoteSupplementFiles(RemoteSupplementReportFilesVO vo, String source) {
                return R.fail("补充附件调用失败:" + cause.getMessage());
            }

            @Override
            public R remoteSupplementAuthFiles(List<RemoteSupplementReportFilesVO> voList) {
                return R.fail("批量补充认证附件调用失败:" + cause.getMessage());
            }

            @Override
            public R<List<RemoteCustomerDeliverMiniDTO>> remoteCustomerDeliverMiniList(Long deptId, Integer deliverType, Integer operType, Integer period) {
                return R.fail("获取交付待处理数据失败:" + cause.getMessage());
            }

            @Override
            public R remoteConfirm(List<RemoteConfirmDeliverVO> voList) {
                return R.fail("批量确认交付单失败:" + cause.getMessage());
            }

            @Override
            public R remoteConfirmSingle(RemoteConfirmDeliverVO vo) {
                return R.fail("单个确认交付单失败:" + cause.getMessage());
            }

            @Override
            public R remotePreAuthConfirm(RemotePreAuthConfirmVO vo) {
                return R.fail("远程确认预认证交付单失败:" + cause.getMessage());
            }

            @Override
            public R remoteReject(RemoteRejectVO vo, String source) {
                return R.fail("远程驳回交付单失败:" + cause.getMessage());
            }

            @Override
            public R remoteOverDeliver(RemoteOverDeliverVO vo, String source) {
                return R.fail("远程完结交付单失败:" + cause.getMessage());
            }

            @Override
            public R remoteDealOverException(RemoteDealOverExceptionDeliverVO vo, String source) {
                return R.fail("远程处理完结异常交付单失败:" + cause.getMessage());
            }

            @Override
            public R remoteReportV2(RemoteReportV2VO vo, String source) {
                return R.fail("远程申报交付单失败:" + cause.getMessage());
            }

            @Override
            public R remoteDeductionV2(RemoteDeductionV2VO vo, String source) {
                return R.fail("远程扣款交付单失败:" + cause.getMessage());
            }

            @Override
            public R remoteSupplementReportFilesV2(RemoteSupplementReportFilesV2VO vo, String source) {
                return R.fail("远程补充申报附件失败:" + cause.getMessage());
            }

            @Override
            public R remoteUpdateTaxReportTotalAmount(RemoteUpdateTaxReportTotalAmountVO vo, String source) {
                return R.fail("远程更新个税申报总额失败:" + cause.getMessage());
            }

            @Override
            public R<List<CommonFileVO>> getPersonTaxDeliverFiles(String taxNumber, Integer period, Long deptId, String source) {
                return R.fail("获取个税交付单附件失败:" + cause.getMessage());
            }
        };
    }
}
