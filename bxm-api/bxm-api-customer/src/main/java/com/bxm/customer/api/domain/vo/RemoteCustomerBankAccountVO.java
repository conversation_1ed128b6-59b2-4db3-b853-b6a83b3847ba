package com.bxm.customer.api.domain.vo;


import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RemoteCustomerBankAccountVO {

    private Long customerServiceId;

    private Long bankId;

    @ApiModelProperty(name = "银行名")
    private String bankName;

    @ApiModelProperty(name = "银行账号")
    private String bankAccountNumber;

    @ApiModelProperty(name = "开户行")
    private String depositName;

    @ApiModelProperty(name = "密码")
    private String password;

    @ApiModelProperty(name = "手机号")
    private String phoneNumber;

    @ApiModelProperty(name = "开户时间")
    private LocalDate accountOpenDate;

    @ApiModelProperty(name = "销户时间")
    private LocalDate accountCloseDate;

    @ApiModelProperty(name = "回单卡账号")
    private String receiptAccountNumber;

    @ApiModelProperty(name = "回单卡托管")
    private Integer receiptStatus;

    @ApiModelProperty(name = "银企直连")
    private Integer bankDirect;

    @ApiModelProperty(name = "备注")
    private String remark;

    @ApiModelProperty(name = "是否税局备案，0-否，1-是")
    private Integer isPutOnTaxRecord;

    private Long deptId;

    private Long userId;

    private String operName;
}
